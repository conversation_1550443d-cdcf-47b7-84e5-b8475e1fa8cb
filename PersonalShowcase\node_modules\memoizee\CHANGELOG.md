# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.4.17](https://github.com/medikoo/memoizee/compare/v0.4.16...v0.4.17) (2024-05-24)

### Bug Fixes

- Fix node version declaration ([15ece26](https://github.com/medikoo/memoizee/commit/15ece26de3e7cc949ebe67b62e21edaa89101480))

### [0.4.16](https://github.com/medikoo/memoizee/compare/v0.4.15...v0.4.16) (2024-05-23)

### Maintenance Improvements

- Prettify ([fe3e492](https://github.com/medikoo/memoizee/commit/fe3e4922a7d6b6e2a4f90b40bfeea10638b3b05b))

### [0.4.15](https://github.com/medikoo/memoizee/compare/v0.4.14...v0.4.15) (2021-01-08)

_Maintainance update_

<a name="0.4.14"></a>

## [0.4.14](https://github.com/medikoo/memoizee/compare/v0.4.13...v0.4.14) (2018-08-13)

### Bug Fixes

- ensure to not force unhandled rejections ([9b416ea](https://github.com/medikoo/memoizee/commit/9b416ea))

<a name="0.4.13"></a>

## [0.4.13](https://github.com/medikoo/memoizee/compare/v0.4.12...v0.4.13) (2018-08-06)

### Features

- **promise:** support cancellation case ([b4b018d](https://github.com/medikoo/memoizee/commit/b4b018d)), closes [#97](https://github.com/medikoo/memoizee/issues/97)

<a name="0.4.12"></a>

## [0.4.12](https://github.com/medikoo/memoizee/compare/v0.4.11...v0.4.12) (2018-02-23)

### Bug Fixes

- **max-age:** unref timeouts to not block processes from exiting ([5bcc5a4](https://github.com/medikoo/memoizee/commit/5bcc5a4)), closes [#25](https://github.com/medikoo/memoizee/issues/25)

<a name="0.4.11"></a>

## [0.4.11](https://github.com/medikoo/memoizee/compare/v0.4.10...v0.4.11) (2017-09-11)

### Bug Fixes

- \_get and \_has internal args handling. ([7cb1c7a](https://github.com/medikoo/memoizee/commit/7cb1c7a)), closes [#88](https://github.com/medikoo/memoizee/issues/88)

<a name="0.4.10"></a>

## [0.4.10](https://github.com/medikoo/memoizee/compare/v0.4.9...v0.4.10) (2017-09-07)

### Bug Fixes

- remove then:finally mode as it can't work right ([5b79698](https://github.com/medikoo/memoizee/commit/5b79698))

<a name="0.4.9"></a>

## [0.4.9](https://github.com/medikoo/memoizee/compare/v0.4.8...v0.4.9) (2017-08-29)

<a name="0.4.8"></a>

## [0.4.8](https://github.com/medikoo/memoizee/compare/v0.4.7...v0.4.8) (2017-08-29)

<a name="0.4.7"></a>

## [0.4.7](https://github.com/medikoo/memoizee/compare/v0.4.6...v0.4.7) (2017-08-29)

### Features

- improve 'promise' mode handling ([759e315](https://github.com/medikoo/memoizee/commit/759e315))
- improve internal promise validation ([d23b94f](https://github.com/medikoo/memoizee/commit/d23b94f))

<a name="0.4.6"></a>

## [0.4.6](https://github.com/medikoo/memoizee/compare/v0.4.5...v0.4.6) (2017-08-24)

- `profileName` option for naming memoizee instances in profile output

<a name="0.4.5"></a>

## [0.4.5](https://github.com/medikoo/memoizee/compare/v0.4.4...v0.4.5) (2017-05-10)

### Bug Fixes

- resolution of extensions with weak handling ([f29a97b](https://github.com/medikoo/memoizee/commit/f29a97b)), closes [#79](https://github.com/medikoo/memoizee/issues/79)

## Old changelog

See `CHANGES`
