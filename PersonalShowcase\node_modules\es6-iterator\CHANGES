For recent changelog see CHANGELOG.md

-----

v2.0.1  --  2017.03.15
* Update dependencies

v2.0.0  --  2015.10.02
* Use es6-symbol at v3

v1.0.0  --  2015.06.23
* Implement support for arguments object
* Drop support for v0.8 node ('^' in package.json dependencies)

v0.1.3  --  2015.02.02
* Update dependencies
* Fix spelling of LICENSE

v0.1.2  --  2014.11.19
* Optimise internal `_next` to not verify internal's list length at all times
  (#2 thanks @RReverser)
* Fix documentation examples
* Configure lint scripts

v0.1.1  --  2014.04.29
* Fix es6-symbol dependency version

v0.1.0  --  2014.04.29
* Assure strictly npm hosted dependencies
* Remove sparse arrays dedicated handling (as per spec)
* Add: isIterable, validIterable and chain (method)
* Remove toArray, it's addressed by Array.from (polyfil can be found in es5-ext/array/from)
* Add break possiblity to 'forOf' via 'doBreak' function argument
* Provide dedicated iterator for array-likes (ArrayIterator) and for strings (StringIterator)
* Provide @@toStringTag symbol
* When available rely on @@iterator symbol
* Remove 32bit integer maximum list length restriction
* Improve Iterator internals
* Update to use latest version of dependencies

v0.0.0  --  2013.10.12
Initial (dev version)
