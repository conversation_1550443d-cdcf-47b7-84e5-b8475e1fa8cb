{"author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "name": "json-diff", "description": "JSON diff", "version": "0.9.0", "homepage": "https://github.com/andreyvit/json-diff", "license": "MIT", "repository": {"url": "**************:andreyvit/json-diff.git"}, "main": "lib/index.js", "bin": "bin/json-diff.js", "scripts": {"fix": "eslint --fix lib", "test": "coffee -c test; mocha test/*.js", "cov": "rm -rf lib-cov; jscoverage lib lib-cov; env JSLIB=lib-cov mocha -R dot && env JSLIB=lib-cov mocha -R html-cov >coverage.html; open coverage.html"}, "dependencies": {"cli-color": "^2.0.0", "difflib": "~0.2.1", "dreamopt": "~0.8.0"}, "devDependencies": {"coffeescript": "^2.6.1", "eslint": "^8.4.1", "eslint-config-standard": "^16.0.3", "mocha": "9.1.3", "jscoverage": "^0.6.0"}, "engines": {"node": "*"}}