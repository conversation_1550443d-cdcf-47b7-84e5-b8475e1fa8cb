// server/index.ts
import express2 from "express";

// server/routes.ts
import { createServer } from "http";

// server/storage.ts
var MockStorage = class {
  users = [];
  contacts = [];
  projects = [
    {
      id: 1,
      title: "Personal Portfolio Website",
      description: "A modern, responsive portfolio website built with React and TypeScript",
      technologies: ["React", "TypeScript", "Tailwind CSS", "Vite"],
      imageUrl: "/api/placeholder/600/400",
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example/portfolio",
      featured: true,
      createdAt: /* @__PURE__ */ new Date()
    },
    {
      id: 2,
      title: "E-commerce Dashboard",
      description: "A comprehensive dashboard for managing e-commerce operations",
      technologies: ["Next.js", "Node.js", "PostgreSQL", "Prisma"],
      imageUrl: "/api/placeholder/600/400",
      liveUrl: "https://example.com/dashboard",
      githubUrl: "https://github.com/example/dashboard",
      featured: true,
      createdAt: /* @__PURE__ */ new Date()
    }
  ];
  skills = [
    { id: 1, category: "Frontend", name: "React", level: 90, icon: "react", color: "#61DAFB" },
    { id: 2, category: "Frontend", name: "TypeScript", level: 85, icon: "typescript", color: "#3178C6" },
    { id: 3, category: "Frontend", name: "Tailwind CSS", level: 80, icon: "tailwind", color: "#06B6D4" },
    { id: 4, category: "Backend", name: "Node.js", level: 85, icon: "nodejs", color: "#339933" },
    { id: 5, category: "Backend", name: "Express", level: 80, icon: "express", color: "#000000" },
    { id: 6, category: "Database", name: "MySQL", level: 75, icon: "mysql", color: "#4479A1" },
    { id: 7, category: "Database", name: "PostgreSQL", level: 70, icon: "postgresql", color: "#336791" }
  ];
  nextId = { users: 1, contacts: 1, projects: 3, skills: 8 };
  async getUser(id) {
    return this.users.find((user) => user.id === id);
  }
  async getUserByUsername(username) {
    return this.users.find((user) => user.username === username);
  }
  async createUser(insertUser) {
    const user = {
      id: this.nextId.users++,
      ...insertUser
    };
    this.users.push(user);
    return user;
  }
  async createContact(insertContact) {
    const contact = {
      id: this.nextId.contacts++,
      ...insertContact,
      createdAt: /* @__PURE__ */ new Date()
    };
    this.contacts.push(contact);
    return contact;
  }
  async getContacts() {
    return [...this.contacts].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  async createProject(insertProject) {
    const project = {
      id: this.nextId.projects++,
      ...insertProject,
      createdAt: /* @__PURE__ */ new Date()
    };
    this.projects.push(project);
    return project;
  }
  async getProjects() {
    return [...this.projects].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  async getFeaturedProjects() {
    return this.projects.filter((p) => p.featured).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  async updateProject(id, updateData) {
    const index = this.projects.findIndex((p) => p.id === id);
    if (index === -1) return void 0;
    this.projects[index] = { ...this.projects[index], ...updateData };
    return this.projects[index];
  }
  async deleteProject(id) {
    const index = this.projects.findIndex((p) => p.id === id);
    if (index === -1) return false;
    this.projects.splice(index, 1);
    return true;
  }
  async createSkill(insertSkill) {
    const skill = {
      id: this.nextId.skills++,
      ...insertSkill
    };
    this.skills.push(skill);
    return skill;
  }
  async getSkills() {
    return [...this.skills];
  }
  async getSkillsByCategory(category) {
    return this.skills.filter((skill) => skill.category === category);
  }
  async updateSkill(id, updateData) {
    const index = this.skills.findIndex((s) => s.id === id);
    if (index === -1) return void 0;
    this.skills[index] = { ...this.skills[index], ...updateData };
    return this.skills[index];
  }
  async deleteSkill(id) {
    const index = this.skills.findIndex((s) => s.id === id);
    if (index === -1) return false;
    this.skills.splice(index, 1);
    return true;
  }
};
var storage = new MockStorage();

// shared/schema.ts
import { mysqlTable, text, int, boolean, timestamp, json } from "drizzle-orm/mysql-core";
import { createInsertSchema } from "drizzle-zod";
var users = mysqlTable("users", {
  id: int("id").primaryKey().autoincrement(),
  username: text("username").notNull().unique(),
  password: text("password").notNull()
});
var contacts = mysqlTable("contacts", {
  id: int("id").primaryKey().autoincrement(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  email: text("email").notNull(),
  subject: text("subject").notNull(),
  message: text("message").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull()
});
var projects = mysqlTable("projects", {
  id: int("id").primaryKey().autoincrement(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  technologies: json("technologies").notNull(),
  imageUrl: text("image_url"),
  liveUrl: text("live_url"),
  githubUrl: text("github_url"),
  featured: boolean("featured").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull()
});
var skills = mysqlTable("skills", {
  id: int("id").primaryKey().autoincrement(),
  category: text("category").notNull(),
  name: text("name").notNull(),
  level: int("level").notNull(),
  icon: text("icon"),
  color: text("color")
});
var insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true
});
var insertContactSchema = createInsertSchema(contacts).pick({
  firstName: true,
  lastName: true,
  email: true,
  subject: true,
  message: true
});
var insertProjectSchema = createInsertSchema(projects).pick({
  title: true,
  description: true,
  technologies: true,
  imageUrl: true,
  liveUrl: true,
  githubUrl: true,
  featured: true
});
var insertSkillSchema = createInsertSchema(skills).pick({
  category: true,
  name: true,
  level: true,
  icon: true,
  color: true
});

// server/routes.ts
async function registerRoutes(app2) {
  app2.post("/api/contacts", async (req, res) => {
    try {
      const validatedData = insertContactSchema.parse(req.body);
      const contact = await storage.createContact(validatedData);
      res.json({ success: true, contact });
    } catch (error) {
      console.error(`Error creating contact: ${error.message}`);
      res.status(400).json({ success: false, error: error.message });
    }
  });
  app2.get("/api/contacts", async (req, res) => {
    try {
      const contacts2 = await storage.getContacts();
      res.json({ success: true, contacts: contacts2 });
    } catch (error) {
      console.error(`Error fetching contacts: ${error.message}`);
      res.status(500).json({ success: false, error: error.message });
    }
  });
  app2.post("/api/projects", async (req, res) => {
    try {
      const validatedData = insertProjectSchema.parse(req.body);
      const project = await storage.createProject(validatedData);
      res.json({ success: true, project });
    } catch (error) {
      console.error(`Error creating project: ${error.message}`);
      res.status(400).json({ success: false, error: error.message });
    }
  });
  app2.get("/api/projects", async (req, res) => {
    try {
      const projects2 = await storage.getProjects();
      res.json({ success: true, projects: projects2 });
    } catch (error) {
      console.error(`Error fetching projects: ${error.message}`);
      res.status(500).json({ success: false, error: error.message });
    }
  });
  app2.get("/api/projects/featured", async (req, res) => {
    try {
      const projects2 = await storage.getFeaturedProjects();
      res.json({ success: true, projects: projects2 });
    } catch (error) {
      console.error(`Error fetching featured projects: ${error.message}`);
      res.status(500).json({ success: false, error: error.message });
    }
  });
  app2.post("/api/skills", async (req, res) => {
    try {
      const validatedData = insertSkillSchema.parse(req.body);
      const skill = await storage.createSkill(validatedData);
      res.json({ success: true, skill });
    } catch (error) {
      console.error(`Error creating skill: ${error.message}`);
      res.status(400).json({ success: false, error: error.message });
    }
  });
  app2.get("/api/skills", async (req, res) => {
    try {
      const category = req.query.category;
      const skills2 = category ? await storage.getSkillsByCategory(category) : await storage.getSkills();
      res.json({ success: true, skills: skills2 });
    } catch (error) {
      console.error(`Error fetching skills: ${error.message}`);
      res.status(500).json({ success: false, error: error.message });
    }
  });
  const httpServer = createServer(app2);
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs from "fs";
import path2 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...process.env.NODE_ENV !== "production" && process.env.REPL_ID !== void 0 ? [
      await import("@replit/vite-plugin-cartographer").then(
        (m) => m.cartographer()
      )
    ] : []
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets")
    }
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"]
    }
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(import.meta.dirname, "public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/index.ts
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path3 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path3.startsWith("/api")) {
      let logLine = `${req.method} ${path3} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = 5e3;
  server.listen(port, "localhost", () => {
    log(`serving on port ${port}`);
  });
})();
