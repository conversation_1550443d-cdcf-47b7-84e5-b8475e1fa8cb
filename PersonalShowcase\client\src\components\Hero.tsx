import { ChevronDown, User } from "lucide-react";

export default function Hero() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <section id="home" className="hero">
      <div className="hero-content">
        <div className="animate-fade-in">
          {/* Professional headshot */}
          <div className="hero-avatar">
            <img
              src="/src/assets/profile.jpg"
              alt="Alex Johnson - Profile Picture"
              style={{
                width: '100%',
                height: '100%',
                borderRadius: '50%',
                objectFit: 'cover'
              }}
              onError={(e) => {
                // Fallback to icon if image fails to load
                e.currentTarget.style.display = 'none';
                e.currentTarget.nextElementSibling.style.display = 'flex';
              }}
            />
            <User
              size={48}
              style={{
                color: 'rgba(255, 255, 255, 0.7)',
                display: 'none'
              }}
            />
          </div>

          <h1 className="hero-title">
            <PERSON><PERSON>  
          </h1>
          <p className="hero-subtitle">
System support & system developer 
          </p>
          <p className="hero-description">
            Passionate about creating beautiful, functional web applications that solve real-world problems.
            Specialized in React, Node.js, and modern web technologies with a focus on user experience.
          </p>

          <div className="hero-buttons">
            <button
              onClick={() => scrollToSection('projects')}
              className="btn btn-primary btn-lg"
            >
              View My Work
            </button>
            <button
              onClick={() => scrollToSection('contact')}
              className="btn btn-outline btn-lg"
            >
              Get In Touch
            </button>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div style={{
        position: 'absolute',
        bottom: '2rem',
        left: '50%',
        transform: 'translateX(-50%)',
        animation: 'bounce 2s infinite'
      }}>
        <ChevronDown size={24} style={{ color: 'rgba(255, 255, 255, 0.7)' }} />
      </div>
    </section>
  );
}
