import { users, contacts, projects, skills, type User, type InsertUser, type Contact, type InsertContact, type Project, type InsertProject, type Skill, type InsertSkill } from "@shared/schema";
// import { db } from "./db";
// import { eq } from "drizzle-orm";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Contact methods
  createContact(contact: InsertContact): Promise<Contact>;
  getContacts(): Promise<Contact[]>;
  
  // Project methods
  createProject(project: InsertProject): Promise<Project>;
  getProjects(): Promise<Project[]>;
  getFeaturedProjects(): Promise<Project[]>;
  updateProject(id: number, project: Partial<InsertProject>): Promise<Project | undefined>;
  deleteProject(id: number): Promise<boolean>;
  
  // Skill methods
  createSkill(skill: InsertSkill): Promise<Skill>;
  getSkills(): Promise<Skill[]>;
  getSkillsByCategory(category: string): Promise<Skill[]>;
  updateSkill(id: number, skill: Partial<InsertSkill>): Promise<Skill | undefined>;
  deleteSkill(id: number): Promise<boolean>;
}

// Commented out DatabaseStorage class since we're using mock storage for now
/*
export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  // Contact methods
  async createContact(insertContact: InsertContact): Promise<Contact> {
    const [contact] = await db
      .insert(contacts)
      .values(insertContact)
      .returning();
    return contact;
  }

  async getContacts(): Promise<Contact[]> {
    return await db.select().from(contacts).orderBy(contacts.createdAt);
  }

  // Project methods
  async createProject(insertProject: InsertProject): Promise<Project> {
    const [project] = await db
      .insert(projects)
      .values(insertProject)
      .returning();
    return project;
  }

  async getProjects(): Promise<Project[]> {
    return await db.select().from(projects).orderBy(projects.createdAt);
  }

  async getFeaturedProjects(): Promise<Project[]> {
    return await db.select().from(projects).where(eq(projects.featured, true)).orderBy(projects.createdAt);
  }

  async updateProject(id: number, updateData: Partial<InsertProject>): Promise<Project | undefined> {
    const [project] = await db
      .update(projects)
      .set(updateData)
      .where(eq(projects.id, id))
      .returning();
    return project || undefined;
  }

  async deleteProject(id: number): Promise<boolean> {
    const result = await db.delete(projects).where(eq(projects.id, id));
    return result.rowCount !== null && result.rowCount > 0;
  }

  // Skill methods
  async createSkill(insertSkill: InsertSkill): Promise<Skill> {
    const [skill] = await db
      .insert(skills)
      .values(insertSkill)
      .returning();
    return skill;
  }

  async getSkills(): Promise<Skill[]> {
    return await db.select().from(skills);
  }

  async getSkillsByCategory(category: string): Promise<Skill[]> {
    return await db.select().from(skills).where(eq(skills.category, category));
  }

  async updateSkill(id: number, updateData: Partial<InsertSkill>): Promise<Skill | undefined> {
    const [skill] = await db
      .update(skills)
      .set(updateData)
      .where(eq(skills.id, id))
      .returning();
    return skill || undefined;
  }

  async deleteSkill(id: number): Promise<boolean> {
    const result = await db.delete(skills).where(eq(skills.id, id));
    return result.rowCount !== null && result.rowCount > 0;
  }
}
*/

// Mock storage implementation for development without database
export class MockStorage implements IStorage {
  private users: User[] = [];
  private contacts: Contact[] = [];
  private projects: Project[] = [
    {
      id: 1,
      title: "Personal Portfolio Website",
      description: "A modern, responsive portfolio website built with React and TypeScript",
      technologies: ["React", "TypeScript", "Tailwind CSS", "Vite"],
      imageUrl: "/api/placeholder/600/400",
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example/portfolio",
      featured: true,
      createdAt: new Date()
    },
    {
      id: 2,
      title: "E-commerce Dashboard",
      description: "A comprehensive dashboard for managing e-commerce operations",
      technologies: ["Next.js", "Node.js", "PostgreSQL", "Prisma"],
      imageUrl: "/api/placeholder/600/400",
      liveUrl: "https://example.com/dashboard",
      githubUrl: "https://github.com/example/dashboard",
      featured: true,
      createdAt: new Date()
    }
  ];
  private skills: Skill[] = [
    { id: 1, category: "Frontend", name: "React", level: 90, icon: "react", color: "#61DAFB" },
    { id: 2, category: "Frontend", name: "TypeScript", level: 85, icon: "typescript", color: "#3178C6" },
    { id: 3, category: "Frontend", name: "Tailwind CSS", level: 80, icon: "tailwind", color: "#06B6D4" },
    { id: 4, category: "Backend", name: "Node.js", level: 85, icon: "nodejs", color: "#339933" },
    { id: 5, category: "Backend", name: "Express", level: 80, icon: "express", color: "#000000" },
    { id: 6, category: "Database", name: "MySQL", level: 75, icon: "mysql", color: "#4479A1" },
    { id: 7, category: "Database", name: "PostgreSQL", level: 70, icon: "postgresql", color: "#336791" }
  ];
  private nextId = { users: 1, contacts: 1, projects: 3, skills: 8 };

  async getUser(id: number): Promise<User | undefined> {
    return this.users.find(user => user.id === id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return this.users.find(user => user.username === username);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const user: User = {
      id: this.nextId.users++,
      ...insertUser
    };
    this.users.push(user);
    return user;
  }

  async createContact(insertContact: InsertContact): Promise<Contact> {
    const contact: Contact = {
      id: this.nextId.contacts++,
      ...insertContact,
      createdAt: new Date()
    };
    this.contacts.push(contact);
    return contact;
  }

  async getContacts(): Promise<Contact[]> {
    return [...this.contacts].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async createProject(insertProject: InsertProject): Promise<Project> {
    const project: Project = {
      id: this.nextId.projects++,
      ...insertProject,
      createdAt: new Date()
    };
    this.projects.push(project);
    return project;
  }

  async getProjects(): Promise<Project[]> {
    return [...this.projects].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async getFeaturedProjects(): Promise<Project[]> {
    return this.projects.filter(p => p.featured).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async updateProject(id: number, updateData: Partial<InsertProject>): Promise<Project | undefined> {
    const index = this.projects.findIndex(p => p.id === id);
    if (index === -1) return undefined;

    this.projects[index] = { ...this.projects[index], ...updateData };
    return this.projects[index];
  }

  async deleteProject(id: number): Promise<boolean> {
    const index = this.projects.findIndex(p => p.id === id);
    if (index === -1) return false;

    this.projects.splice(index, 1);
    return true;
  }

  async createSkill(insertSkill: InsertSkill): Promise<Skill> {
    const skill: Skill = {
      id: this.nextId.skills++,
      ...insertSkill
    };
    this.skills.push(skill);
    return skill;
  }

  async getSkills(): Promise<Skill[]> {
    return [...this.skills];
  }

  async getSkillsByCategory(category: string): Promise<Skill[]> {
    return this.skills.filter(skill => skill.category === category);
  }

  async updateSkill(id: number, updateData: Partial<InsertSkill>): Promise<Skill | undefined> {
    const index = this.skills.findIndex(s => s.id === id);
    if (index === -1) return undefined;

    this.skills[index] = { ...this.skills[index], ...updateData };
    return this.skills[index];
  }

  async deleteSkill(id: number): Promise<boolean> {
    const index = this.skills.findIndex(s => s.id === id);
    if (index === -1) return false;

    this.skills.splice(index, 1);
    return true;
  }
}

// Use mock storage instead of database storage for now
export const storage = new MockStorage();
// export const storage = new DatabaseStorage();
