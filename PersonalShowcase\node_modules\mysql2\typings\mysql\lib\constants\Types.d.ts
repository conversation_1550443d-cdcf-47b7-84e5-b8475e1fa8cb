interface Types {
  0x00: string;
  0x01: string;
  0x02: string;
  0x03: string;
  0x04: string;
  0x05: string;
  0x06: string;
  0x07: string;
  0x08: string;
  0x09: string;
  0x0a: string;
  0x0b: string;
  0x0c: string;
  0x0d: string;
  0x0e: string;
  0x0f: string;
  0x10: string;
  0xf2: string;
  0xf5: string;
  0xf6: string;
  0xf7: string;
  0xf8: string;
  0xf9: string;
  0xfa: string;
  0xfb: string;
  0xfc: string;
  0xfd: string;
  0xfe: string;
  0xff: string;

  DECIMAL: number;
  TINY: number;
  SHORT: number;
  LONG: number;
  FLOAT: number;
  DOUBLE: number;
  NULL: number;
  TIMESTAMP: number;
  LONGLONG: number;
  INT24: number;
  DATE: number;
  TIME: number;
  DATETIME: number;
  YEAR: number;
  NEWDATE: number;
  VARCHAR: number;
  BIT: number;
  VECTOR: number;
  JSON: number;
  NEWDECIMAL: number;
  ENUM: number;
  SET: number;
  TINY_BLOB: number;
  MEDIUM_BLOB: number;
  LONG_BLOB: number;
  BLOB: number;
  VAR_STRING: number;
  STRING: number;
  GEOMETRY: number;
}

/**
 * Constant `Types`.
 *
 * Please note that `Types` can only be accessed from the `mysql` object and not imported directly.
 */
declare const Types: Types;

export { Types };
