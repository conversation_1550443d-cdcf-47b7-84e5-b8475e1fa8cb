"use strict";

var d              = require("d")
  , assign         = require("es5-ext/object/assign")
  , forEach        = require("es5-ext/object/for-each")
  , map            = require("es5-ext/object/map")
  , primitiveSet   = require("es5-ext/object/primitive-set")
  , setPrototypeOf = require("es5-ext/object/set-prototype-of")
  , memoize        = require("memoizee")
  , memoizeMethods = require("memoizee/methods")
  , sgr            = require("./lib/sgr")
  , supportsColor  = require("./lib/supports-color");

var mods           = sgr.mods
  , join           = Array.prototype.join
  , defineProperty = Object.defineProperty
  , max            = Math.max
  , min            = Math.min
  , variantModes   = primitiveSet("_fg", "_bg")
  , xtermMatch     = process.platform === "win32" ? require("./lib/xterm-match") : null;

var getFn;

// Some use cli-color as: console.log(clc.red('Error!'));
// Which is inefficient as on each call it configures new clc object
// with memoization we reuse once created object
var memoized = memoize(function (scope, mod) {
	return defineProperty(getFn(), "_cliColorData", d(assign({}, scope._cliColorData, mod)));
});

var proto = Object.create(
	Function.prototype,
	assign(
		map(mods, function (mod) {
			return d.gs(function () { return memoized(this, mod); });
		}),
		memoizeMethods({
			// xterm (255) color
			xterm: d(function (code) {
				code = isNaN(code) ? 255 : min(max(code, 0), 255);
				return defineProperty(
					getFn(), "_cliColorData",
					d(
						assign({}, this._cliColorData, {
							_fg: [xtermMatch ? xtermMatch[code] : "38;5;" + code, 39]
						})
					)
				);
			}),
			bgXterm: d(function (code) {
				code = isNaN(code) ? 255 : min(max(code, 0), 255);
				return defineProperty(
					getFn(), "_cliColorData",
					d(
						assign({}, this._cliColorData, {
							_bg: [xtermMatch ? xtermMatch[code] + 10 : "48;5;" + code, 49]
						})
					)
				);
			})
		})
	)
);

var getEndRe = memoize(function (code) { return new RegExp("\x1b\\[" + code + "m", "g"); }, {
	primitive: true
});

getFn = function () {
	return setPrototypeOf(
		function self(/* …msg*/) {
			var start = ""
			  , end = ""
			  , msg = join.call(arguments, " ")
			  , conf = self._cliColorData
			  , hasAnsi = sgr.hasCSI(msg);
			forEach(
				conf,
				function (mod, key) {
					end = sgr(mod[1]) + end;
					start += sgr(mod[0]);
					if (hasAnsi) {
						msg = msg.replace(getEndRe(mod[1]), variantModes[key] ? sgr(mod[0]) : "");
					}
				},
				null,
				true
			);
			if (!supportsColor.isColorSupported()) return msg;
			return start + msg + end;
		},
		proto
	);
};

module.exports = Object.defineProperties(getFn(), {
	xtermSupported: d(!xtermMatch),
	_cliColorData: d("", {})
});
