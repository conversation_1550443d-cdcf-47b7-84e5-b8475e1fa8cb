var vh=n=>{throw TypeError(n)};var mu=(n,r,s)=>r.has(n)||vh("Cannot "+s);var P=(n,r,s)=>(mu(n,r,"read from private field"),s?s.call(n):r.get(n)),le=(n,r,s)=>r.has(n)?vh("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(n):r.set(n,s),X=(n,r,s,l)=>(mu(n,r,"write to private field"),l?l.call(n,s):r.set(n,s),s),ye=(n,r,s)=>(mu(n,r,"access private method"),s);var Bi=(n,r,s,l)=>({set _(u){X(n,r,u,s)},get _(){return P(n,r,l)}});function Sv(n,r){for(var s=0;s<r.length;s++){const l=r[s];if(typeof l!="string"&&!Array.isArray(l)){for(const u in l)if(u!=="default"&&!(u in n)){const d=Object.getOwnPropertyDescriptor(l,u);d&&Object.defineProperty(n,u,d.get?d:{enumerable:!0,get:()=>l[u]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))l(u);new MutationObserver(u=>{for(const d of u)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function s(u){const d={};return u.integrity&&(d.integrity=u.integrity),u.referrerPolicy&&(d.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?d.credentials="include":u.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function l(u){if(u.ep)return;u.ep=!0;const d=s(u);fetch(u.href,d)}})();function Pp(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var gu={exports:{}},ms={},yu={exports:{}},xe={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xh;function Cv(){if(xh)return xe;xh=1;var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),f=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),v=Symbol.iterator;function E(R){return R===null||typeof R!="object"?null:(R=v&&R[v]||R["@@iterator"],typeof R=="function"?R:null)}var N={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},j=Object.assign,S={};function k(R,z,fe){this.props=R,this.context=z,this.refs=S,this.updater=fe||N}k.prototype.isReactComponent={},k.prototype.setState=function(R,z){if(typeof R!="object"&&typeof R!="function"&&R!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,R,z,"setState")},k.prototype.forceUpdate=function(R){this.updater.enqueueForceUpdate(this,R,"forceUpdate")};function A(){}A.prototype=k.prototype;function D(R,z,fe){this.props=R,this.context=z,this.refs=S,this.updater=fe||N}var I=D.prototype=new A;I.constructor=D,j(I,k.prototype),I.isPureReactComponent=!0;var F=Array.isArray,H=Object.prototype.hasOwnProperty,q={current:null},B={key:!0,ref:!0,__self:!0,__source:!0};function J(R,z,fe){var he,Se={},Ce=null,Ne=null;if(z!=null)for(he in z.ref!==void 0&&(Ne=z.ref),z.key!==void 0&&(Ce=""+z.key),z)H.call(z,he)&&!B.hasOwnProperty(he)&&(Se[he]=z[he]);var ke=arguments.length-2;if(ke===1)Se.children=fe;else if(1<ke){for(var Oe=Array(ke),et=0;et<ke;et++)Oe[et]=arguments[et+2];Se.children=Oe}if(R&&R.defaultProps)for(he in ke=R.defaultProps,ke)Se[he]===void 0&&(Se[he]=ke[he]);return{$$typeof:n,type:R,key:Ce,ref:Ne,props:Se,_owner:q.current}}function re(R,z){return{$$typeof:n,type:R.type,key:z,ref:R.ref,props:R.props,_owner:R._owner}}function de(R){return typeof R=="object"&&R!==null&&R.$$typeof===n}function pe(R){var z={"=":"=0",":":"=2"};return"$"+R.replace(/[=:]/g,function(fe){return z[fe]})}var ee=/\/+/g;function me(R,z){return typeof R=="object"&&R!==null&&R.key!=null?pe(""+R.key):z.toString(36)}function K(R,z,fe,he,Se){var Ce=typeof R;(Ce==="undefined"||Ce==="boolean")&&(R=null);var Ne=!1;if(R===null)Ne=!0;else switch(Ce){case"string":case"number":Ne=!0;break;case"object":switch(R.$$typeof){case n:case r:Ne=!0}}if(Ne)return Ne=R,Se=Se(Ne),R=he===""?"."+me(Ne,0):he,F(Se)?(fe="",R!=null&&(fe=R.replace(ee,"$&/")+"/"),K(Se,z,fe,"",function(et){return et})):Se!=null&&(de(Se)&&(Se=re(Se,fe+(!Se.key||Ne&&Ne.key===Se.key?"":(""+Se.key).replace(ee,"$&/")+"/")+R)),z.push(Se)),1;if(Ne=0,he=he===""?".":he+":",F(R))for(var ke=0;ke<R.length;ke++){Ce=R[ke];var Oe=he+me(Ce,ke);Ne+=K(Ce,z,fe,Oe,Se)}else if(Oe=E(R),typeof Oe=="function")for(R=Oe.call(R),ke=0;!(Ce=R.next()).done;)Ce=Ce.value,Oe=he+me(Ce,ke++),Ne+=K(Ce,z,fe,Oe,Se);else if(Ce==="object")throw z=String(R),Error("Objects are not valid as a React child (found: "+(z==="[object Object]"?"object with keys {"+Object.keys(R).join(", ")+"}":z)+"). If you meant to render a collection of children, use an array instead.");return Ne}function ve(R,z,fe){if(R==null)return R;var he=[],Se=0;return K(R,he,"","",function(Ce){return z.call(fe,Ce,Se++)}),he}function ue(R){if(R._status===-1){var z=R._result;z=z(),z.then(function(fe){(R._status===0||R._status===-1)&&(R._status=1,R._result=fe)},function(fe){(R._status===0||R._status===-1)&&(R._status=2,R._result=fe)}),R._status===-1&&(R._status=0,R._result=z)}if(R._status===1)return R._result.default;throw R._result}var ae={current:null},L={transition:null},Y={ReactCurrentDispatcher:ae,ReactCurrentBatchConfig:L,ReactCurrentOwner:q};function Q(){throw Error("act(...) is not supported in production builds of React.")}return xe.Children={map:ve,forEach:function(R,z,fe){ve(R,function(){z.apply(this,arguments)},fe)},count:function(R){var z=0;return ve(R,function(){z++}),z},toArray:function(R){return ve(R,function(z){return z})||[]},only:function(R){if(!de(R))throw Error("React.Children.only expected to receive a single React element child.");return R}},xe.Component=k,xe.Fragment=s,xe.Profiler=u,xe.PureComponent=D,xe.StrictMode=l,xe.Suspense=g,xe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Y,xe.act=Q,xe.cloneElement=function(R,z,fe){if(R==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+R+".");var he=j({},R.props),Se=R.key,Ce=R.ref,Ne=R._owner;if(z!=null){if(z.ref!==void 0&&(Ce=z.ref,Ne=q.current),z.key!==void 0&&(Se=""+z.key),R.type&&R.type.defaultProps)var ke=R.type.defaultProps;for(Oe in z)H.call(z,Oe)&&!B.hasOwnProperty(Oe)&&(he[Oe]=z[Oe]===void 0&&ke!==void 0?ke[Oe]:z[Oe])}var Oe=arguments.length-2;if(Oe===1)he.children=fe;else if(1<Oe){ke=Array(Oe);for(var et=0;et<Oe;et++)ke[et]=arguments[et+2];he.children=ke}return{$$typeof:n,type:R.type,key:Se,ref:Ce,props:he,_owner:Ne}},xe.createContext=function(R){return R={$$typeof:f,_currentValue:R,_currentValue2:R,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},R.Provider={$$typeof:d,_context:R},R.Consumer=R},xe.createElement=J,xe.createFactory=function(R){var z=J.bind(null,R);return z.type=R,z},xe.createRef=function(){return{current:null}},xe.forwardRef=function(R){return{$$typeof:p,render:R}},xe.isValidElement=de,xe.lazy=function(R){return{$$typeof:x,_payload:{_status:-1,_result:R},_init:ue}},xe.memo=function(R,z){return{$$typeof:y,type:R,compare:z===void 0?null:z}},xe.startTransition=function(R){var z=L.transition;L.transition={};try{R()}finally{L.transition=z}},xe.unstable_act=Q,xe.useCallback=function(R,z){return ae.current.useCallback(R,z)},xe.useContext=function(R){return ae.current.useContext(R)},xe.useDebugValue=function(){},xe.useDeferredValue=function(R){return ae.current.useDeferredValue(R)},xe.useEffect=function(R,z){return ae.current.useEffect(R,z)},xe.useId=function(){return ae.current.useId()},xe.useImperativeHandle=function(R,z,fe){return ae.current.useImperativeHandle(R,z,fe)},xe.useInsertionEffect=function(R,z){return ae.current.useInsertionEffect(R,z)},xe.useLayoutEffect=function(R,z){return ae.current.useLayoutEffect(R,z)},xe.useMemo=function(R,z){return ae.current.useMemo(R,z)},xe.useReducer=function(R,z,fe){return ae.current.useReducer(R,z,fe)},xe.useRef=function(R){return ae.current.useRef(R)},xe.useState=function(R){return ae.current.useState(R)},xe.useSyncExternalStore=function(R,z,fe){return ae.current.useSyncExternalStore(R,z,fe)},xe.useTransition=function(){return ae.current.useTransition()},xe.version="18.3.1",xe}var wh;function fl(){return wh||(wh=1,yu.exports=Cv()),yu.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sh;function Ev(){if(Sh)return ms;Sh=1;var n=fl(),r=Symbol.for("react.element"),s=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,u=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function f(p,g,y){var x,v={},E=null,N=null;y!==void 0&&(E=""+y),g.key!==void 0&&(E=""+g.key),g.ref!==void 0&&(N=g.ref);for(x in g)l.call(g,x)&&!d.hasOwnProperty(x)&&(v[x]=g[x]);if(p&&p.defaultProps)for(x in g=p.defaultProps,g)v[x]===void 0&&(v[x]=g[x]);return{$$typeof:r,type:p,key:E,ref:N,props:v,_owner:u.current}}return ms.Fragment=s,ms.jsx=f,ms.jsxs=f,ms}var Ch;function kv(){return Ch||(Ch=1,gu.exports=Ev()),gu.exports}var m=kv(),Qi={},vu={exports:{}},mt={},xu={exports:{}},wu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Eh;function bv(){return Eh||(Eh=1,function(n){function r(L,Y){var Q=L.length;L.push(Y);e:for(;0<Q;){var R=Q-1>>>1,z=L[R];if(0<u(z,Y))L[R]=Y,L[Q]=z,Q=R;else break e}}function s(L){return L.length===0?null:L[0]}function l(L){if(L.length===0)return null;var Y=L[0],Q=L.pop();if(Q!==Y){L[0]=Q;e:for(var R=0,z=L.length,fe=z>>>1;R<fe;){var he=2*(R+1)-1,Se=L[he],Ce=he+1,Ne=L[Ce];if(0>u(Se,Q))Ce<z&&0>u(Ne,Se)?(L[R]=Ne,L[Ce]=Q,R=Ce):(L[R]=Se,L[he]=Q,R=he);else if(Ce<z&&0>u(Ne,Q))L[R]=Ne,L[Ce]=Q,R=Ce;else break e}}return Y}function u(L,Y){var Q=L.sortIndex-Y.sortIndex;return Q!==0?Q:L.id-Y.id}if(typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var f=Date,p=f.now();n.unstable_now=function(){return f.now()-p}}var g=[],y=[],x=1,v=null,E=3,N=!1,j=!1,S=!1,k=typeof setTimeout=="function"?setTimeout:null,A=typeof clearTimeout=="function"?clearTimeout:null,D=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function I(L){for(var Y=s(y);Y!==null;){if(Y.callback===null)l(y);else if(Y.startTime<=L)l(y),Y.sortIndex=Y.expirationTime,r(g,Y);else break;Y=s(y)}}function F(L){if(S=!1,I(L),!j)if(s(g)!==null)j=!0,ue(H);else{var Y=s(y);Y!==null&&ae(F,Y.startTime-L)}}function H(L,Y){j=!1,S&&(S=!1,A(J),J=-1),N=!0;var Q=E;try{for(I(Y),v=s(g);v!==null&&(!(v.expirationTime>Y)||L&&!pe());){var R=v.callback;if(typeof R=="function"){v.callback=null,E=v.priorityLevel;var z=R(v.expirationTime<=Y);Y=n.unstable_now(),typeof z=="function"?v.callback=z:v===s(g)&&l(g),I(Y)}else l(g);v=s(g)}if(v!==null)var fe=!0;else{var he=s(y);he!==null&&ae(F,he.startTime-Y),fe=!1}return fe}finally{v=null,E=Q,N=!1}}var q=!1,B=null,J=-1,re=5,de=-1;function pe(){return!(n.unstable_now()-de<re)}function ee(){if(B!==null){var L=n.unstable_now();de=L;var Y=!0;try{Y=B(!0,L)}finally{Y?me():(q=!1,B=null)}}else q=!1}var me;if(typeof D=="function")me=function(){D(ee)};else if(typeof MessageChannel<"u"){var K=new MessageChannel,ve=K.port2;K.port1.onmessage=ee,me=function(){ve.postMessage(null)}}else me=function(){k(ee,0)};function ue(L){B=L,q||(q=!0,me())}function ae(L,Y){J=k(function(){L(n.unstable_now())},Y)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(L){L.callback=null},n.unstable_continueExecution=function(){j||N||(j=!0,ue(H))},n.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):re=0<L?Math.floor(1e3/L):5},n.unstable_getCurrentPriorityLevel=function(){return E},n.unstable_getFirstCallbackNode=function(){return s(g)},n.unstable_next=function(L){switch(E){case 1:case 2:case 3:var Y=3;break;default:Y=E}var Q=E;E=Y;try{return L()}finally{E=Q}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(L,Y){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var Q=E;E=L;try{return Y()}finally{E=Q}},n.unstable_scheduleCallback=function(L,Y,Q){var R=n.unstable_now();switch(typeof Q=="object"&&Q!==null?(Q=Q.delay,Q=typeof Q=="number"&&0<Q?R+Q:R):Q=R,L){case 1:var z=-1;break;case 2:z=250;break;case 5:z=**********;break;case 4:z=1e4;break;default:z=5e3}return z=Q+z,L={id:x++,callback:Y,priorityLevel:L,startTime:Q,expirationTime:z,sortIndex:-1},Q>R?(L.sortIndex=Q,r(y,L),s(g)===null&&L===s(y)&&(S?(A(J),J=-1):S=!0,ae(F,Q-R))):(L.sortIndex=z,r(g,L),j||N||(j=!0,ue(H))),L},n.unstable_shouldYield=pe,n.unstable_wrapCallback=function(L){var Y=E;return function(){var Q=E;E=Y;try{return L.apply(this,arguments)}finally{E=Q}}}}(wu)),wu}var kh;function Pv(){return kh||(kh=1,xu.exports=bv()),xu.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bh;function Nv(){if(bh)return mt;bh=1;var n=fl(),r=Pv();function s(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,o=1;o<arguments.length;o++)t+="&args[]="+encodeURIComponent(arguments[o]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,u={};function d(e,t){f(e,t),f(e+"Capture",t)}function f(e,t){for(u[e]=t,e=0;e<t.length;e++)l.add(t[e])}var p=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),g=Object.prototype.hasOwnProperty,y=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,x={},v={};function E(e){return g.call(v,e)?!0:g.call(x,e)?!1:y.test(e)?v[e]=!0:(x[e]=!0,!1)}function N(e,t,o,i){if(o!==null&&o.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return i?!1:o!==null?!o.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function j(e,t,o,i){if(t===null||typeof t>"u"||N(e,t,o,i))return!0;if(i)return!1;if(o!==null)switch(o.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function S(e,t,o,i,a,c,h){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=i,this.attributeNamespace=a,this.mustUseProperty=o,this.propertyName=e,this.type=t,this.sanitizeURL=c,this.removeEmptyString=h}var k={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){k[e]=new S(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];k[t]=new S(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){k[e]=new S(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){k[e]=new S(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){k[e]=new S(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){k[e]=new S(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){k[e]=new S(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){k[e]=new S(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){k[e]=new S(e,5,!1,e.toLowerCase(),null,!1,!1)});var A=/[\-:]([a-z])/g;function D(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(A,D);k[t]=new S(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(A,D);k[t]=new S(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(A,D);k[t]=new S(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){k[e]=new S(e,1,!1,e.toLowerCase(),null,!1,!1)}),k.xlinkHref=new S("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){k[e]=new S(e,1,!1,e.toLowerCase(),null,!0,!0)});function I(e,t,o,i){var a=k.hasOwnProperty(t)?k[t]:null;(a!==null?a.type!==0:i||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(j(t,o,a,i)&&(o=null),i||a===null?E(t)&&(o===null?e.removeAttribute(t):e.setAttribute(t,""+o)):a.mustUseProperty?e[a.propertyName]=o===null?a.type===3?!1:"":o:(t=a.attributeName,i=a.attributeNamespace,o===null?e.removeAttribute(t):(a=a.type,o=a===3||a===4&&o===!0?"":""+o,i?e.setAttributeNS(i,t,o):e.setAttribute(t,o))))}var F=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,H=Symbol.for("react.element"),q=Symbol.for("react.portal"),B=Symbol.for("react.fragment"),J=Symbol.for("react.strict_mode"),re=Symbol.for("react.profiler"),de=Symbol.for("react.provider"),pe=Symbol.for("react.context"),ee=Symbol.for("react.forward_ref"),me=Symbol.for("react.suspense"),K=Symbol.for("react.suspense_list"),ve=Symbol.for("react.memo"),ue=Symbol.for("react.lazy"),ae=Symbol.for("react.offscreen"),L=Symbol.iterator;function Y(e){return e===null||typeof e!="object"?null:(e=L&&e[L]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Object.assign,R;function z(e){if(R===void 0)try{throw Error()}catch(o){var t=o.stack.trim().match(/\n( *(at )?)/);R=t&&t[1]||""}return`
`+R+e}var fe=!1;function he(e,t){if(!e||fe)return"";fe=!0;var o=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(_){var i=_}Reflect.construct(e,[],t)}else{try{t.call()}catch(_){i=_}e.call(t.prototype)}else{try{throw Error()}catch(_){i=_}e()}}catch(_){if(_&&i&&typeof _.stack=="string"){for(var a=_.stack.split(`
`),c=i.stack.split(`
`),h=a.length-1,w=c.length-1;1<=h&&0<=w&&a[h]!==c[w];)w--;for(;1<=h&&0<=w;h--,w--)if(a[h]!==c[w]){if(h!==1||w!==1)do if(h--,w--,0>w||a[h]!==c[w]){var b=`
`+a[h].replace(" at new "," at ");return e.displayName&&b.includes("<anonymous>")&&(b=b.replace("<anonymous>",e.displayName)),b}while(1<=h&&0<=w);break}}}finally{fe=!1,Error.prepareStackTrace=o}return(e=e?e.displayName||e.name:"")?z(e):""}function Se(e){switch(e.tag){case 5:return z(e.type);case 16:return z("Lazy");case 13:return z("Suspense");case 19:return z("SuspenseList");case 0:case 2:case 15:return e=he(e.type,!1),e;case 11:return e=he(e.type.render,!1),e;case 1:return e=he(e.type,!0),e;default:return""}}function Ce(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case B:return"Fragment";case q:return"Portal";case re:return"Profiler";case J:return"StrictMode";case me:return"Suspense";case K:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case pe:return(e.displayName||"Context")+".Consumer";case de:return(e._context.displayName||"Context")+".Provider";case ee:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ve:return t=e.displayName||null,t!==null?t:Ce(e.type)||"Memo";case ue:t=e._payload,e=e._init;try{return Ce(e(t))}catch{}}return null}function Ne(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ce(t);case 8:return t===J?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function ke(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Oe(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function et(e){var t=Oe(e)?"checked":"value",o=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof o<"u"&&typeof o.get=="function"&&typeof o.set=="function"){var a=o.get,c=o.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(h){i=""+h,c.call(this,h)}}),Object.defineProperty(e,t,{enumerable:o.enumerable}),{getValue:function(){return i},setValue:function(h){i=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function sr(e){e._valueTracker||(e._valueTracker=et(e))}function To(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var o=t.getValue(),i="";return e&&(i=Oe(e)?e.checked?"true":"false":e.value),e=i,e!==o?(t.setValue(e),!0):!1}function qt(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Mr(e,t){var o=t.checked;return Q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:o??e._wrapperState.initialChecked})}function As(e,t){var o=t.defaultValue==null?"":t.defaultValue,i=t.checked!=null?t.checked:t.defaultChecked;o=ke(t.value!=null?t.value:o),e._wrapperState={initialChecked:i,initialValue:o,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ds(e,t){t=t.checked,t!=null&&I(e,"checked",t,!1)}function Ro(e,t){Ds(e,t);var o=ke(t.value),i=t.type;if(o!=null)i==="number"?(o===0&&e.value===""||e.value!=o)&&(e.value=""+o):e.value!==""+o&&(e.value=""+o);else if(i==="submit"||i==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Cn(e,t.type,o):t.hasOwnProperty("defaultValue")&&Cn(e,t.type,ke(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ls(e,t,o){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var i=t.type;if(!(i!=="submit"&&i!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,o||t===e.value||(e.value=t),e.defaultValue=t}o=e.name,o!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,o!==""&&(e.name=o)}function Cn(e,t,o){(t!=="number"||qt(e.ownerDocument)!==e)&&(o==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+o&&(e.defaultValue=""+o))}var Oo=Array.isArray;function _r(e,t,o,i){if(e=e.options,t){t={};for(var a=0;a<o.length;a++)t["$"+o[a]]=!0;for(o=0;o<e.length;o++)a=t.hasOwnProperty("$"+e[o].value),e[o].selected!==a&&(e[o].selected=a),a&&i&&(e[o].defaultSelected=!0)}else{for(o=""+ke(o),t=null,a=0;a<e.length;a++){if(e[a].value===o){e[a].selected=!0,i&&(e[a].defaultSelected=!0);return}t!==null||e[a].disabled||(t=e[a])}t!==null&&(t.selected=!0)}}function bl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(s(91));return Q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Nc(e,t){var o=t.value;if(o==null){if(o=t.children,t=t.defaultValue,o!=null){if(t!=null)throw Error(s(92));if(Oo(o)){if(1<o.length)throw Error(s(93));o=o[0]}t=o}t==null&&(t=""),o=t}e._wrapperState={initialValue:ke(o)}}function Tc(e,t){var o=ke(t.value),i=ke(t.defaultValue);o!=null&&(o=""+o,o!==e.value&&(e.value=o),t.defaultValue==null&&e.defaultValue!==o&&(e.defaultValue=o)),i!=null&&(e.defaultValue=""+i)}function Rc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Oc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Pl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Oc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Is,jc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,o,i,a){MSApp.execUnsafeLocalFunction(function(){return e(t,o,i,a)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Is=Is||document.createElement("div"),Is.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Is.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function jo(e,t){if(t){var o=e.firstChild;if(o&&o===e.lastChild&&o.nodeType===3){o.nodeValue=t;return}}e.textContent=t}var Mo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},bg=["Webkit","ms","Moz","O"];Object.keys(Mo).forEach(function(e){bg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Mo[t]=Mo[e]})});function Mc(e,t,o){return t==null||typeof t=="boolean"||t===""?"":o||typeof t!="number"||t===0||Mo.hasOwnProperty(e)&&Mo[e]?(""+t).trim():t+"px"}function _c(e,t){e=e.style;for(var o in t)if(t.hasOwnProperty(o)){var i=o.indexOf("--")===0,a=Mc(o,t[o],i);o==="float"&&(o="cssFloat"),i?e.setProperty(o,a):e[o]=a}}var Pg=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Nl(e,t){if(t){if(Pg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(s(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(s(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(s(61))}if(t.style!=null&&typeof t.style!="object")throw Error(s(62))}}function Tl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Rl=null;function Ol(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var jl=null,Ar=null,Dr=null;function Ac(e){if(e=es(e)){if(typeof jl!="function")throw Error(s(280));var t=e.stateNode;t&&(t=ii(t),jl(e.stateNode,e.type,t))}}function Dc(e){Ar?Dr?Dr.push(e):Dr=[e]:Ar=e}function Lc(){if(Ar){var e=Ar,t=Dr;if(Dr=Ar=null,Ac(e),t)for(e=0;e<t.length;e++)Ac(t[e])}}function Ic(e,t){return e(t)}function Fc(){}var Ml=!1;function zc(e,t,o){if(Ml)return e(t,o);Ml=!0;try{return Ic(e,t,o)}finally{Ml=!1,(Ar!==null||Dr!==null)&&(Fc(),Lc())}}function _o(e,t){var o=e.stateNode;if(o===null)return null;var i=ii(o);if(i===null)return null;o=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(o&&typeof o!="function")throw Error(s(231,t,typeof o));return o}var _l=!1;if(p)try{var Ao={};Object.defineProperty(Ao,"passive",{get:function(){_l=!0}}),window.addEventListener("test",Ao,Ao),window.removeEventListener("test",Ao,Ao)}catch{_l=!1}function Ng(e,t,o,i,a,c,h,w,b){var _=Array.prototype.slice.call(arguments,3);try{t.apply(o,_)}catch(U){this.onError(U)}}var Do=!1,Fs=null,zs=!1,Al=null,Tg={onError:function(e){Do=!0,Fs=e}};function Rg(e,t,o,i,a,c,h,w,b){Do=!1,Fs=null,Ng.apply(Tg,arguments)}function Og(e,t,o,i,a,c,h,w,b){if(Rg.apply(this,arguments),Do){if(Do){var _=Fs;Do=!1,Fs=null}else throw Error(s(198));zs||(zs=!0,Al=_)}}function ir(e){var t=e,o=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(o=t.return),e=t.return;while(e)}return t.tag===3?o:null}function Wc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Uc(e){if(ir(e)!==e)throw Error(s(188))}function jg(e){var t=e.alternate;if(!t){if(t=ir(e),t===null)throw Error(s(188));return t!==e?null:e}for(var o=e,i=t;;){var a=o.return;if(a===null)break;var c=a.alternate;if(c===null){if(i=a.return,i!==null){o=i;continue}break}if(a.child===c.child){for(c=a.child;c;){if(c===o)return Uc(a),e;if(c===i)return Uc(a),t;c=c.sibling}throw Error(s(188))}if(o.return!==i.return)o=a,i=c;else{for(var h=!1,w=a.child;w;){if(w===o){h=!0,o=a,i=c;break}if(w===i){h=!0,i=a,o=c;break}w=w.sibling}if(!h){for(w=c.child;w;){if(w===o){h=!0,o=c,i=a;break}if(w===i){h=!0,i=c,o=a;break}w=w.sibling}if(!h)throw Error(s(189))}}if(o.alternate!==i)throw Error(s(190))}if(o.tag!==3)throw Error(s(188));return o.stateNode.current===o?e:t}function $c(e){return e=jg(e),e!==null?Hc(e):null}function Hc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Hc(e);if(t!==null)return t;e=e.sibling}return null}var Vc=r.unstable_scheduleCallback,Bc=r.unstable_cancelCallback,Mg=r.unstable_shouldYield,_g=r.unstable_requestPaint,We=r.unstable_now,Ag=r.unstable_getCurrentPriorityLevel,Dl=r.unstable_ImmediatePriority,Qc=r.unstable_UserBlockingPriority,Ws=r.unstable_NormalPriority,Dg=r.unstable_LowPriority,qc=r.unstable_IdlePriority,Us=null,Yt=null;function Lg(e){if(Yt&&typeof Yt.onCommitFiberRoot=="function")try{Yt.onCommitFiberRoot(Us,e,void 0,(e.current.flags&128)===128)}catch{}}var At=Math.clz32?Math.clz32:zg,Ig=Math.log,Fg=Math.LN2;function zg(e){return e>>>=0,e===0?32:31-(Ig(e)/Fg|0)|0}var $s=64,Hs=4194304;function Lo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Vs(e,t){var o=e.pendingLanes;if(o===0)return 0;var i=0,a=e.suspendedLanes,c=e.pingedLanes,h=o&268435455;if(h!==0){var w=h&~a;w!==0?i=Lo(w):(c&=h,c!==0&&(i=Lo(c)))}else h=o&~a,h!==0?i=Lo(h):c!==0&&(i=Lo(c));if(i===0)return 0;if(t!==0&&t!==i&&(t&a)===0&&(a=i&-i,c=t&-t,a>=c||a===16&&(c&4194240)!==0))return t;if((i&4)!==0&&(i|=o&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=i;0<t;)o=31-At(t),a=1<<o,i|=e[o],t&=~a;return i}function Wg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ug(e,t){for(var o=e.suspendedLanes,i=e.pingedLanes,a=e.expirationTimes,c=e.pendingLanes;0<c;){var h=31-At(c),w=1<<h,b=a[h];b===-1?((w&o)===0||(w&i)!==0)&&(a[h]=Wg(w,t)):b<=t&&(e.expiredLanes|=w),c&=~w}}function Ll(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Yc(){var e=$s;return $s<<=1,($s&4194240)===0&&($s=64),e}function Il(e){for(var t=[],o=0;31>o;o++)t.push(e);return t}function Io(e,t,o){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-At(t),e[t]=o}function $g(e,t){var o=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var i=e.eventTimes;for(e=e.expirationTimes;0<o;){var a=31-At(o),c=1<<a;t[a]=0,i[a]=-1,e[a]=-1,o&=~c}}function Fl(e,t){var o=e.entangledLanes|=t;for(e=e.entanglements;o;){var i=31-At(o),a=1<<i;a&t|e[i]&t&&(e[i]|=t),o&=~a}}var Te=0;function Kc(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Gc,zl,Xc,Jc,Zc,Wl=!1,Bs=[],En=null,kn=null,bn=null,Fo=new Map,zo=new Map,Pn=[],Hg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ed(e,t){switch(e){case"focusin":case"focusout":En=null;break;case"dragenter":case"dragleave":kn=null;break;case"mouseover":case"mouseout":bn=null;break;case"pointerover":case"pointerout":Fo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":zo.delete(t.pointerId)}}function Wo(e,t,o,i,a,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:o,eventSystemFlags:i,nativeEvent:c,targetContainers:[a]},t!==null&&(t=es(t),t!==null&&zl(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,a!==null&&t.indexOf(a)===-1&&t.push(a),e)}function Vg(e,t,o,i,a){switch(t){case"focusin":return En=Wo(En,e,t,o,i,a),!0;case"dragenter":return kn=Wo(kn,e,t,o,i,a),!0;case"mouseover":return bn=Wo(bn,e,t,o,i,a),!0;case"pointerover":var c=a.pointerId;return Fo.set(c,Wo(Fo.get(c)||null,e,t,o,i,a)),!0;case"gotpointercapture":return c=a.pointerId,zo.set(c,Wo(zo.get(c)||null,e,t,o,i,a)),!0}return!1}function td(e){var t=lr(e.target);if(t!==null){var o=ir(t);if(o!==null){if(t=o.tag,t===13){if(t=Wc(o),t!==null){e.blockedOn=t,Zc(e.priority,function(){Xc(o)});return}}else if(t===3&&o.stateNode.current.memoizedState.isDehydrated){e.blockedOn=o.tag===3?o.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Qs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var o=$l(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(o===null){o=e.nativeEvent;var i=new o.constructor(o.type,o);Rl=i,o.target.dispatchEvent(i),Rl=null}else return t=es(o),t!==null&&zl(t),e.blockedOn=o,!1;t.shift()}return!0}function nd(e,t,o){Qs(e)&&o.delete(t)}function Bg(){Wl=!1,En!==null&&Qs(En)&&(En=null),kn!==null&&Qs(kn)&&(kn=null),bn!==null&&Qs(bn)&&(bn=null),Fo.forEach(nd),zo.forEach(nd)}function Uo(e,t){e.blockedOn===t&&(e.blockedOn=null,Wl||(Wl=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Bg)))}function $o(e){function t(a){return Uo(a,e)}if(0<Bs.length){Uo(Bs[0],e);for(var o=1;o<Bs.length;o++){var i=Bs[o];i.blockedOn===e&&(i.blockedOn=null)}}for(En!==null&&Uo(En,e),kn!==null&&Uo(kn,e),bn!==null&&Uo(bn,e),Fo.forEach(t),zo.forEach(t),o=0;o<Pn.length;o++)i=Pn[o],i.blockedOn===e&&(i.blockedOn=null);for(;0<Pn.length&&(o=Pn[0],o.blockedOn===null);)td(o),o.blockedOn===null&&Pn.shift()}var Lr=F.ReactCurrentBatchConfig,qs=!0;function Qg(e,t,o,i){var a=Te,c=Lr.transition;Lr.transition=null;try{Te=1,Ul(e,t,o,i)}finally{Te=a,Lr.transition=c}}function qg(e,t,o,i){var a=Te,c=Lr.transition;Lr.transition=null;try{Te=4,Ul(e,t,o,i)}finally{Te=a,Lr.transition=c}}function Ul(e,t,o,i){if(qs){var a=$l(e,t,o,i);if(a===null)sa(e,t,i,Ys,o),ed(e,i);else if(Vg(a,e,t,o,i))i.stopPropagation();else if(ed(e,i),t&4&&-1<Hg.indexOf(e)){for(;a!==null;){var c=es(a);if(c!==null&&Gc(c),c=$l(e,t,o,i),c===null&&sa(e,t,i,Ys,o),c===a)break;a=c}a!==null&&i.stopPropagation()}else sa(e,t,i,null,o)}}var Ys=null;function $l(e,t,o,i){if(Ys=null,e=Ol(i),e=lr(e),e!==null)if(t=ir(e),t===null)e=null;else if(o=t.tag,o===13){if(e=Wc(t),e!==null)return e;e=null}else if(o===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ys=e,null}function rd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ag()){case Dl:return 1;case Qc:return 4;case Ws:case Dg:return 16;case qc:return 536870912;default:return 16}default:return 16}}var Nn=null,Hl=null,Ks=null;function od(){if(Ks)return Ks;var e,t=Hl,o=t.length,i,a="value"in Nn?Nn.value:Nn.textContent,c=a.length;for(e=0;e<o&&t[e]===a[e];e++);var h=o-e;for(i=1;i<=h&&t[o-i]===a[c-i];i++);return Ks=a.slice(e,1<i?1-i:void 0)}function Gs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Xs(){return!0}function sd(){return!1}function xt(e){function t(o,i,a,c,h){this._reactName=o,this._targetInst=a,this.type=i,this.nativeEvent=c,this.target=h,this.currentTarget=null;for(var w in e)e.hasOwnProperty(w)&&(o=e[w],this[w]=o?o(c):c[w]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Xs:sd,this.isPropagationStopped=sd,this}return Q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var o=this.nativeEvent;o&&(o.preventDefault?o.preventDefault():typeof o.returnValue!="unknown"&&(o.returnValue=!1),this.isDefaultPrevented=Xs)},stopPropagation:function(){var o=this.nativeEvent;o&&(o.stopPropagation?o.stopPropagation():typeof o.cancelBubble!="unknown"&&(o.cancelBubble=!0),this.isPropagationStopped=Xs)},persist:function(){},isPersistent:Xs}),t}var Ir={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Vl=xt(Ir),Ho=Q({},Ir,{view:0,detail:0}),Yg=xt(Ho),Bl,Ql,Vo,Js=Q({},Ho,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Yl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Vo&&(Vo&&e.type==="mousemove"?(Bl=e.screenX-Vo.screenX,Ql=e.screenY-Vo.screenY):Ql=Bl=0,Vo=e),Bl)},movementY:function(e){return"movementY"in e?e.movementY:Ql}}),id=xt(Js),Kg=Q({},Js,{dataTransfer:0}),Gg=xt(Kg),Xg=Q({},Ho,{relatedTarget:0}),ql=xt(Xg),Jg=Q({},Ir,{animationName:0,elapsedTime:0,pseudoElement:0}),Zg=xt(Jg),ey=Q({},Ir,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ty=xt(ey),ny=Q({},Ir,{data:0}),ld=xt(ny),ry={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},oy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},sy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function iy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=sy[e])?!!t[e]:!1}function Yl(){return iy}var ly=Q({},Ho,{key:function(e){if(e.key){var t=ry[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Gs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?oy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Yl,charCode:function(e){return e.type==="keypress"?Gs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Gs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ay=xt(ly),uy=Q({},Js,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ad=xt(uy),cy=Q({},Ho,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Yl}),dy=xt(cy),fy=Q({},Ir,{propertyName:0,elapsedTime:0,pseudoElement:0}),hy=xt(fy),py=Q({},Js,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),my=xt(py),gy=[9,13,27,32],Kl=p&&"CompositionEvent"in window,Bo=null;p&&"documentMode"in document&&(Bo=document.documentMode);var yy=p&&"TextEvent"in window&&!Bo,ud=p&&(!Kl||Bo&&8<Bo&&11>=Bo),cd=" ",dd=!1;function fd(e,t){switch(e){case"keyup":return gy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function hd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Fr=!1;function vy(e,t){switch(e){case"compositionend":return hd(t);case"keypress":return t.which!==32?null:(dd=!0,cd);case"textInput":return e=t.data,e===cd&&dd?null:e;default:return null}}function xy(e,t){if(Fr)return e==="compositionend"||!Kl&&fd(e,t)?(e=od(),Ks=Hl=Nn=null,Fr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ud&&t.locale!=="ko"?null:t.data;default:return null}}var wy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function pd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!wy[e.type]:t==="textarea"}function md(e,t,o,i){Dc(i),t=ri(t,"onChange"),0<t.length&&(o=new Vl("onChange","change",null,o,i),e.push({event:o,listeners:t}))}var Qo=null,qo=null;function Sy(e){_d(e,0)}function Zs(e){var t=Hr(e);if(To(t))return e}function Cy(e,t){if(e==="change")return t}var gd=!1;if(p){var Gl;if(p){var Xl="oninput"in document;if(!Xl){var yd=document.createElement("div");yd.setAttribute("oninput","return;"),Xl=typeof yd.oninput=="function"}Gl=Xl}else Gl=!1;gd=Gl&&(!document.documentMode||9<document.documentMode)}function vd(){Qo&&(Qo.detachEvent("onpropertychange",xd),qo=Qo=null)}function xd(e){if(e.propertyName==="value"&&Zs(qo)){var t=[];md(t,qo,e,Ol(e)),zc(Sy,t)}}function Ey(e,t,o){e==="focusin"?(vd(),Qo=t,qo=o,Qo.attachEvent("onpropertychange",xd)):e==="focusout"&&vd()}function ky(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Zs(qo)}function by(e,t){if(e==="click")return Zs(t)}function Py(e,t){if(e==="input"||e==="change")return Zs(t)}function Ny(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Dt=typeof Object.is=="function"?Object.is:Ny;function Yo(e,t){if(Dt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var o=Object.keys(e),i=Object.keys(t);if(o.length!==i.length)return!1;for(i=0;i<o.length;i++){var a=o[i];if(!g.call(t,a)||!Dt(e[a],t[a]))return!1}return!0}function wd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Sd(e,t){var o=wd(e);e=0;for(var i;o;){if(o.nodeType===3){if(i=e+o.textContent.length,e<=t&&i>=t)return{node:o,offset:t-e};e=i}e:{for(;o;){if(o.nextSibling){o=o.nextSibling;break e}o=o.parentNode}o=void 0}o=wd(o)}}function Cd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Cd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ed(){for(var e=window,t=qt();t instanceof e.HTMLIFrameElement;){try{var o=typeof t.contentWindow.location.href=="string"}catch{o=!1}if(o)e=t.contentWindow;else break;t=qt(e.document)}return t}function Jl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Ty(e){var t=Ed(),o=e.focusedElem,i=e.selectionRange;if(t!==o&&o&&o.ownerDocument&&Cd(o.ownerDocument.documentElement,o)){if(i!==null&&Jl(o)){if(t=i.start,e=i.end,e===void 0&&(e=t),"selectionStart"in o)o.selectionStart=t,o.selectionEnd=Math.min(e,o.value.length);else if(e=(t=o.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var a=o.textContent.length,c=Math.min(i.start,a);i=i.end===void 0?c:Math.min(i.end,a),!e.extend&&c>i&&(a=i,i=c,c=a),a=Sd(o,c);var h=Sd(o,i);a&&h&&(e.rangeCount!==1||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==h.node||e.focusOffset!==h.offset)&&(t=t.createRange(),t.setStart(a.node,a.offset),e.removeAllRanges(),c>i?(e.addRange(t),e.extend(h.node,h.offset)):(t.setEnd(h.node,h.offset),e.addRange(t)))}}for(t=[],e=o;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<t.length;o++)e=t[o],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Ry=p&&"documentMode"in document&&11>=document.documentMode,zr=null,Zl=null,Ko=null,ea=!1;function kd(e,t,o){var i=o.window===o?o.document:o.nodeType===9?o:o.ownerDocument;ea||zr==null||zr!==qt(i)||(i=zr,"selectionStart"in i&&Jl(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Ko&&Yo(Ko,i)||(Ko=i,i=ri(Zl,"onSelect"),0<i.length&&(t=new Vl("onSelect","select",null,t,o),e.push({event:t,listeners:i}),t.target=zr)))}function ei(e,t){var o={};return o[e.toLowerCase()]=t.toLowerCase(),o["Webkit"+e]="webkit"+t,o["Moz"+e]="moz"+t,o}var Wr={animationend:ei("Animation","AnimationEnd"),animationiteration:ei("Animation","AnimationIteration"),animationstart:ei("Animation","AnimationStart"),transitionend:ei("Transition","TransitionEnd")},ta={},bd={};p&&(bd=document.createElement("div").style,"AnimationEvent"in window||(delete Wr.animationend.animation,delete Wr.animationiteration.animation,delete Wr.animationstart.animation),"TransitionEvent"in window||delete Wr.transitionend.transition);function ti(e){if(ta[e])return ta[e];if(!Wr[e])return e;var t=Wr[e],o;for(o in t)if(t.hasOwnProperty(o)&&o in bd)return ta[e]=t[o];return e}var Pd=ti("animationend"),Nd=ti("animationiteration"),Td=ti("animationstart"),Rd=ti("transitionend"),Od=new Map,jd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tn(e,t){Od.set(e,t),d(t,[e])}for(var na=0;na<jd.length;na++){var ra=jd[na],Oy=ra.toLowerCase(),jy=ra[0].toUpperCase()+ra.slice(1);Tn(Oy,"on"+jy)}Tn(Pd,"onAnimationEnd"),Tn(Nd,"onAnimationIteration"),Tn(Td,"onAnimationStart"),Tn("dblclick","onDoubleClick"),Tn("focusin","onFocus"),Tn("focusout","onBlur"),Tn(Rd,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),d("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),d("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),d("onBeforeInput",["compositionend","keypress","textInput","paste"]),d("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Go="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),My=new Set("cancel close invalid load scroll toggle".split(" ").concat(Go));function Md(e,t,o){var i=e.type||"unknown-event";e.currentTarget=o,Og(i,t,void 0,e),e.currentTarget=null}function _d(e,t){t=(t&4)!==0;for(var o=0;o<e.length;o++){var i=e[o],a=i.event;i=i.listeners;e:{var c=void 0;if(t)for(var h=i.length-1;0<=h;h--){var w=i[h],b=w.instance,_=w.currentTarget;if(w=w.listener,b!==c&&a.isPropagationStopped())break e;Md(a,w,_),c=b}else for(h=0;h<i.length;h++){if(w=i[h],b=w.instance,_=w.currentTarget,w=w.listener,b!==c&&a.isPropagationStopped())break e;Md(a,w,_),c=b}}}if(zs)throw e=Al,zs=!1,Al=null,e}function Me(e,t){var o=t[da];o===void 0&&(o=t[da]=new Set);var i=e+"__bubble";o.has(i)||(Ad(t,e,2,!1),o.add(i))}function oa(e,t,o){var i=0;t&&(i|=4),Ad(o,e,i,t)}var ni="_reactListening"+Math.random().toString(36).slice(2);function Xo(e){if(!e[ni]){e[ni]=!0,l.forEach(function(o){o!=="selectionchange"&&(My.has(o)||oa(o,!1,e),oa(o,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ni]||(t[ni]=!0,oa("selectionchange",!1,t))}}function Ad(e,t,o,i){switch(rd(t)){case 1:var a=Qg;break;case 4:a=qg;break;default:a=Ul}o=a.bind(null,t,o,e),a=void 0,!_l||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(a=!0),i?a!==void 0?e.addEventListener(t,o,{capture:!0,passive:a}):e.addEventListener(t,o,!0):a!==void 0?e.addEventListener(t,o,{passive:a}):e.addEventListener(t,o,!1)}function sa(e,t,o,i,a){var c=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var h=i.tag;if(h===3||h===4){var w=i.stateNode.containerInfo;if(w===a||w.nodeType===8&&w.parentNode===a)break;if(h===4)for(h=i.return;h!==null;){var b=h.tag;if((b===3||b===4)&&(b=h.stateNode.containerInfo,b===a||b.nodeType===8&&b.parentNode===a))return;h=h.return}for(;w!==null;){if(h=lr(w),h===null)return;if(b=h.tag,b===5||b===6){i=c=h;continue e}w=w.parentNode}}i=i.return}zc(function(){var _=c,U=Ol(o),$=[];e:{var W=Od.get(e);if(W!==void 0){var G=Vl,te=e;switch(e){case"keypress":if(Gs(o)===0)break e;case"keydown":case"keyup":G=ay;break;case"focusin":te="focus",G=ql;break;case"focusout":te="blur",G=ql;break;case"beforeblur":case"afterblur":G=ql;break;case"click":if(o.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":G=id;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":G=Gg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":G=dy;break;case Pd:case Nd:case Td:G=Zg;break;case Rd:G=hy;break;case"scroll":G=Yg;break;case"wheel":G=my;break;case"copy":case"cut":case"paste":G=ty;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":G=ad}var ne=(t&4)!==0,Ue=!ne&&e==="scroll",O=ne?W!==null?W+"Capture":null:W;ne=[];for(var T=_,M;T!==null;){M=T;var V=M.stateNode;if(M.tag===5&&V!==null&&(M=V,O!==null&&(V=_o(T,O),V!=null&&ne.push(Jo(T,V,M)))),Ue)break;T=T.return}0<ne.length&&(W=new G(W,te,null,o,U),$.push({event:W,listeners:ne}))}}if((t&7)===0){e:{if(W=e==="mouseover"||e==="pointerover",G=e==="mouseout"||e==="pointerout",W&&o!==Rl&&(te=o.relatedTarget||o.fromElement)&&(lr(te)||te[an]))break e;if((G||W)&&(W=U.window===U?U:(W=U.ownerDocument)?W.defaultView||W.parentWindow:window,G?(te=o.relatedTarget||o.toElement,G=_,te=te?lr(te):null,te!==null&&(Ue=ir(te),te!==Ue||te.tag!==5&&te.tag!==6)&&(te=null)):(G=null,te=_),G!==te)){if(ne=id,V="onMouseLeave",O="onMouseEnter",T="mouse",(e==="pointerout"||e==="pointerover")&&(ne=ad,V="onPointerLeave",O="onPointerEnter",T="pointer"),Ue=G==null?W:Hr(G),M=te==null?W:Hr(te),W=new ne(V,T+"leave",G,o,U),W.target=Ue,W.relatedTarget=M,V=null,lr(U)===_&&(ne=new ne(O,T+"enter",te,o,U),ne.target=M,ne.relatedTarget=Ue,V=ne),Ue=V,G&&te)t:{for(ne=G,O=te,T=0,M=ne;M;M=Ur(M))T++;for(M=0,V=O;V;V=Ur(V))M++;for(;0<T-M;)ne=Ur(ne),T--;for(;0<M-T;)O=Ur(O),M--;for(;T--;){if(ne===O||O!==null&&ne===O.alternate)break t;ne=Ur(ne),O=Ur(O)}ne=null}else ne=null;G!==null&&Dd($,W,G,ne,!1),te!==null&&Ue!==null&&Dd($,Ue,te,ne,!0)}}e:{if(W=_?Hr(_):window,G=W.nodeName&&W.nodeName.toLowerCase(),G==="select"||G==="input"&&W.type==="file")var oe=Cy;else if(pd(W))if(gd)oe=Py;else{oe=ky;var se=Ey}else(G=W.nodeName)&&G.toLowerCase()==="input"&&(W.type==="checkbox"||W.type==="radio")&&(oe=by);if(oe&&(oe=oe(e,_))){md($,oe,o,U);break e}se&&se(e,W,_),e==="focusout"&&(se=W._wrapperState)&&se.controlled&&W.type==="number"&&Cn(W,"number",W.value)}switch(se=_?Hr(_):window,e){case"focusin":(pd(se)||se.contentEditable==="true")&&(zr=se,Zl=_,Ko=null);break;case"focusout":Ko=Zl=zr=null;break;case"mousedown":ea=!0;break;case"contextmenu":case"mouseup":case"dragend":ea=!1,kd($,o,U);break;case"selectionchange":if(Ry)break;case"keydown":case"keyup":kd($,o,U)}var ie;if(Kl)e:{switch(e){case"compositionstart":var ce="onCompositionStart";break e;case"compositionend":ce="onCompositionEnd";break e;case"compositionupdate":ce="onCompositionUpdate";break e}ce=void 0}else Fr?fd(e,o)&&(ce="onCompositionEnd"):e==="keydown"&&o.keyCode===229&&(ce="onCompositionStart");ce&&(ud&&o.locale!=="ko"&&(Fr||ce!=="onCompositionStart"?ce==="onCompositionEnd"&&Fr&&(ie=od()):(Nn=U,Hl="value"in Nn?Nn.value:Nn.textContent,Fr=!0)),se=ri(_,ce),0<se.length&&(ce=new ld(ce,e,null,o,U),$.push({event:ce,listeners:se}),ie?ce.data=ie:(ie=hd(o),ie!==null&&(ce.data=ie)))),(ie=yy?vy(e,o):xy(e,o))&&(_=ri(_,"onBeforeInput"),0<_.length&&(U=new ld("onBeforeInput","beforeinput",null,o,U),$.push({event:U,listeners:_}),U.data=ie))}_d($,t)})}function Jo(e,t,o){return{instance:e,listener:t,currentTarget:o}}function ri(e,t){for(var o=t+"Capture",i=[];e!==null;){var a=e,c=a.stateNode;a.tag===5&&c!==null&&(a=c,c=_o(e,o),c!=null&&i.unshift(Jo(e,c,a)),c=_o(e,t),c!=null&&i.push(Jo(e,c,a))),e=e.return}return i}function Ur(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Dd(e,t,o,i,a){for(var c=t._reactName,h=[];o!==null&&o!==i;){var w=o,b=w.alternate,_=w.stateNode;if(b!==null&&b===i)break;w.tag===5&&_!==null&&(w=_,a?(b=_o(o,c),b!=null&&h.unshift(Jo(o,b,w))):a||(b=_o(o,c),b!=null&&h.push(Jo(o,b,w)))),o=o.return}h.length!==0&&e.push({event:t,listeners:h})}var _y=/\r\n?/g,Ay=/\u0000|\uFFFD/g;function Ld(e){return(typeof e=="string"?e:""+e).replace(_y,`
`).replace(Ay,"")}function oi(e,t,o){if(t=Ld(t),Ld(e)!==t&&o)throw Error(s(425))}function si(){}var ia=null,la=null;function aa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ua=typeof setTimeout=="function"?setTimeout:void 0,Dy=typeof clearTimeout=="function"?clearTimeout:void 0,Id=typeof Promise=="function"?Promise:void 0,Ly=typeof queueMicrotask=="function"?queueMicrotask:typeof Id<"u"?function(e){return Id.resolve(null).then(e).catch(Iy)}:ua;function Iy(e){setTimeout(function(){throw e})}function ca(e,t){var o=t,i=0;do{var a=o.nextSibling;if(e.removeChild(o),a&&a.nodeType===8)if(o=a.data,o==="/$"){if(i===0){e.removeChild(a),$o(t);return}i--}else o!=="$"&&o!=="$?"&&o!=="$!"||i++;o=a}while(o);$o(t)}function Rn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Fd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var o=e.data;if(o==="$"||o==="$!"||o==="$?"){if(t===0)return e;t--}else o==="/$"&&t++}e=e.previousSibling}return null}var $r=Math.random().toString(36).slice(2),Kt="__reactFiber$"+$r,Zo="__reactProps$"+$r,an="__reactContainer$"+$r,da="__reactEvents$"+$r,Fy="__reactListeners$"+$r,zy="__reactHandles$"+$r;function lr(e){var t=e[Kt];if(t)return t;for(var o=e.parentNode;o;){if(t=o[an]||o[Kt]){if(o=t.alternate,t.child!==null||o!==null&&o.child!==null)for(e=Fd(e);e!==null;){if(o=e[Kt])return o;e=Fd(e)}return t}e=o,o=e.parentNode}return null}function es(e){return e=e[Kt]||e[an],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Hr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(s(33))}function ii(e){return e[Zo]||null}var fa=[],Vr=-1;function On(e){return{current:e}}function _e(e){0>Vr||(e.current=fa[Vr],fa[Vr]=null,Vr--)}function je(e,t){Vr++,fa[Vr]=e.current,e.current=t}var jn={},tt=On(jn),ct=On(!1),ar=jn;function Br(e,t){var o=e.type.contextTypes;if(!o)return jn;var i=e.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===t)return i.__reactInternalMemoizedMaskedChildContext;var a={},c;for(c in o)a[c]=t[c];return i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function dt(e){return e=e.childContextTypes,e!=null}function li(){_e(ct),_e(tt)}function zd(e,t,o){if(tt.current!==jn)throw Error(s(168));je(tt,t),je(ct,o)}function Wd(e,t,o){var i=e.stateNode;if(t=t.childContextTypes,typeof i.getChildContext!="function")return o;i=i.getChildContext();for(var a in i)if(!(a in t))throw Error(s(108,Ne(e)||"Unknown",a));return Q({},o,i)}function ai(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||jn,ar=tt.current,je(tt,e),je(ct,ct.current),!0}function Ud(e,t,o){var i=e.stateNode;if(!i)throw Error(s(169));o?(e=Wd(e,t,ar),i.__reactInternalMemoizedMergedChildContext=e,_e(ct),_e(tt),je(tt,e)):_e(ct),je(ct,o)}var un=null,ui=!1,ha=!1;function $d(e){un===null?un=[e]:un.push(e)}function Wy(e){ui=!0,$d(e)}function Mn(){if(!ha&&un!==null){ha=!0;var e=0,t=Te;try{var o=un;for(Te=1;e<o.length;e++){var i=o[e];do i=i(!0);while(i!==null)}un=null,ui=!1}catch(a){throw un!==null&&(un=un.slice(e+1)),Vc(Dl,Mn),a}finally{Te=t,ha=!1}}return null}var Qr=[],qr=0,ci=null,di=0,Pt=[],Nt=0,ur=null,cn=1,dn="";function cr(e,t){Qr[qr++]=di,Qr[qr++]=ci,ci=e,di=t}function Hd(e,t,o){Pt[Nt++]=cn,Pt[Nt++]=dn,Pt[Nt++]=ur,ur=e;var i=cn;e=dn;var a=32-At(i)-1;i&=~(1<<a),o+=1;var c=32-At(t)+a;if(30<c){var h=a-a%5;c=(i&(1<<h)-1).toString(32),i>>=h,a-=h,cn=1<<32-At(t)+a|o<<a|i,dn=c+e}else cn=1<<c|o<<a|i,dn=e}function pa(e){e.return!==null&&(cr(e,1),Hd(e,1,0))}function ma(e){for(;e===ci;)ci=Qr[--qr],Qr[qr]=null,di=Qr[--qr],Qr[qr]=null;for(;e===ur;)ur=Pt[--Nt],Pt[Nt]=null,dn=Pt[--Nt],Pt[Nt]=null,cn=Pt[--Nt],Pt[Nt]=null}var wt=null,St=null,De=!1,Lt=null;function Vd(e,t){var o=jt(5,null,null,0);o.elementType="DELETED",o.stateNode=t,o.return=e,t=e.deletions,t===null?(e.deletions=[o],e.flags|=16):t.push(o)}function Bd(e,t){switch(e.tag){case 5:var o=e.type;return t=t.nodeType!==1||o.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,wt=e,St=Rn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,wt=e,St=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(o=ur!==null?{id:cn,overflow:dn}:null,e.memoizedState={dehydrated:t,treeContext:o,retryLane:1073741824},o=jt(18,null,null,0),o.stateNode=t,o.return=e,e.child=o,wt=e,St=null,!0):!1;default:return!1}}function ga(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ya(e){if(De){var t=St;if(t){var o=t;if(!Bd(e,t)){if(ga(e))throw Error(s(418));t=Rn(o.nextSibling);var i=wt;t&&Bd(e,t)?Vd(i,o):(e.flags=e.flags&-4097|2,De=!1,wt=e)}}else{if(ga(e))throw Error(s(418));e.flags=e.flags&-4097|2,De=!1,wt=e}}}function Qd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;wt=e}function fi(e){if(e!==wt)return!1;if(!De)return Qd(e),De=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!aa(e.type,e.memoizedProps)),t&&(t=St)){if(ga(e))throw qd(),Error(s(418));for(;t;)Vd(e,t),t=Rn(t.nextSibling)}if(Qd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var o=e.data;if(o==="/$"){if(t===0){St=Rn(e.nextSibling);break e}t--}else o!=="$"&&o!=="$!"&&o!=="$?"||t++}e=e.nextSibling}St=null}}else St=wt?Rn(e.stateNode.nextSibling):null;return!0}function qd(){for(var e=St;e;)e=Rn(e.nextSibling)}function Yr(){St=wt=null,De=!1}function va(e){Lt===null?Lt=[e]:Lt.push(e)}var Uy=F.ReactCurrentBatchConfig;function ts(e,t,o){if(e=o.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(o._owner){if(o=o._owner,o){if(o.tag!==1)throw Error(s(309));var i=o.stateNode}if(!i)throw Error(s(147,e));var a=i,c=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===c?t.ref:(t=function(h){var w=a.refs;h===null?delete w[c]:w[c]=h},t._stringRef=c,t)}if(typeof e!="string")throw Error(s(284));if(!o._owner)throw Error(s(290,e))}return e}function hi(e,t){throw e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Yd(e){var t=e._init;return t(e._payload)}function Kd(e){function t(O,T){if(e){var M=O.deletions;M===null?(O.deletions=[T],O.flags|=16):M.push(T)}}function o(O,T){if(!e)return null;for(;T!==null;)t(O,T),T=T.sibling;return null}function i(O,T){for(O=new Map;T!==null;)T.key!==null?O.set(T.key,T):O.set(T.index,T),T=T.sibling;return O}function a(O,T){return O=Wn(O,T),O.index=0,O.sibling=null,O}function c(O,T,M){return O.index=M,e?(M=O.alternate,M!==null?(M=M.index,M<T?(O.flags|=2,T):M):(O.flags|=2,T)):(O.flags|=1048576,T)}function h(O){return e&&O.alternate===null&&(O.flags|=2),O}function w(O,T,M,V){return T===null||T.tag!==6?(T=uu(M,O.mode,V),T.return=O,T):(T=a(T,M),T.return=O,T)}function b(O,T,M,V){var oe=M.type;return oe===B?U(O,T,M.props.children,V,M.key):T!==null&&(T.elementType===oe||typeof oe=="object"&&oe!==null&&oe.$$typeof===ue&&Yd(oe)===T.type)?(V=a(T,M.props),V.ref=ts(O,T,M),V.return=O,V):(V=Ii(M.type,M.key,M.props,null,O.mode,V),V.ref=ts(O,T,M),V.return=O,V)}function _(O,T,M,V){return T===null||T.tag!==4||T.stateNode.containerInfo!==M.containerInfo||T.stateNode.implementation!==M.implementation?(T=cu(M,O.mode,V),T.return=O,T):(T=a(T,M.children||[]),T.return=O,T)}function U(O,T,M,V,oe){return T===null||T.tag!==7?(T=vr(M,O.mode,V,oe),T.return=O,T):(T=a(T,M),T.return=O,T)}function $(O,T,M){if(typeof T=="string"&&T!==""||typeof T=="number")return T=uu(""+T,O.mode,M),T.return=O,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case H:return M=Ii(T.type,T.key,T.props,null,O.mode,M),M.ref=ts(O,null,T),M.return=O,M;case q:return T=cu(T,O.mode,M),T.return=O,T;case ue:var V=T._init;return $(O,V(T._payload),M)}if(Oo(T)||Y(T))return T=vr(T,O.mode,M,null),T.return=O,T;hi(O,T)}return null}function W(O,T,M,V){var oe=T!==null?T.key:null;if(typeof M=="string"&&M!==""||typeof M=="number")return oe!==null?null:w(O,T,""+M,V);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case H:return M.key===oe?b(O,T,M,V):null;case q:return M.key===oe?_(O,T,M,V):null;case ue:return oe=M._init,W(O,T,oe(M._payload),V)}if(Oo(M)||Y(M))return oe!==null?null:U(O,T,M,V,null);hi(O,M)}return null}function G(O,T,M,V,oe){if(typeof V=="string"&&V!==""||typeof V=="number")return O=O.get(M)||null,w(T,O,""+V,oe);if(typeof V=="object"&&V!==null){switch(V.$$typeof){case H:return O=O.get(V.key===null?M:V.key)||null,b(T,O,V,oe);case q:return O=O.get(V.key===null?M:V.key)||null,_(T,O,V,oe);case ue:var se=V._init;return G(O,T,M,se(V._payload),oe)}if(Oo(V)||Y(V))return O=O.get(M)||null,U(T,O,V,oe,null);hi(T,V)}return null}function te(O,T,M,V){for(var oe=null,se=null,ie=T,ce=T=0,Ge=null;ie!==null&&ce<M.length;ce++){ie.index>ce?(Ge=ie,ie=null):Ge=ie.sibling;var be=W(O,ie,M[ce],V);if(be===null){ie===null&&(ie=Ge);break}e&&ie&&be.alternate===null&&t(O,ie),T=c(be,T,ce),se===null?oe=be:se.sibling=be,se=be,ie=Ge}if(ce===M.length)return o(O,ie),De&&cr(O,ce),oe;if(ie===null){for(;ce<M.length;ce++)ie=$(O,M[ce],V),ie!==null&&(T=c(ie,T,ce),se===null?oe=ie:se.sibling=ie,se=ie);return De&&cr(O,ce),oe}for(ie=i(O,ie);ce<M.length;ce++)Ge=G(ie,O,ce,M[ce],V),Ge!==null&&(e&&Ge.alternate!==null&&ie.delete(Ge.key===null?ce:Ge.key),T=c(Ge,T,ce),se===null?oe=Ge:se.sibling=Ge,se=Ge);return e&&ie.forEach(function(Un){return t(O,Un)}),De&&cr(O,ce),oe}function ne(O,T,M,V){var oe=Y(M);if(typeof oe!="function")throw Error(s(150));if(M=oe.call(M),M==null)throw Error(s(151));for(var se=oe=null,ie=T,ce=T=0,Ge=null,be=M.next();ie!==null&&!be.done;ce++,be=M.next()){ie.index>ce?(Ge=ie,ie=null):Ge=ie.sibling;var Un=W(O,ie,be.value,V);if(Un===null){ie===null&&(ie=Ge);break}e&&ie&&Un.alternate===null&&t(O,ie),T=c(Un,T,ce),se===null?oe=Un:se.sibling=Un,se=Un,ie=Ge}if(be.done)return o(O,ie),De&&cr(O,ce),oe;if(ie===null){for(;!be.done;ce++,be=M.next())be=$(O,be.value,V),be!==null&&(T=c(be,T,ce),se===null?oe=be:se.sibling=be,se=be);return De&&cr(O,ce),oe}for(ie=i(O,ie);!be.done;ce++,be=M.next())be=G(ie,O,ce,be.value,V),be!==null&&(e&&be.alternate!==null&&ie.delete(be.key===null?ce:be.key),T=c(be,T,ce),se===null?oe=be:se.sibling=be,se=be);return e&&ie.forEach(function(wv){return t(O,wv)}),De&&cr(O,ce),oe}function Ue(O,T,M,V){if(typeof M=="object"&&M!==null&&M.type===B&&M.key===null&&(M=M.props.children),typeof M=="object"&&M!==null){switch(M.$$typeof){case H:e:{for(var oe=M.key,se=T;se!==null;){if(se.key===oe){if(oe=M.type,oe===B){if(se.tag===7){o(O,se.sibling),T=a(se,M.props.children),T.return=O,O=T;break e}}else if(se.elementType===oe||typeof oe=="object"&&oe!==null&&oe.$$typeof===ue&&Yd(oe)===se.type){o(O,se.sibling),T=a(se,M.props),T.ref=ts(O,se,M),T.return=O,O=T;break e}o(O,se);break}else t(O,se);se=se.sibling}M.type===B?(T=vr(M.props.children,O.mode,V,M.key),T.return=O,O=T):(V=Ii(M.type,M.key,M.props,null,O.mode,V),V.ref=ts(O,T,M),V.return=O,O=V)}return h(O);case q:e:{for(se=M.key;T!==null;){if(T.key===se)if(T.tag===4&&T.stateNode.containerInfo===M.containerInfo&&T.stateNode.implementation===M.implementation){o(O,T.sibling),T=a(T,M.children||[]),T.return=O,O=T;break e}else{o(O,T);break}else t(O,T);T=T.sibling}T=cu(M,O.mode,V),T.return=O,O=T}return h(O);case ue:return se=M._init,Ue(O,T,se(M._payload),V)}if(Oo(M))return te(O,T,M,V);if(Y(M))return ne(O,T,M,V);hi(O,M)}return typeof M=="string"&&M!==""||typeof M=="number"?(M=""+M,T!==null&&T.tag===6?(o(O,T.sibling),T=a(T,M),T.return=O,O=T):(o(O,T),T=uu(M,O.mode,V),T.return=O,O=T),h(O)):o(O,T)}return Ue}var Kr=Kd(!0),Gd=Kd(!1),pi=On(null),mi=null,Gr=null,xa=null;function wa(){xa=Gr=mi=null}function Sa(e){var t=pi.current;_e(pi),e._currentValue=t}function Ca(e,t,o){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===o)break;e=e.return}}function Xr(e,t){mi=e,xa=Gr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(ft=!0),e.firstContext=null)}function Tt(e){var t=e._currentValue;if(xa!==e)if(e={context:e,memoizedValue:t,next:null},Gr===null){if(mi===null)throw Error(s(308));Gr=e,mi.dependencies={lanes:0,firstContext:e}}else Gr=Gr.next=e;return t}var dr=null;function Ea(e){dr===null?dr=[e]:dr.push(e)}function Xd(e,t,o,i){var a=t.interleaved;return a===null?(o.next=o,Ea(t)):(o.next=a.next,a.next=o),t.interleaved=o,fn(e,i)}function fn(e,t){e.lanes|=t;var o=e.alternate;for(o!==null&&(o.lanes|=t),o=e,e=e.return;e!==null;)e.childLanes|=t,o=e.alternate,o!==null&&(o.childLanes|=t),o=e,e=e.return;return o.tag===3?o.stateNode:null}var _n=!1;function ka(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Jd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function hn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function An(e,t,o){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Ee&2)!==0){var a=i.pending;return a===null?t.next=t:(t.next=a.next,a.next=t),i.pending=t,fn(e,o)}return a=i.interleaved,a===null?(t.next=t,Ea(i)):(t.next=a.next,a.next=t),i.interleaved=t,fn(e,o)}function gi(e,t,o){if(t=t.updateQueue,t!==null&&(t=t.shared,(o&4194240)!==0)){var i=t.lanes;i&=e.pendingLanes,o|=i,t.lanes=o,Fl(e,o)}}function Zd(e,t){var o=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,o===i)){var a=null,c=null;if(o=o.firstBaseUpdate,o!==null){do{var h={eventTime:o.eventTime,lane:o.lane,tag:o.tag,payload:o.payload,callback:o.callback,next:null};c===null?a=c=h:c=c.next=h,o=o.next}while(o!==null);c===null?a=c=t:c=c.next=t}else a=c=t;o={baseState:i.baseState,firstBaseUpdate:a,lastBaseUpdate:c,shared:i.shared,effects:i.effects},e.updateQueue=o;return}e=o.lastBaseUpdate,e===null?o.firstBaseUpdate=t:e.next=t,o.lastBaseUpdate=t}function yi(e,t,o,i){var a=e.updateQueue;_n=!1;var c=a.firstBaseUpdate,h=a.lastBaseUpdate,w=a.shared.pending;if(w!==null){a.shared.pending=null;var b=w,_=b.next;b.next=null,h===null?c=_:h.next=_,h=b;var U=e.alternate;U!==null&&(U=U.updateQueue,w=U.lastBaseUpdate,w!==h&&(w===null?U.firstBaseUpdate=_:w.next=_,U.lastBaseUpdate=b))}if(c!==null){var $=a.baseState;h=0,U=_=b=null,w=c;do{var W=w.lane,G=w.eventTime;if((i&W)===W){U!==null&&(U=U.next={eventTime:G,lane:0,tag:w.tag,payload:w.payload,callback:w.callback,next:null});e:{var te=e,ne=w;switch(W=t,G=o,ne.tag){case 1:if(te=ne.payload,typeof te=="function"){$=te.call(G,$,W);break e}$=te;break e;case 3:te.flags=te.flags&-65537|128;case 0:if(te=ne.payload,W=typeof te=="function"?te.call(G,$,W):te,W==null)break e;$=Q({},$,W);break e;case 2:_n=!0}}w.callback!==null&&w.lane!==0&&(e.flags|=64,W=a.effects,W===null?a.effects=[w]:W.push(w))}else G={eventTime:G,lane:W,tag:w.tag,payload:w.payload,callback:w.callback,next:null},U===null?(_=U=G,b=$):U=U.next=G,h|=W;if(w=w.next,w===null){if(w=a.shared.pending,w===null)break;W=w,w=W.next,W.next=null,a.lastBaseUpdate=W,a.shared.pending=null}}while(!0);if(U===null&&(b=$),a.baseState=b,a.firstBaseUpdate=_,a.lastBaseUpdate=U,t=a.shared.interleaved,t!==null){a=t;do h|=a.lane,a=a.next;while(a!==t)}else c===null&&(a.shared.lanes=0);pr|=h,e.lanes=h,e.memoizedState=$}}function ef(e,t,o){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var i=e[t],a=i.callback;if(a!==null){if(i.callback=null,i=o,typeof a!="function")throw Error(s(191,a));a.call(i)}}}var ns={},Gt=On(ns),rs=On(ns),os=On(ns);function fr(e){if(e===ns)throw Error(s(174));return e}function ba(e,t){switch(je(os,t),je(rs,e),je(Gt,ns),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Pl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Pl(t,e)}_e(Gt),je(Gt,t)}function Jr(){_e(Gt),_e(rs),_e(os)}function tf(e){fr(os.current);var t=fr(Gt.current),o=Pl(t,e.type);t!==o&&(je(rs,e),je(Gt,o))}function Pa(e){rs.current===e&&(_e(Gt),_e(rs))}var Le=On(0);function vi(e){for(var t=e;t!==null;){if(t.tag===13){var o=t.memoizedState;if(o!==null&&(o=o.dehydrated,o===null||o.data==="$?"||o.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Na=[];function Ta(){for(var e=0;e<Na.length;e++)Na[e]._workInProgressVersionPrimary=null;Na.length=0}var xi=F.ReactCurrentDispatcher,Ra=F.ReactCurrentBatchConfig,hr=0,Ie=null,He=null,Ye=null,wi=!1,ss=!1,is=0,$y=0;function nt(){throw Error(s(321))}function Oa(e,t){if(t===null)return!1;for(var o=0;o<t.length&&o<e.length;o++)if(!Dt(e[o],t[o]))return!1;return!0}function ja(e,t,o,i,a,c){if(hr=c,Ie=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,xi.current=e===null||e.memoizedState===null?Qy:qy,e=o(i,a),ss){c=0;do{if(ss=!1,is=0,25<=c)throw Error(s(301));c+=1,Ye=He=null,t.updateQueue=null,xi.current=Yy,e=o(i,a)}while(ss)}if(xi.current=Ei,t=He!==null&&He.next!==null,hr=0,Ye=He=Ie=null,wi=!1,t)throw Error(s(300));return e}function Ma(){var e=is!==0;return is=0,e}function Xt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ye===null?Ie.memoizedState=Ye=e:Ye=Ye.next=e,Ye}function Rt(){if(He===null){var e=Ie.alternate;e=e!==null?e.memoizedState:null}else e=He.next;var t=Ye===null?Ie.memoizedState:Ye.next;if(t!==null)Ye=t,He=e;else{if(e===null)throw Error(s(310));He=e,e={memoizedState:He.memoizedState,baseState:He.baseState,baseQueue:He.baseQueue,queue:He.queue,next:null},Ye===null?Ie.memoizedState=Ye=e:Ye=Ye.next=e}return Ye}function ls(e,t){return typeof t=="function"?t(e):t}function _a(e){var t=Rt(),o=t.queue;if(o===null)throw Error(s(311));o.lastRenderedReducer=e;var i=He,a=i.baseQueue,c=o.pending;if(c!==null){if(a!==null){var h=a.next;a.next=c.next,c.next=h}i.baseQueue=a=c,o.pending=null}if(a!==null){c=a.next,i=i.baseState;var w=h=null,b=null,_=c;do{var U=_.lane;if((hr&U)===U)b!==null&&(b=b.next={lane:0,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null}),i=_.hasEagerState?_.eagerState:e(i,_.action);else{var $={lane:U,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null};b===null?(w=b=$,h=i):b=b.next=$,Ie.lanes|=U,pr|=U}_=_.next}while(_!==null&&_!==c);b===null?h=i:b.next=w,Dt(i,t.memoizedState)||(ft=!0),t.memoizedState=i,t.baseState=h,t.baseQueue=b,o.lastRenderedState=i}if(e=o.interleaved,e!==null){a=e;do c=a.lane,Ie.lanes|=c,pr|=c,a=a.next;while(a!==e)}else a===null&&(o.lanes=0);return[t.memoizedState,o.dispatch]}function Aa(e){var t=Rt(),o=t.queue;if(o===null)throw Error(s(311));o.lastRenderedReducer=e;var i=o.dispatch,a=o.pending,c=t.memoizedState;if(a!==null){o.pending=null;var h=a=a.next;do c=e(c,h.action),h=h.next;while(h!==a);Dt(c,t.memoizedState)||(ft=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),o.lastRenderedState=c}return[c,i]}function nf(){}function rf(e,t){var o=Ie,i=Rt(),a=t(),c=!Dt(i.memoizedState,a);if(c&&(i.memoizedState=a,ft=!0),i=i.queue,Da(lf.bind(null,o,i,e),[e]),i.getSnapshot!==t||c||Ye!==null&&Ye.memoizedState.tag&1){if(o.flags|=2048,as(9,sf.bind(null,o,i,a,t),void 0,null),Ke===null)throw Error(s(349));(hr&30)!==0||of(o,t,a)}return a}function of(e,t,o){e.flags|=16384,e={getSnapshot:t,value:o},t=Ie.updateQueue,t===null?(t={lastEffect:null,stores:null},Ie.updateQueue=t,t.stores=[e]):(o=t.stores,o===null?t.stores=[e]:o.push(e))}function sf(e,t,o,i){t.value=o,t.getSnapshot=i,af(t)&&uf(e)}function lf(e,t,o){return o(function(){af(t)&&uf(e)})}function af(e){var t=e.getSnapshot;e=e.value;try{var o=t();return!Dt(e,o)}catch{return!0}}function uf(e){var t=fn(e,1);t!==null&&Wt(t,e,1,-1)}function cf(e){var t=Xt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ls,lastRenderedState:e},t.queue=e,e=e.dispatch=By.bind(null,Ie,e),[t.memoizedState,e]}function as(e,t,o,i){return e={tag:e,create:t,destroy:o,deps:i,next:null},t=Ie.updateQueue,t===null?(t={lastEffect:null,stores:null},Ie.updateQueue=t,t.lastEffect=e.next=e):(o=t.lastEffect,o===null?t.lastEffect=e.next=e:(i=o.next,o.next=e,e.next=i,t.lastEffect=e)),e}function df(){return Rt().memoizedState}function Si(e,t,o,i){var a=Xt();Ie.flags|=e,a.memoizedState=as(1|t,o,void 0,i===void 0?null:i)}function Ci(e,t,o,i){var a=Rt();i=i===void 0?null:i;var c=void 0;if(He!==null){var h=He.memoizedState;if(c=h.destroy,i!==null&&Oa(i,h.deps)){a.memoizedState=as(t,o,c,i);return}}Ie.flags|=e,a.memoizedState=as(1|t,o,c,i)}function ff(e,t){return Si(8390656,8,e,t)}function Da(e,t){return Ci(2048,8,e,t)}function hf(e,t){return Ci(4,2,e,t)}function pf(e,t){return Ci(4,4,e,t)}function mf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function gf(e,t,o){return o=o!=null?o.concat([e]):null,Ci(4,4,mf.bind(null,t,e),o)}function La(){}function yf(e,t){var o=Rt();t=t===void 0?null:t;var i=o.memoizedState;return i!==null&&t!==null&&Oa(t,i[1])?i[0]:(o.memoizedState=[e,t],e)}function vf(e,t){var o=Rt();t=t===void 0?null:t;var i=o.memoizedState;return i!==null&&t!==null&&Oa(t,i[1])?i[0]:(e=e(),o.memoizedState=[e,t],e)}function xf(e,t,o){return(hr&21)===0?(e.baseState&&(e.baseState=!1,ft=!0),e.memoizedState=o):(Dt(o,t)||(o=Yc(),Ie.lanes|=o,pr|=o,e.baseState=!0),t)}function Hy(e,t){var o=Te;Te=o!==0&&4>o?o:4,e(!0);var i=Ra.transition;Ra.transition={};try{e(!1),t()}finally{Te=o,Ra.transition=i}}function wf(){return Rt().memoizedState}function Vy(e,t,o){var i=Fn(e);if(o={lane:i,action:o,hasEagerState:!1,eagerState:null,next:null},Sf(e))Cf(t,o);else if(o=Xd(e,t,o,i),o!==null){var a=lt();Wt(o,e,i,a),Ef(o,t,i)}}function By(e,t,o){var i=Fn(e),a={lane:i,action:o,hasEagerState:!1,eagerState:null,next:null};if(Sf(e))Cf(t,a);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var h=t.lastRenderedState,w=c(h,o);if(a.hasEagerState=!0,a.eagerState=w,Dt(w,h)){var b=t.interleaved;b===null?(a.next=a,Ea(t)):(a.next=b.next,b.next=a),t.interleaved=a;return}}catch{}finally{}o=Xd(e,t,a,i),o!==null&&(a=lt(),Wt(o,e,i,a),Ef(o,t,i))}}function Sf(e){var t=e.alternate;return e===Ie||t!==null&&t===Ie}function Cf(e,t){ss=wi=!0;var o=e.pending;o===null?t.next=t:(t.next=o.next,o.next=t),e.pending=t}function Ef(e,t,o){if((o&4194240)!==0){var i=t.lanes;i&=e.pendingLanes,o|=i,t.lanes=o,Fl(e,o)}}var Ei={readContext:Tt,useCallback:nt,useContext:nt,useEffect:nt,useImperativeHandle:nt,useInsertionEffect:nt,useLayoutEffect:nt,useMemo:nt,useReducer:nt,useRef:nt,useState:nt,useDebugValue:nt,useDeferredValue:nt,useTransition:nt,useMutableSource:nt,useSyncExternalStore:nt,useId:nt,unstable_isNewReconciler:!1},Qy={readContext:Tt,useCallback:function(e,t){return Xt().memoizedState=[e,t===void 0?null:t],e},useContext:Tt,useEffect:ff,useImperativeHandle:function(e,t,o){return o=o!=null?o.concat([e]):null,Si(4194308,4,mf.bind(null,t,e),o)},useLayoutEffect:function(e,t){return Si(4194308,4,e,t)},useInsertionEffect:function(e,t){return Si(4,2,e,t)},useMemo:function(e,t){var o=Xt();return t=t===void 0?null:t,e=e(),o.memoizedState=[e,t],e},useReducer:function(e,t,o){var i=Xt();return t=o!==void 0?o(t):t,i.memoizedState=i.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},i.queue=e,e=e.dispatch=Vy.bind(null,Ie,e),[i.memoizedState,e]},useRef:function(e){var t=Xt();return e={current:e},t.memoizedState=e},useState:cf,useDebugValue:La,useDeferredValue:function(e){return Xt().memoizedState=e},useTransition:function(){var e=cf(!1),t=e[0];return e=Hy.bind(null,e[1]),Xt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,o){var i=Ie,a=Xt();if(De){if(o===void 0)throw Error(s(407));o=o()}else{if(o=t(),Ke===null)throw Error(s(349));(hr&30)!==0||of(i,t,o)}a.memoizedState=o;var c={value:o,getSnapshot:t};return a.queue=c,ff(lf.bind(null,i,c,e),[e]),i.flags|=2048,as(9,sf.bind(null,i,c,o,t),void 0,null),o},useId:function(){var e=Xt(),t=Ke.identifierPrefix;if(De){var o=dn,i=cn;o=(i&~(1<<32-At(i)-1)).toString(32)+o,t=":"+t+"R"+o,o=is++,0<o&&(t+="H"+o.toString(32)),t+=":"}else o=$y++,t=":"+t+"r"+o.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},qy={readContext:Tt,useCallback:yf,useContext:Tt,useEffect:Da,useImperativeHandle:gf,useInsertionEffect:hf,useLayoutEffect:pf,useMemo:vf,useReducer:_a,useRef:df,useState:function(){return _a(ls)},useDebugValue:La,useDeferredValue:function(e){var t=Rt();return xf(t,He.memoizedState,e)},useTransition:function(){var e=_a(ls)[0],t=Rt().memoizedState;return[e,t]},useMutableSource:nf,useSyncExternalStore:rf,useId:wf,unstable_isNewReconciler:!1},Yy={readContext:Tt,useCallback:yf,useContext:Tt,useEffect:Da,useImperativeHandle:gf,useInsertionEffect:hf,useLayoutEffect:pf,useMemo:vf,useReducer:Aa,useRef:df,useState:function(){return Aa(ls)},useDebugValue:La,useDeferredValue:function(e){var t=Rt();return He===null?t.memoizedState=e:xf(t,He.memoizedState,e)},useTransition:function(){var e=Aa(ls)[0],t=Rt().memoizedState;return[e,t]},useMutableSource:nf,useSyncExternalStore:rf,useId:wf,unstable_isNewReconciler:!1};function It(e,t){if(e&&e.defaultProps){t=Q({},t),e=e.defaultProps;for(var o in e)t[o]===void 0&&(t[o]=e[o]);return t}return t}function Ia(e,t,o,i){t=e.memoizedState,o=o(i,t),o=o==null?t:Q({},t,o),e.memoizedState=o,e.lanes===0&&(e.updateQueue.baseState=o)}var ki={isMounted:function(e){return(e=e._reactInternals)?ir(e)===e:!1},enqueueSetState:function(e,t,o){e=e._reactInternals;var i=lt(),a=Fn(e),c=hn(i,a);c.payload=t,o!=null&&(c.callback=o),t=An(e,c,a),t!==null&&(Wt(t,e,a,i),gi(t,e,a))},enqueueReplaceState:function(e,t,o){e=e._reactInternals;var i=lt(),a=Fn(e),c=hn(i,a);c.tag=1,c.payload=t,o!=null&&(c.callback=o),t=An(e,c,a),t!==null&&(Wt(t,e,a,i),gi(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var o=lt(),i=Fn(e),a=hn(o,i);a.tag=2,t!=null&&(a.callback=t),t=An(e,a,i),t!==null&&(Wt(t,e,i,o),gi(t,e,i))}};function kf(e,t,o,i,a,c,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,c,h):t.prototype&&t.prototype.isPureReactComponent?!Yo(o,i)||!Yo(a,c):!0}function bf(e,t,o){var i=!1,a=jn,c=t.contextType;return typeof c=="object"&&c!==null?c=Tt(c):(a=dt(t)?ar:tt.current,i=t.contextTypes,c=(i=i!=null)?Br(e,a):jn),t=new t(o,c),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ki,e.stateNode=t,t._reactInternals=e,i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=c),t}function Pf(e,t,o,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(o,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(o,i),t.state!==e&&ki.enqueueReplaceState(t,t.state,null)}function Fa(e,t,o,i){var a=e.stateNode;a.props=o,a.state=e.memoizedState,a.refs={},ka(e);var c=t.contextType;typeof c=="object"&&c!==null?a.context=Tt(c):(c=dt(t)?ar:tt.current,a.context=Br(e,c)),a.state=e.memoizedState,c=t.getDerivedStateFromProps,typeof c=="function"&&(Ia(e,t,c,o),a.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof a.getSnapshotBeforeUpdate=="function"||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(t=a.state,typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount(),t!==a.state&&ki.enqueueReplaceState(a,a.state,null),yi(e,o,a,i),a.state=e.memoizedState),typeof a.componentDidMount=="function"&&(e.flags|=4194308)}function Zr(e,t){try{var o="",i=t;do o+=Se(i),i=i.return;while(i);var a=o}catch(c){a=`
Error generating stack: `+c.message+`
`+c.stack}return{value:e,source:t,stack:a,digest:null}}function za(e,t,o){return{value:e,source:null,stack:o??null,digest:t??null}}function Wa(e,t){try{console.error(t.value)}catch(o){setTimeout(function(){throw o})}}var Ky=typeof WeakMap=="function"?WeakMap:Map;function Nf(e,t,o){o=hn(-1,o),o.tag=3,o.payload={element:null};var i=t.value;return o.callback=function(){ji||(ji=!0,tu=i),Wa(e,t)},o}function Tf(e,t,o){o=hn(-1,o),o.tag=3;var i=e.type.getDerivedStateFromError;if(typeof i=="function"){var a=t.value;o.payload=function(){return i(a)},o.callback=function(){Wa(e,t)}}var c=e.stateNode;return c!==null&&typeof c.componentDidCatch=="function"&&(o.callback=function(){Wa(e,t),typeof i!="function"&&(Ln===null?Ln=new Set([this]):Ln.add(this));var h=t.stack;this.componentDidCatch(t.value,{componentStack:h!==null?h:""})}),o}function Rf(e,t,o){var i=e.pingCache;if(i===null){i=e.pingCache=new Ky;var a=new Set;i.set(t,a)}else a=i.get(t),a===void 0&&(a=new Set,i.set(t,a));a.has(o)||(a.add(o),e=uv.bind(null,e,t,o),t.then(e,e))}function Of(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function jf(e,t,o,i,a){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,o.flags|=131072,o.flags&=-52805,o.tag===1&&(o.alternate===null?o.tag=17:(t=hn(-1,1),t.tag=2,An(o,t,1))),o.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var Gy=F.ReactCurrentOwner,ft=!1;function it(e,t,o,i){t.child=e===null?Gd(t,null,o,i):Kr(t,e.child,o,i)}function Mf(e,t,o,i,a){o=o.render;var c=t.ref;return Xr(t,a),i=ja(e,t,o,i,c,a),o=Ma(),e!==null&&!ft?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,pn(e,t,a)):(De&&o&&pa(t),t.flags|=1,it(e,t,i,a),t.child)}function _f(e,t,o,i,a){if(e===null){var c=o.type;return typeof c=="function"&&!au(c)&&c.defaultProps===void 0&&o.compare===null&&o.defaultProps===void 0?(t.tag=15,t.type=c,Af(e,t,c,i,a)):(e=Ii(o.type,null,i,t,t.mode,a),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,(e.lanes&a)===0){var h=c.memoizedProps;if(o=o.compare,o=o!==null?o:Yo,o(h,i)&&e.ref===t.ref)return pn(e,t,a)}return t.flags|=1,e=Wn(c,i),e.ref=t.ref,e.return=t,t.child=e}function Af(e,t,o,i,a){if(e!==null){var c=e.memoizedProps;if(Yo(c,i)&&e.ref===t.ref)if(ft=!1,t.pendingProps=i=c,(e.lanes&a)!==0)(e.flags&131072)!==0&&(ft=!0);else return t.lanes=e.lanes,pn(e,t,a)}return Ua(e,t,o,i,a)}function Df(e,t,o){var i=t.pendingProps,a=i.children,c=e!==null?e.memoizedState:null;if(i.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},je(to,Ct),Ct|=o;else{if((o&1073741824)===0)return e=c!==null?c.baseLanes|o:o,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,je(to,Ct),Ct|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},i=c!==null?c.baseLanes:o,je(to,Ct),Ct|=i}else c!==null?(i=c.baseLanes|o,t.memoizedState=null):i=o,je(to,Ct),Ct|=i;return it(e,t,a,o),t.child}function Lf(e,t){var o=t.ref;(e===null&&o!==null||e!==null&&e.ref!==o)&&(t.flags|=512,t.flags|=2097152)}function Ua(e,t,o,i,a){var c=dt(o)?ar:tt.current;return c=Br(t,c),Xr(t,a),o=ja(e,t,o,i,c,a),i=Ma(),e!==null&&!ft?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,pn(e,t,a)):(De&&i&&pa(t),t.flags|=1,it(e,t,o,a),t.child)}function If(e,t,o,i,a){if(dt(o)){var c=!0;ai(t)}else c=!1;if(Xr(t,a),t.stateNode===null)Pi(e,t),bf(t,o,i),Fa(t,o,i,a),i=!0;else if(e===null){var h=t.stateNode,w=t.memoizedProps;h.props=w;var b=h.context,_=o.contextType;typeof _=="object"&&_!==null?_=Tt(_):(_=dt(o)?ar:tt.current,_=Br(t,_));var U=o.getDerivedStateFromProps,$=typeof U=="function"||typeof h.getSnapshotBeforeUpdate=="function";$||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(w!==i||b!==_)&&Pf(t,h,i,_),_n=!1;var W=t.memoizedState;h.state=W,yi(t,i,h,a),b=t.memoizedState,w!==i||W!==b||ct.current||_n?(typeof U=="function"&&(Ia(t,o,U,i),b=t.memoizedState),(w=_n||kf(t,o,w,i,W,b,_))?($||typeof h.UNSAFE_componentWillMount!="function"&&typeof h.componentWillMount!="function"||(typeof h.componentWillMount=="function"&&h.componentWillMount(),typeof h.UNSAFE_componentWillMount=="function"&&h.UNSAFE_componentWillMount()),typeof h.componentDidMount=="function"&&(t.flags|=4194308)):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=b),h.props=i,h.state=b,h.context=_,i=w):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{h=t.stateNode,Jd(e,t),w=t.memoizedProps,_=t.type===t.elementType?w:It(t.type,w),h.props=_,$=t.pendingProps,W=h.context,b=o.contextType,typeof b=="object"&&b!==null?b=Tt(b):(b=dt(o)?ar:tt.current,b=Br(t,b));var G=o.getDerivedStateFromProps;(U=typeof G=="function"||typeof h.getSnapshotBeforeUpdate=="function")||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(w!==$||W!==b)&&Pf(t,h,i,b),_n=!1,W=t.memoizedState,h.state=W,yi(t,i,h,a);var te=t.memoizedState;w!==$||W!==te||ct.current||_n?(typeof G=="function"&&(Ia(t,o,G,i),te=t.memoizedState),(_=_n||kf(t,o,_,i,W,te,b)||!1)?(U||typeof h.UNSAFE_componentWillUpdate!="function"&&typeof h.componentWillUpdate!="function"||(typeof h.componentWillUpdate=="function"&&h.componentWillUpdate(i,te,b),typeof h.UNSAFE_componentWillUpdate=="function"&&h.UNSAFE_componentWillUpdate(i,te,b)),typeof h.componentDidUpdate=="function"&&(t.flags|=4),typeof h.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof h.componentDidUpdate!="function"||w===e.memoizedProps&&W===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||w===e.memoizedProps&&W===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=te),h.props=i,h.state=te,h.context=b,i=_):(typeof h.componentDidUpdate!="function"||w===e.memoizedProps&&W===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||w===e.memoizedProps&&W===e.memoizedState||(t.flags|=1024),i=!1)}return $a(e,t,o,i,c,a)}function $a(e,t,o,i,a,c){Lf(e,t);var h=(t.flags&128)!==0;if(!i&&!h)return a&&Ud(t,o,!1),pn(e,t,c);i=t.stateNode,Gy.current=t;var w=h&&typeof o.getDerivedStateFromError!="function"?null:i.render();return t.flags|=1,e!==null&&h?(t.child=Kr(t,e.child,null,c),t.child=Kr(t,null,w,c)):it(e,t,w,c),t.memoizedState=i.state,a&&Ud(t,o,!0),t.child}function Ff(e){var t=e.stateNode;t.pendingContext?zd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&zd(e,t.context,!1),ba(e,t.containerInfo)}function zf(e,t,o,i,a){return Yr(),va(a),t.flags|=256,it(e,t,o,i),t.child}var Ha={dehydrated:null,treeContext:null,retryLane:0};function Va(e){return{baseLanes:e,cachePool:null,transitions:null}}function Wf(e,t,o){var i=t.pendingProps,a=Le.current,c=!1,h=(t.flags&128)!==0,w;if((w=h)||(w=e!==null&&e.memoizedState===null?!1:(a&2)!==0),w?(c=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(a|=1),je(Le,a&1),e===null)return ya(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(h=i.children,e=i.fallback,c?(i=t.mode,c=t.child,h={mode:"hidden",children:h},(i&1)===0&&c!==null?(c.childLanes=0,c.pendingProps=h):c=Fi(h,i,0,null),e=vr(e,i,o,null),c.return=t,e.return=t,c.sibling=e,t.child=c,t.child.memoizedState=Va(o),t.memoizedState=Ha,e):Ba(t,h));if(a=e.memoizedState,a!==null&&(w=a.dehydrated,w!==null))return Xy(e,t,h,i,w,a,o);if(c){c=i.fallback,h=t.mode,a=e.child,w=a.sibling;var b={mode:"hidden",children:i.children};return(h&1)===0&&t.child!==a?(i=t.child,i.childLanes=0,i.pendingProps=b,t.deletions=null):(i=Wn(a,b),i.subtreeFlags=a.subtreeFlags&14680064),w!==null?c=Wn(w,c):(c=vr(c,h,o,null),c.flags|=2),c.return=t,i.return=t,i.sibling=c,t.child=i,i=c,c=t.child,h=e.child.memoizedState,h=h===null?Va(o):{baseLanes:h.baseLanes|o,cachePool:null,transitions:h.transitions},c.memoizedState=h,c.childLanes=e.childLanes&~o,t.memoizedState=Ha,i}return c=e.child,e=c.sibling,i=Wn(c,{mode:"visible",children:i.children}),(t.mode&1)===0&&(i.lanes=o),i.return=t,i.sibling=null,e!==null&&(o=t.deletions,o===null?(t.deletions=[e],t.flags|=16):o.push(e)),t.child=i,t.memoizedState=null,i}function Ba(e,t){return t=Fi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function bi(e,t,o,i){return i!==null&&va(i),Kr(t,e.child,null,o),e=Ba(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Xy(e,t,o,i,a,c,h){if(o)return t.flags&256?(t.flags&=-257,i=za(Error(s(422))),bi(e,t,h,i)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(c=i.fallback,a=t.mode,i=Fi({mode:"visible",children:i.children},a,0,null),c=vr(c,a,h,null),c.flags|=2,i.return=t,c.return=t,i.sibling=c,t.child=i,(t.mode&1)!==0&&Kr(t,e.child,null,h),t.child.memoizedState=Va(h),t.memoizedState=Ha,c);if((t.mode&1)===0)return bi(e,t,h,null);if(a.data==="$!"){if(i=a.nextSibling&&a.nextSibling.dataset,i)var w=i.dgst;return i=w,c=Error(s(419)),i=za(c,i,void 0),bi(e,t,h,i)}if(w=(h&e.childLanes)!==0,ft||w){if(i=Ke,i!==null){switch(h&-h){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}a=(a&(i.suspendedLanes|h))!==0?0:a,a!==0&&a!==c.retryLane&&(c.retryLane=a,fn(e,a),Wt(i,e,a,-1))}return lu(),i=za(Error(s(421))),bi(e,t,h,i)}return a.data==="$?"?(t.flags|=128,t.child=e.child,t=cv.bind(null,e),a._reactRetry=t,null):(e=c.treeContext,St=Rn(a.nextSibling),wt=t,De=!0,Lt=null,e!==null&&(Pt[Nt++]=cn,Pt[Nt++]=dn,Pt[Nt++]=ur,cn=e.id,dn=e.overflow,ur=t),t=Ba(t,i.children),t.flags|=4096,t)}function Uf(e,t,o){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),Ca(e.return,t,o)}function Qa(e,t,o,i,a){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:o,tailMode:a}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=i,c.tail=o,c.tailMode=a)}function $f(e,t,o){var i=t.pendingProps,a=i.revealOrder,c=i.tail;if(it(e,t,i.children,o),i=Le.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Uf(e,o,t);else if(e.tag===19)Uf(e,o,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}if(je(Le,i),(t.mode&1)===0)t.memoizedState=null;else switch(a){case"forwards":for(o=t.child,a=null;o!==null;)e=o.alternate,e!==null&&vi(e)===null&&(a=o),o=o.sibling;o=a,o===null?(a=t.child,t.child=null):(a=o.sibling,o.sibling=null),Qa(t,!1,a,o,c);break;case"backwards":for(o=null,a=t.child,t.child=null;a!==null;){if(e=a.alternate,e!==null&&vi(e)===null){t.child=a;break}e=a.sibling,a.sibling=o,o=a,a=e}Qa(t,!0,o,null,c);break;case"together":Qa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Pi(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function pn(e,t,o){if(e!==null&&(t.dependencies=e.dependencies),pr|=t.lanes,(o&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,o=Wn(e,e.pendingProps),t.child=o,o.return=t;e.sibling!==null;)e=e.sibling,o=o.sibling=Wn(e,e.pendingProps),o.return=t;o.sibling=null}return t.child}function Jy(e,t,o){switch(t.tag){case 3:Ff(t),Yr();break;case 5:tf(t);break;case 1:dt(t.type)&&ai(t);break;case 4:ba(t,t.stateNode.containerInfo);break;case 10:var i=t.type._context,a=t.memoizedProps.value;je(pi,i._currentValue),i._currentValue=a;break;case 13:if(i=t.memoizedState,i!==null)return i.dehydrated!==null?(je(Le,Le.current&1),t.flags|=128,null):(o&t.child.childLanes)!==0?Wf(e,t,o):(je(Le,Le.current&1),e=pn(e,t,o),e!==null?e.sibling:null);je(Le,Le.current&1);break;case 19:if(i=(o&t.childLanes)!==0,(e.flags&128)!==0){if(i)return $f(e,t,o);t.flags|=128}if(a=t.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),je(Le,Le.current),i)break;return null;case 22:case 23:return t.lanes=0,Df(e,t,o)}return pn(e,t,o)}var Hf,qa,Vf,Bf;Hf=function(e,t){for(var o=t.child;o!==null;){if(o.tag===5||o.tag===6)e.appendChild(o.stateNode);else if(o.tag!==4&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===t)break;for(;o.sibling===null;){if(o.return===null||o.return===t)return;o=o.return}o.sibling.return=o.return,o=o.sibling}},qa=function(){},Vf=function(e,t,o,i){var a=e.memoizedProps;if(a!==i){e=t.stateNode,fr(Gt.current);var c=null;switch(o){case"input":a=Mr(e,a),i=Mr(e,i),c=[];break;case"select":a=Q({},a,{value:void 0}),i=Q({},i,{value:void 0}),c=[];break;case"textarea":a=bl(e,a),i=bl(e,i),c=[];break;default:typeof a.onClick!="function"&&typeof i.onClick=="function"&&(e.onclick=si)}Nl(o,i);var h;o=null;for(_ in a)if(!i.hasOwnProperty(_)&&a.hasOwnProperty(_)&&a[_]!=null)if(_==="style"){var w=a[_];for(h in w)w.hasOwnProperty(h)&&(o||(o={}),o[h]="")}else _!=="dangerouslySetInnerHTML"&&_!=="children"&&_!=="suppressContentEditableWarning"&&_!=="suppressHydrationWarning"&&_!=="autoFocus"&&(u.hasOwnProperty(_)?c||(c=[]):(c=c||[]).push(_,null));for(_ in i){var b=i[_];if(w=a!=null?a[_]:void 0,i.hasOwnProperty(_)&&b!==w&&(b!=null||w!=null))if(_==="style")if(w){for(h in w)!w.hasOwnProperty(h)||b&&b.hasOwnProperty(h)||(o||(o={}),o[h]="");for(h in b)b.hasOwnProperty(h)&&w[h]!==b[h]&&(o||(o={}),o[h]=b[h])}else o||(c||(c=[]),c.push(_,o)),o=b;else _==="dangerouslySetInnerHTML"?(b=b?b.__html:void 0,w=w?w.__html:void 0,b!=null&&w!==b&&(c=c||[]).push(_,b)):_==="children"?typeof b!="string"&&typeof b!="number"||(c=c||[]).push(_,""+b):_!=="suppressContentEditableWarning"&&_!=="suppressHydrationWarning"&&(u.hasOwnProperty(_)?(b!=null&&_==="onScroll"&&Me("scroll",e),c||w===b||(c=[])):(c=c||[]).push(_,b))}o&&(c=c||[]).push("style",o);var _=c;(t.updateQueue=_)&&(t.flags|=4)}},Bf=function(e,t,o,i){o!==i&&(t.flags|=4)};function us(e,t){if(!De)switch(e.tailMode){case"hidden":t=e.tail;for(var o=null;t!==null;)t.alternate!==null&&(o=t),t=t.sibling;o===null?e.tail=null:o.sibling=null;break;case"collapsed":o=e.tail;for(var i=null;o!==null;)o.alternate!==null&&(i=o),o=o.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function rt(e){var t=e.alternate!==null&&e.alternate.child===e.child,o=0,i=0;if(t)for(var a=e.child;a!==null;)o|=a.lanes|a.childLanes,i|=a.subtreeFlags&14680064,i|=a.flags&14680064,a.return=e,a=a.sibling;else for(a=e.child;a!==null;)o|=a.lanes|a.childLanes,i|=a.subtreeFlags,i|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=i,e.childLanes=o,t}function Zy(e,t,o){var i=t.pendingProps;switch(ma(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return rt(t),null;case 1:return dt(t.type)&&li(),rt(t),null;case 3:return i=t.stateNode,Jr(),_e(ct),_e(tt),Ta(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(fi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Lt!==null&&(ou(Lt),Lt=null))),qa(e,t),rt(t),null;case 5:Pa(t);var a=fr(os.current);if(o=t.type,e!==null&&t.stateNode!=null)Vf(e,t,o,i,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!i){if(t.stateNode===null)throw Error(s(166));return rt(t),null}if(e=fr(Gt.current),fi(t)){i=t.stateNode,o=t.type;var c=t.memoizedProps;switch(i[Kt]=t,i[Zo]=c,e=(t.mode&1)!==0,o){case"dialog":Me("cancel",i),Me("close",i);break;case"iframe":case"object":case"embed":Me("load",i);break;case"video":case"audio":for(a=0;a<Go.length;a++)Me(Go[a],i);break;case"source":Me("error",i);break;case"img":case"image":case"link":Me("error",i),Me("load",i);break;case"details":Me("toggle",i);break;case"input":As(i,c),Me("invalid",i);break;case"select":i._wrapperState={wasMultiple:!!c.multiple},Me("invalid",i);break;case"textarea":Nc(i,c),Me("invalid",i)}Nl(o,c),a=null;for(var h in c)if(c.hasOwnProperty(h)){var w=c[h];h==="children"?typeof w=="string"?i.textContent!==w&&(c.suppressHydrationWarning!==!0&&oi(i.textContent,w,e),a=["children",w]):typeof w=="number"&&i.textContent!==""+w&&(c.suppressHydrationWarning!==!0&&oi(i.textContent,w,e),a=["children",""+w]):u.hasOwnProperty(h)&&w!=null&&h==="onScroll"&&Me("scroll",i)}switch(o){case"input":sr(i),Ls(i,c,!0);break;case"textarea":sr(i),Rc(i);break;case"select":case"option":break;default:typeof c.onClick=="function"&&(i.onclick=si)}i=a,t.updateQueue=i,i!==null&&(t.flags|=4)}else{h=a.nodeType===9?a:a.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Oc(o)),e==="http://www.w3.org/1999/xhtml"?o==="script"?(e=h.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof i.is=="string"?e=h.createElement(o,{is:i.is}):(e=h.createElement(o),o==="select"&&(h=e,i.multiple?h.multiple=!0:i.size&&(h.size=i.size))):e=h.createElementNS(e,o),e[Kt]=t,e[Zo]=i,Hf(e,t,!1,!1),t.stateNode=e;e:{switch(h=Tl(o,i),o){case"dialog":Me("cancel",e),Me("close",e),a=i;break;case"iframe":case"object":case"embed":Me("load",e),a=i;break;case"video":case"audio":for(a=0;a<Go.length;a++)Me(Go[a],e);a=i;break;case"source":Me("error",e),a=i;break;case"img":case"image":case"link":Me("error",e),Me("load",e),a=i;break;case"details":Me("toggle",e),a=i;break;case"input":As(e,i),a=Mr(e,i),Me("invalid",e);break;case"option":a=i;break;case"select":e._wrapperState={wasMultiple:!!i.multiple},a=Q({},i,{value:void 0}),Me("invalid",e);break;case"textarea":Nc(e,i),a=bl(e,i),Me("invalid",e);break;default:a=i}Nl(o,a),w=a;for(c in w)if(w.hasOwnProperty(c)){var b=w[c];c==="style"?_c(e,b):c==="dangerouslySetInnerHTML"?(b=b?b.__html:void 0,b!=null&&jc(e,b)):c==="children"?typeof b=="string"?(o!=="textarea"||b!=="")&&jo(e,b):typeof b=="number"&&jo(e,""+b):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(u.hasOwnProperty(c)?b!=null&&c==="onScroll"&&Me("scroll",e):b!=null&&I(e,c,b,h))}switch(o){case"input":sr(e),Ls(e,i,!1);break;case"textarea":sr(e),Rc(e);break;case"option":i.value!=null&&e.setAttribute("value",""+ke(i.value));break;case"select":e.multiple=!!i.multiple,c=i.value,c!=null?_r(e,!!i.multiple,c,!1):i.defaultValue!=null&&_r(e,!!i.multiple,i.defaultValue,!0);break;default:typeof a.onClick=="function"&&(e.onclick=si)}switch(o){case"button":case"input":case"select":case"textarea":i=!!i.autoFocus;break e;case"img":i=!0;break e;default:i=!1}}i&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return rt(t),null;case 6:if(e&&t.stateNode!=null)Bf(e,t,e.memoizedProps,i);else{if(typeof i!="string"&&t.stateNode===null)throw Error(s(166));if(o=fr(os.current),fr(Gt.current),fi(t)){if(i=t.stateNode,o=t.memoizedProps,i[Kt]=t,(c=i.nodeValue!==o)&&(e=wt,e!==null))switch(e.tag){case 3:oi(i.nodeValue,o,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&oi(i.nodeValue,o,(e.mode&1)!==0)}c&&(t.flags|=4)}else i=(o.nodeType===9?o:o.ownerDocument).createTextNode(i),i[Kt]=t,t.stateNode=i}return rt(t),null;case 13:if(_e(Le),i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(De&&St!==null&&(t.mode&1)!==0&&(t.flags&128)===0)qd(),Yr(),t.flags|=98560,c=!1;else if(c=fi(t),i!==null&&i.dehydrated!==null){if(e===null){if(!c)throw Error(s(318));if(c=t.memoizedState,c=c!==null?c.dehydrated:null,!c)throw Error(s(317));c[Kt]=t}else Yr(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;rt(t),c=!1}else Lt!==null&&(ou(Lt),Lt=null),c=!0;if(!c)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=o,t):(i=i!==null,i!==(e!==null&&e.memoizedState!==null)&&i&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Le.current&1)!==0?Ve===0&&(Ve=3):lu())),t.updateQueue!==null&&(t.flags|=4),rt(t),null);case 4:return Jr(),qa(e,t),e===null&&Xo(t.stateNode.containerInfo),rt(t),null;case 10:return Sa(t.type._context),rt(t),null;case 17:return dt(t.type)&&li(),rt(t),null;case 19:if(_e(Le),c=t.memoizedState,c===null)return rt(t),null;if(i=(t.flags&128)!==0,h=c.rendering,h===null)if(i)us(c,!1);else{if(Ve!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(h=vi(e),h!==null){for(t.flags|=128,us(c,!1),i=h.updateQueue,i!==null&&(t.updateQueue=i,t.flags|=4),t.subtreeFlags=0,i=o,o=t.child;o!==null;)c=o,e=i,c.flags&=14680066,h=c.alternate,h===null?(c.childLanes=0,c.lanes=e,c.child=null,c.subtreeFlags=0,c.memoizedProps=null,c.memoizedState=null,c.updateQueue=null,c.dependencies=null,c.stateNode=null):(c.childLanes=h.childLanes,c.lanes=h.lanes,c.child=h.child,c.subtreeFlags=0,c.deletions=null,c.memoizedProps=h.memoizedProps,c.memoizedState=h.memoizedState,c.updateQueue=h.updateQueue,c.type=h.type,e=h.dependencies,c.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),o=o.sibling;return je(Le,Le.current&1|2),t.child}e=e.sibling}c.tail!==null&&We()>no&&(t.flags|=128,i=!0,us(c,!1),t.lanes=4194304)}else{if(!i)if(e=vi(h),e!==null){if(t.flags|=128,i=!0,o=e.updateQueue,o!==null&&(t.updateQueue=o,t.flags|=4),us(c,!0),c.tail===null&&c.tailMode==="hidden"&&!h.alternate&&!De)return rt(t),null}else 2*We()-c.renderingStartTime>no&&o!==1073741824&&(t.flags|=128,i=!0,us(c,!1),t.lanes=4194304);c.isBackwards?(h.sibling=t.child,t.child=h):(o=c.last,o!==null?o.sibling=h:t.child=h,c.last=h)}return c.tail!==null?(t=c.tail,c.rendering=t,c.tail=t.sibling,c.renderingStartTime=We(),t.sibling=null,o=Le.current,je(Le,i?o&1|2:o&1),t):(rt(t),null);case 22:case 23:return iu(),i=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==i&&(t.flags|=8192),i&&(t.mode&1)!==0?(Ct&1073741824)!==0&&(rt(t),t.subtreeFlags&6&&(t.flags|=8192)):rt(t),null;case 24:return null;case 25:return null}throw Error(s(156,t.tag))}function ev(e,t){switch(ma(t),t.tag){case 1:return dt(t.type)&&li(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Jr(),_e(ct),_e(tt),Ta(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return Pa(t),null;case 13:if(_e(Le),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Yr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return _e(Le),null;case 4:return Jr(),null;case 10:return Sa(t.type._context),null;case 22:case 23:return iu(),null;case 24:return null;default:return null}}var Ni=!1,ot=!1,tv=typeof WeakSet=="function"?WeakSet:Set,Z=null;function eo(e,t){var o=e.ref;if(o!==null)if(typeof o=="function")try{o(null)}catch(i){Fe(e,t,i)}else o.current=null}function Ya(e,t,o){try{o()}catch(i){Fe(e,t,i)}}var Qf=!1;function nv(e,t){if(ia=qs,e=Ed(),Jl(e)){if("selectionStart"in e)var o={start:e.selectionStart,end:e.selectionEnd};else e:{o=(o=e.ownerDocument)&&o.defaultView||window;var i=o.getSelection&&o.getSelection();if(i&&i.rangeCount!==0){o=i.anchorNode;var a=i.anchorOffset,c=i.focusNode;i=i.focusOffset;try{o.nodeType,c.nodeType}catch{o=null;break e}var h=0,w=-1,b=-1,_=0,U=0,$=e,W=null;t:for(;;){for(var G;$!==o||a!==0&&$.nodeType!==3||(w=h+a),$!==c||i!==0&&$.nodeType!==3||(b=h+i),$.nodeType===3&&(h+=$.nodeValue.length),(G=$.firstChild)!==null;)W=$,$=G;for(;;){if($===e)break t;if(W===o&&++_===a&&(w=h),W===c&&++U===i&&(b=h),(G=$.nextSibling)!==null)break;$=W,W=$.parentNode}$=G}o=w===-1||b===-1?null:{start:w,end:b}}else o=null}o=o||{start:0,end:0}}else o=null;for(la={focusedElem:e,selectionRange:o},qs=!1,Z=t;Z!==null;)if(t=Z,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,Z=e;else for(;Z!==null;){t=Z;try{var te=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(te!==null){var ne=te.memoizedProps,Ue=te.memoizedState,O=t.stateNode,T=O.getSnapshotBeforeUpdate(t.elementType===t.type?ne:It(t.type,ne),Ue);O.__reactInternalSnapshotBeforeUpdate=T}break;case 3:var M=t.stateNode.containerInfo;M.nodeType===1?M.textContent="":M.nodeType===9&&M.documentElement&&M.removeChild(M.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(s(163))}}catch(V){Fe(t,t.return,V)}if(e=t.sibling,e!==null){e.return=t.return,Z=e;break}Z=t.return}return te=Qf,Qf=!1,te}function cs(e,t,o){var i=t.updateQueue;if(i=i!==null?i.lastEffect:null,i!==null){var a=i=i.next;do{if((a.tag&e)===e){var c=a.destroy;a.destroy=void 0,c!==void 0&&Ya(t,o,c)}a=a.next}while(a!==i)}}function Ti(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var o=t=t.next;do{if((o.tag&e)===e){var i=o.create;o.destroy=i()}o=o.next}while(o!==t)}}function Ka(e){var t=e.ref;if(t!==null){var o=e.stateNode;switch(e.tag){case 5:e=o;break;default:e=o}typeof t=="function"?t(e):t.current=e}}function qf(e){var t=e.alternate;t!==null&&(e.alternate=null,qf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Kt],delete t[Zo],delete t[da],delete t[Fy],delete t[zy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Yf(e){return e.tag===5||e.tag===3||e.tag===4}function Kf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Yf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ga(e,t,o){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?o.nodeType===8?o.parentNode.insertBefore(e,t):o.insertBefore(e,t):(o.nodeType===8?(t=o.parentNode,t.insertBefore(e,o)):(t=o,t.appendChild(e)),o=o._reactRootContainer,o!=null||t.onclick!==null||(t.onclick=si));else if(i!==4&&(e=e.child,e!==null))for(Ga(e,t,o),e=e.sibling;e!==null;)Ga(e,t,o),e=e.sibling}function Xa(e,t,o){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?o.insertBefore(e,t):o.appendChild(e);else if(i!==4&&(e=e.child,e!==null))for(Xa(e,t,o),e=e.sibling;e!==null;)Xa(e,t,o),e=e.sibling}var Xe=null,Ft=!1;function Dn(e,t,o){for(o=o.child;o!==null;)Gf(e,t,o),o=o.sibling}function Gf(e,t,o){if(Yt&&typeof Yt.onCommitFiberUnmount=="function")try{Yt.onCommitFiberUnmount(Us,o)}catch{}switch(o.tag){case 5:ot||eo(o,t);case 6:var i=Xe,a=Ft;Xe=null,Dn(e,t,o),Xe=i,Ft=a,Xe!==null&&(Ft?(e=Xe,o=o.stateNode,e.nodeType===8?e.parentNode.removeChild(o):e.removeChild(o)):Xe.removeChild(o.stateNode));break;case 18:Xe!==null&&(Ft?(e=Xe,o=o.stateNode,e.nodeType===8?ca(e.parentNode,o):e.nodeType===1&&ca(e,o),$o(e)):ca(Xe,o.stateNode));break;case 4:i=Xe,a=Ft,Xe=o.stateNode.containerInfo,Ft=!0,Dn(e,t,o),Xe=i,Ft=a;break;case 0:case 11:case 14:case 15:if(!ot&&(i=o.updateQueue,i!==null&&(i=i.lastEffect,i!==null))){a=i=i.next;do{var c=a,h=c.destroy;c=c.tag,h!==void 0&&((c&2)!==0||(c&4)!==0)&&Ya(o,t,h),a=a.next}while(a!==i)}Dn(e,t,o);break;case 1:if(!ot&&(eo(o,t),i=o.stateNode,typeof i.componentWillUnmount=="function"))try{i.props=o.memoizedProps,i.state=o.memoizedState,i.componentWillUnmount()}catch(w){Fe(o,t,w)}Dn(e,t,o);break;case 21:Dn(e,t,o);break;case 22:o.mode&1?(ot=(i=ot)||o.memoizedState!==null,Dn(e,t,o),ot=i):Dn(e,t,o);break;default:Dn(e,t,o)}}function Xf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var o=e.stateNode;o===null&&(o=e.stateNode=new tv),t.forEach(function(i){var a=dv.bind(null,e,i);o.has(i)||(o.add(i),i.then(a,a))})}}function zt(e,t){var o=t.deletions;if(o!==null)for(var i=0;i<o.length;i++){var a=o[i];try{var c=e,h=t,w=h;e:for(;w!==null;){switch(w.tag){case 5:Xe=w.stateNode,Ft=!1;break e;case 3:Xe=w.stateNode.containerInfo,Ft=!0;break e;case 4:Xe=w.stateNode.containerInfo,Ft=!0;break e}w=w.return}if(Xe===null)throw Error(s(160));Gf(c,h,a),Xe=null,Ft=!1;var b=a.alternate;b!==null&&(b.return=null),a.return=null}catch(_){Fe(a,t,_)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Jf(t,e),t=t.sibling}function Jf(e,t){var o=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(zt(t,e),Jt(e),i&4){try{cs(3,e,e.return),Ti(3,e)}catch(ne){Fe(e,e.return,ne)}try{cs(5,e,e.return)}catch(ne){Fe(e,e.return,ne)}}break;case 1:zt(t,e),Jt(e),i&512&&o!==null&&eo(o,o.return);break;case 5:if(zt(t,e),Jt(e),i&512&&o!==null&&eo(o,o.return),e.flags&32){var a=e.stateNode;try{jo(a,"")}catch(ne){Fe(e,e.return,ne)}}if(i&4&&(a=e.stateNode,a!=null)){var c=e.memoizedProps,h=o!==null?o.memoizedProps:c,w=e.type,b=e.updateQueue;if(e.updateQueue=null,b!==null)try{w==="input"&&c.type==="radio"&&c.name!=null&&Ds(a,c),Tl(w,h);var _=Tl(w,c);for(h=0;h<b.length;h+=2){var U=b[h],$=b[h+1];U==="style"?_c(a,$):U==="dangerouslySetInnerHTML"?jc(a,$):U==="children"?jo(a,$):I(a,U,$,_)}switch(w){case"input":Ro(a,c);break;case"textarea":Tc(a,c);break;case"select":var W=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!c.multiple;var G=c.value;G!=null?_r(a,!!c.multiple,G,!1):W!==!!c.multiple&&(c.defaultValue!=null?_r(a,!!c.multiple,c.defaultValue,!0):_r(a,!!c.multiple,c.multiple?[]:"",!1))}a[Zo]=c}catch(ne){Fe(e,e.return,ne)}}break;case 6:if(zt(t,e),Jt(e),i&4){if(e.stateNode===null)throw Error(s(162));a=e.stateNode,c=e.memoizedProps;try{a.nodeValue=c}catch(ne){Fe(e,e.return,ne)}}break;case 3:if(zt(t,e),Jt(e),i&4&&o!==null&&o.memoizedState.isDehydrated)try{$o(t.containerInfo)}catch(ne){Fe(e,e.return,ne)}break;case 4:zt(t,e),Jt(e);break;case 13:zt(t,e),Jt(e),a=e.child,a.flags&8192&&(c=a.memoizedState!==null,a.stateNode.isHidden=c,!c||a.alternate!==null&&a.alternate.memoizedState!==null||(eu=We())),i&4&&Xf(e);break;case 22:if(U=o!==null&&o.memoizedState!==null,e.mode&1?(ot=(_=ot)||U,zt(t,e),ot=_):zt(t,e),Jt(e),i&8192){if(_=e.memoizedState!==null,(e.stateNode.isHidden=_)&&!U&&(e.mode&1)!==0)for(Z=e,U=e.child;U!==null;){for($=Z=U;Z!==null;){switch(W=Z,G=W.child,W.tag){case 0:case 11:case 14:case 15:cs(4,W,W.return);break;case 1:eo(W,W.return);var te=W.stateNode;if(typeof te.componentWillUnmount=="function"){i=W,o=W.return;try{t=i,te.props=t.memoizedProps,te.state=t.memoizedState,te.componentWillUnmount()}catch(ne){Fe(i,o,ne)}}break;case 5:eo(W,W.return);break;case 22:if(W.memoizedState!==null){th($);continue}}G!==null?(G.return=W,Z=G):th($)}U=U.sibling}e:for(U=null,$=e;;){if($.tag===5){if(U===null){U=$;try{a=$.stateNode,_?(c=a.style,typeof c.setProperty=="function"?c.setProperty("display","none","important"):c.display="none"):(w=$.stateNode,b=$.memoizedProps.style,h=b!=null&&b.hasOwnProperty("display")?b.display:null,w.style.display=Mc("display",h))}catch(ne){Fe(e,e.return,ne)}}}else if($.tag===6){if(U===null)try{$.stateNode.nodeValue=_?"":$.memoizedProps}catch(ne){Fe(e,e.return,ne)}}else if(($.tag!==22&&$.tag!==23||$.memoizedState===null||$===e)&&$.child!==null){$.child.return=$,$=$.child;continue}if($===e)break e;for(;$.sibling===null;){if($.return===null||$.return===e)break e;U===$&&(U=null),$=$.return}U===$&&(U=null),$.sibling.return=$.return,$=$.sibling}}break;case 19:zt(t,e),Jt(e),i&4&&Xf(e);break;case 21:break;default:zt(t,e),Jt(e)}}function Jt(e){var t=e.flags;if(t&2){try{e:{for(var o=e.return;o!==null;){if(Yf(o)){var i=o;break e}o=o.return}throw Error(s(160))}switch(i.tag){case 5:var a=i.stateNode;i.flags&32&&(jo(a,""),i.flags&=-33);var c=Kf(e);Xa(e,c,a);break;case 3:case 4:var h=i.stateNode.containerInfo,w=Kf(e);Ga(e,w,h);break;default:throw Error(s(161))}}catch(b){Fe(e,e.return,b)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function rv(e,t,o){Z=e,Zf(e)}function Zf(e,t,o){for(var i=(e.mode&1)!==0;Z!==null;){var a=Z,c=a.child;if(a.tag===22&&i){var h=a.memoizedState!==null||Ni;if(!h){var w=a.alternate,b=w!==null&&w.memoizedState!==null||ot;w=Ni;var _=ot;if(Ni=h,(ot=b)&&!_)for(Z=a;Z!==null;)h=Z,b=h.child,h.tag===22&&h.memoizedState!==null?nh(a):b!==null?(b.return=h,Z=b):nh(a);for(;c!==null;)Z=c,Zf(c),c=c.sibling;Z=a,Ni=w,ot=_}eh(e)}else(a.subtreeFlags&8772)!==0&&c!==null?(c.return=a,Z=c):eh(e)}}function eh(e){for(;Z!==null;){var t=Z;if((t.flags&8772)!==0){var o=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:ot||Ti(5,t);break;case 1:var i=t.stateNode;if(t.flags&4&&!ot)if(o===null)i.componentDidMount();else{var a=t.elementType===t.type?o.memoizedProps:It(t.type,o.memoizedProps);i.componentDidUpdate(a,o.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var c=t.updateQueue;c!==null&&ef(t,c,i);break;case 3:var h=t.updateQueue;if(h!==null){if(o=null,t.child!==null)switch(t.child.tag){case 5:o=t.child.stateNode;break;case 1:o=t.child.stateNode}ef(t,h,o)}break;case 5:var w=t.stateNode;if(o===null&&t.flags&4){o=w;var b=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":b.autoFocus&&o.focus();break;case"img":b.src&&(o.src=b.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var _=t.alternate;if(_!==null){var U=_.memoizedState;if(U!==null){var $=U.dehydrated;$!==null&&$o($)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(s(163))}ot||t.flags&512&&Ka(t)}catch(W){Fe(t,t.return,W)}}if(t===e){Z=null;break}if(o=t.sibling,o!==null){o.return=t.return,Z=o;break}Z=t.return}}function th(e){for(;Z!==null;){var t=Z;if(t===e){Z=null;break}var o=t.sibling;if(o!==null){o.return=t.return,Z=o;break}Z=t.return}}function nh(e){for(;Z!==null;){var t=Z;try{switch(t.tag){case 0:case 11:case 15:var o=t.return;try{Ti(4,t)}catch(b){Fe(t,o,b)}break;case 1:var i=t.stateNode;if(typeof i.componentDidMount=="function"){var a=t.return;try{i.componentDidMount()}catch(b){Fe(t,a,b)}}var c=t.return;try{Ka(t)}catch(b){Fe(t,c,b)}break;case 5:var h=t.return;try{Ka(t)}catch(b){Fe(t,h,b)}}}catch(b){Fe(t,t.return,b)}if(t===e){Z=null;break}var w=t.sibling;if(w!==null){w.return=t.return,Z=w;break}Z=t.return}}var ov=Math.ceil,Ri=F.ReactCurrentDispatcher,Ja=F.ReactCurrentOwner,Ot=F.ReactCurrentBatchConfig,Ee=0,Ke=null,$e=null,Je=0,Ct=0,to=On(0),Ve=0,ds=null,pr=0,Oi=0,Za=0,fs=null,ht=null,eu=0,no=1/0,mn=null,ji=!1,tu=null,Ln=null,Mi=!1,In=null,_i=0,hs=0,nu=null,Ai=-1,Di=0;function lt(){return(Ee&6)!==0?We():Ai!==-1?Ai:Ai=We()}function Fn(e){return(e.mode&1)===0?1:(Ee&2)!==0&&Je!==0?Je&-Je:Uy.transition!==null?(Di===0&&(Di=Yc()),Di):(e=Te,e!==0||(e=window.event,e=e===void 0?16:rd(e.type)),e)}function Wt(e,t,o,i){if(50<hs)throw hs=0,nu=null,Error(s(185));Io(e,o,i),((Ee&2)===0||e!==Ke)&&(e===Ke&&((Ee&2)===0&&(Oi|=o),Ve===4&&zn(e,Je)),pt(e,i),o===1&&Ee===0&&(t.mode&1)===0&&(no=We()+500,ui&&Mn()))}function pt(e,t){var o=e.callbackNode;Ug(e,t);var i=Vs(e,e===Ke?Je:0);if(i===0)o!==null&&Bc(o),e.callbackNode=null,e.callbackPriority=0;else if(t=i&-i,e.callbackPriority!==t){if(o!=null&&Bc(o),t===1)e.tag===0?Wy(oh.bind(null,e)):$d(oh.bind(null,e)),Ly(function(){(Ee&6)===0&&Mn()}),o=null;else{switch(Kc(i)){case 1:o=Dl;break;case 4:o=Qc;break;case 16:o=Ws;break;case 536870912:o=qc;break;default:o=Ws}o=fh(o,rh.bind(null,e))}e.callbackPriority=t,e.callbackNode=o}}function rh(e,t){if(Ai=-1,Di=0,(Ee&6)!==0)throw Error(s(327));var o=e.callbackNode;if(ro()&&e.callbackNode!==o)return null;var i=Vs(e,e===Ke?Je:0);if(i===0)return null;if((i&30)!==0||(i&e.expiredLanes)!==0||t)t=Li(e,i);else{t=i;var a=Ee;Ee|=2;var c=ih();(Ke!==e||Je!==t)&&(mn=null,no=We()+500,gr(e,t));do try{lv();break}catch(w){sh(e,w)}while(!0);wa(),Ri.current=c,Ee=a,$e!==null?t=0:(Ke=null,Je=0,t=Ve)}if(t!==0){if(t===2&&(a=Ll(e),a!==0&&(i=a,t=ru(e,a))),t===1)throw o=ds,gr(e,0),zn(e,i),pt(e,We()),o;if(t===6)zn(e,i);else{if(a=e.current.alternate,(i&30)===0&&!sv(a)&&(t=Li(e,i),t===2&&(c=Ll(e),c!==0&&(i=c,t=ru(e,c))),t===1))throw o=ds,gr(e,0),zn(e,i),pt(e,We()),o;switch(e.finishedWork=a,e.finishedLanes=i,t){case 0:case 1:throw Error(s(345));case 2:yr(e,ht,mn);break;case 3:if(zn(e,i),(i&130023424)===i&&(t=eu+500-We(),10<t)){if(Vs(e,0)!==0)break;if(a=e.suspendedLanes,(a&i)!==i){lt(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ua(yr.bind(null,e,ht,mn),t);break}yr(e,ht,mn);break;case 4:if(zn(e,i),(i&4194240)===i)break;for(t=e.eventTimes,a=-1;0<i;){var h=31-At(i);c=1<<h,h=t[h],h>a&&(a=h),i&=~c}if(i=a,i=We()-i,i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*ov(i/1960))-i,10<i){e.timeoutHandle=ua(yr.bind(null,e,ht,mn),i);break}yr(e,ht,mn);break;case 5:yr(e,ht,mn);break;default:throw Error(s(329))}}}return pt(e,We()),e.callbackNode===o?rh.bind(null,e):null}function ru(e,t){var o=fs;return e.current.memoizedState.isDehydrated&&(gr(e,t).flags|=256),e=Li(e,t),e!==2&&(t=ht,ht=o,t!==null&&ou(t)),e}function ou(e){ht===null?ht=e:ht.push.apply(ht,e)}function sv(e){for(var t=e;;){if(t.flags&16384){var o=t.updateQueue;if(o!==null&&(o=o.stores,o!==null))for(var i=0;i<o.length;i++){var a=o[i],c=a.getSnapshot;a=a.value;try{if(!Dt(c(),a))return!1}catch{return!1}}}if(o=t.child,t.subtreeFlags&16384&&o!==null)o.return=t,t=o;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function zn(e,t){for(t&=~Za,t&=~Oi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var o=31-At(t),i=1<<o;e[o]=-1,t&=~i}}function oh(e){if((Ee&6)!==0)throw Error(s(327));ro();var t=Vs(e,0);if((t&1)===0)return pt(e,We()),null;var o=Li(e,t);if(e.tag!==0&&o===2){var i=Ll(e);i!==0&&(t=i,o=ru(e,i))}if(o===1)throw o=ds,gr(e,0),zn(e,t),pt(e,We()),o;if(o===6)throw Error(s(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,yr(e,ht,mn),pt(e,We()),null}function su(e,t){var o=Ee;Ee|=1;try{return e(t)}finally{Ee=o,Ee===0&&(no=We()+500,ui&&Mn())}}function mr(e){In!==null&&In.tag===0&&(Ee&6)===0&&ro();var t=Ee;Ee|=1;var o=Ot.transition,i=Te;try{if(Ot.transition=null,Te=1,e)return e()}finally{Te=i,Ot.transition=o,Ee=t,(Ee&6)===0&&Mn()}}function iu(){Ct=to.current,_e(to)}function gr(e,t){e.finishedWork=null,e.finishedLanes=0;var o=e.timeoutHandle;if(o!==-1&&(e.timeoutHandle=-1,Dy(o)),$e!==null)for(o=$e.return;o!==null;){var i=o;switch(ma(i),i.tag){case 1:i=i.type.childContextTypes,i!=null&&li();break;case 3:Jr(),_e(ct),_e(tt),Ta();break;case 5:Pa(i);break;case 4:Jr();break;case 13:_e(Le);break;case 19:_e(Le);break;case 10:Sa(i.type._context);break;case 22:case 23:iu()}o=o.return}if(Ke=e,$e=e=Wn(e.current,null),Je=Ct=t,Ve=0,ds=null,Za=Oi=pr=0,ht=fs=null,dr!==null){for(t=0;t<dr.length;t++)if(o=dr[t],i=o.interleaved,i!==null){o.interleaved=null;var a=i.next,c=o.pending;if(c!==null){var h=c.next;c.next=a,i.next=h}o.pending=i}dr=null}return e}function sh(e,t){do{var o=$e;try{if(wa(),xi.current=Ei,wi){for(var i=Ie.memoizedState;i!==null;){var a=i.queue;a!==null&&(a.pending=null),i=i.next}wi=!1}if(hr=0,Ye=He=Ie=null,ss=!1,is=0,Ja.current=null,o===null||o.return===null){Ve=1,ds=t,$e=null;break}e:{var c=e,h=o.return,w=o,b=t;if(t=Je,w.flags|=32768,b!==null&&typeof b=="object"&&typeof b.then=="function"){var _=b,U=w,$=U.tag;if((U.mode&1)===0&&($===0||$===11||$===15)){var W=U.alternate;W?(U.updateQueue=W.updateQueue,U.memoizedState=W.memoizedState,U.lanes=W.lanes):(U.updateQueue=null,U.memoizedState=null)}var G=Of(h);if(G!==null){G.flags&=-257,jf(G,h,w,c,t),G.mode&1&&Rf(c,_,t),t=G,b=_;var te=t.updateQueue;if(te===null){var ne=new Set;ne.add(b),t.updateQueue=ne}else te.add(b);break e}else{if((t&1)===0){Rf(c,_,t),lu();break e}b=Error(s(426))}}else if(De&&w.mode&1){var Ue=Of(h);if(Ue!==null){(Ue.flags&65536)===0&&(Ue.flags|=256),jf(Ue,h,w,c,t),va(Zr(b,w));break e}}c=b=Zr(b,w),Ve!==4&&(Ve=2),fs===null?fs=[c]:fs.push(c),c=h;do{switch(c.tag){case 3:c.flags|=65536,t&=-t,c.lanes|=t;var O=Nf(c,b,t);Zd(c,O);break e;case 1:w=b;var T=c.type,M=c.stateNode;if((c.flags&128)===0&&(typeof T.getDerivedStateFromError=="function"||M!==null&&typeof M.componentDidCatch=="function"&&(Ln===null||!Ln.has(M)))){c.flags|=65536,t&=-t,c.lanes|=t;var V=Tf(c,w,t);Zd(c,V);break e}}c=c.return}while(c!==null)}ah(o)}catch(oe){t=oe,$e===o&&o!==null&&($e=o=o.return);continue}break}while(!0)}function ih(){var e=Ri.current;return Ri.current=Ei,e===null?Ei:e}function lu(){(Ve===0||Ve===3||Ve===2)&&(Ve=4),Ke===null||(pr&268435455)===0&&(Oi&268435455)===0||zn(Ke,Je)}function Li(e,t){var o=Ee;Ee|=2;var i=ih();(Ke!==e||Je!==t)&&(mn=null,gr(e,t));do try{iv();break}catch(a){sh(e,a)}while(!0);if(wa(),Ee=o,Ri.current=i,$e!==null)throw Error(s(261));return Ke=null,Je=0,Ve}function iv(){for(;$e!==null;)lh($e)}function lv(){for(;$e!==null&&!Mg();)lh($e)}function lh(e){var t=dh(e.alternate,e,Ct);e.memoizedProps=e.pendingProps,t===null?ah(e):$e=t,Ja.current=null}function ah(e){var t=e;do{var o=t.alternate;if(e=t.return,(t.flags&32768)===0){if(o=Zy(o,t,Ct),o!==null){$e=o;return}}else{if(o=ev(o,t),o!==null){o.flags&=32767,$e=o;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ve=6,$e=null;return}}if(t=t.sibling,t!==null){$e=t;return}$e=t=e}while(t!==null);Ve===0&&(Ve=5)}function yr(e,t,o){var i=Te,a=Ot.transition;try{Ot.transition=null,Te=1,av(e,t,o,i)}finally{Ot.transition=a,Te=i}return null}function av(e,t,o,i){do ro();while(In!==null);if((Ee&6)!==0)throw Error(s(327));o=e.finishedWork;var a=e.finishedLanes;if(o===null)return null;if(e.finishedWork=null,e.finishedLanes=0,o===e.current)throw Error(s(177));e.callbackNode=null,e.callbackPriority=0;var c=o.lanes|o.childLanes;if($g(e,c),e===Ke&&($e=Ke=null,Je=0),(o.subtreeFlags&2064)===0&&(o.flags&2064)===0||Mi||(Mi=!0,fh(Ws,function(){return ro(),null})),c=(o.flags&15990)!==0,(o.subtreeFlags&15990)!==0||c){c=Ot.transition,Ot.transition=null;var h=Te;Te=1;var w=Ee;Ee|=4,Ja.current=null,nv(e,o),Jf(o,e),Ty(la),qs=!!ia,la=ia=null,e.current=o,rv(o),_g(),Ee=w,Te=h,Ot.transition=c}else e.current=o;if(Mi&&(Mi=!1,In=e,_i=a),c=e.pendingLanes,c===0&&(Ln=null),Lg(o.stateNode),pt(e,We()),t!==null)for(i=e.onRecoverableError,o=0;o<t.length;o++)a=t[o],i(a.value,{componentStack:a.stack,digest:a.digest});if(ji)throw ji=!1,e=tu,tu=null,e;return(_i&1)!==0&&e.tag!==0&&ro(),c=e.pendingLanes,(c&1)!==0?e===nu?hs++:(hs=0,nu=e):hs=0,Mn(),null}function ro(){if(In!==null){var e=Kc(_i),t=Ot.transition,o=Te;try{if(Ot.transition=null,Te=16>e?16:e,In===null)var i=!1;else{if(e=In,In=null,_i=0,(Ee&6)!==0)throw Error(s(331));var a=Ee;for(Ee|=4,Z=e.current;Z!==null;){var c=Z,h=c.child;if((Z.flags&16)!==0){var w=c.deletions;if(w!==null){for(var b=0;b<w.length;b++){var _=w[b];for(Z=_;Z!==null;){var U=Z;switch(U.tag){case 0:case 11:case 15:cs(8,U,c)}var $=U.child;if($!==null)$.return=U,Z=$;else for(;Z!==null;){U=Z;var W=U.sibling,G=U.return;if(qf(U),U===_){Z=null;break}if(W!==null){W.return=G,Z=W;break}Z=G}}}var te=c.alternate;if(te!==null){var ne=te.child;if(ne!==null){te.child=null;do{var Ue=ne.sibling;ne.sibling=null,ne=Ue}while(ne!==null)}}Z=c}}if((c.subtreeFlags&2064)!==0&&h!==null)h.return=c,Z=h;else e:for(;Z!==null;){if(c=Z,(c.flags&2048)!==0)switch(c.tag){case 0:case 11:case 15:cs(9,c,c.return)}var O=c.sibling;if(O!==null){O.return=c.return,Z=O;break e}Z=c.return}}var T=e.current;for(Z=T;Z!==null;){h=Z;var M=h.child;if((h.subtreeFlags&2064)!==0&&M!==null)M.return=h,Z=M;else e:for(h=T;Z!==null;){if(w=Z,(w.flags&2048)!==0)try{switch(w.tag){case 0:case 11:case 15:Ti(9,w)}}catch(oe){Fe(w,w.return,oe)}if(w===h){Z=null;break e}var V=w.sibling;if(V!==null){V.return=w.return,Z=V;break e}Z=w.return}}if(Ee=a,Mn(),Yt&&typeof Yt.onPostCommitFiberRoot=="function")try{Yt.onPostCommitFiberRoot(Us,e)}catch{}i=!0}return i}finally{Te=o,Ot.transition=t}}return!1}function uh(e,t,o){t=Zr(o,t),t=Nf(e,t,1),e=An(e,t,1),t=lt(),e!==null&&(Io(e,1,t),pt(e,t))}function Fe(e,t,o){if(e.tag===3)uh(e,e,o);else for(;t!==null;){if(t.tag===3){uh(t,e,o);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Ln===null||!Ln.has(i))){e=Zr(o,e),e=Tf(t,e,1),t=An(t,e,1),e=lt(),t!==null&&(Io(t,1,e),pt(t,e));break}}t=t.return}}function uv(e,t,o){var i=e.pingCache;i!==null&&i.delete(t),t=lt(),e.pingedLanes|=e.suspendedLanes&o,Ke===e&&(Je&o)===o&&(Ve===4||Ve===3&&(Je&130023424)===Je&&500>We()-eu?gr(e,0):Za|=o),pt(e,t)}function ch(e,t){t===0&&((e.mode&1)===0?t=1:(t=Hs,Hs<<=1,(Hs&130023424)===0&&(Hs=4194304)));var o=lt();e=fn(e,t),e!==null&&(Io(e,t,o),pt(e,o))}function cv(e){var t=e.memoizedState,o=0;t!==null&&(o=t.retryLane),ch(e,o)}function dv(e,t){var o=0;switch(e.tag){case 13:var i=e.stateNode,a=e.memoizedState;a!==null&&(o=a.retryLane);break;case 19:i=e.stateNode;break;default:throw Error(s(314))}i!==null&&i.delete(t),ch(e,o)}var dh;dh=function(e,t,o){if(e!==null)if(e.memoizedProps!==t.pendingProps||ct.current)ft=!0;else{if((e.lanes&o)===0&&(t.flags&128)===0)return ft=!1,Jy(e,t,o);ft=(e.flags&131072)!==0}else ft=!1,De&&(t.flags&1048576)!==0&&Hd(t,di,t.index);switch(t.lanes=0,t.tag){case 2:var i=t.type;Pi(e,t),e=t.pendingProps;var a=Br(t,tt.current);Xr(t,o),a=ja(null,t,i,e,a,o);var c=Ma();return t.flags|=1,typeof a=="object"&&a!==null&&typeof a.render=="function"&&a.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,dt(i)?(c=!0,ai(t)):c=!1,t.memoizedState=a.state!==null&&a.state!==void 0?a.state:null,ka(t),a.updater=ki,t.stateNode=a,a._reactInternals=t,Fa(t,i,e,o),t=$a(null,t,i,!0,c,o)):(t.tag=0,De&&c&&pa(t),it(null,t,a,o),t=t.child),t;case 16:i=t.elementType;e:{switch(Pi(e,t),e=t.pendingProps,a=i._init,i=a(i._payload),t.type=i,a=t.tag=hv(i),e=It(i,e),a){case 0:t=Ua(null,t,i,e,o);break e;case 1:t=If(null,t,i,e,o);break e;case 11:t=Mf(null,t,i,e,o);break e;case 14:t=_f(null,t,i,It(i.type,e),o);break e}throw Error(s(306,i,""))}return t;case 0:return i=t.type,a=t.pendingProps,a=t.elementType===i?a:It(i,a),Ua(e,t,i,a,o);case 1:return i=t.type,a=t.pendingProps,a=t.elementType===i?a:It(i,a),If(e,t,i,a,o);case 3:e:{if(Ff(t),e===null)throw Error(s(387));i=t.pendingProps,c=t.memoizedState,a=c.element,Jd(e,t),yi(t,i,null,o);var h=t.memoizedState;if(i=h.element,c.isDehydrated)if(c={element:i,isDehydrated:!1,cache:h.cache,pendingSuspenseBoundaries:h.pendingSuspenseBoundaries,transitions:h.transitions},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){a=Zr(Error(s(423)),t),t=zf(e,t,i,o,a);break e}else if(i!==a){a=Zr(Error(s(424)),t),t=zf(e,t,i,o,a);break e}else for(St=Rn(t.stateNode.containerInfo.firstChild),wt=t,De=!0,Lt=null,o=Gd(t,null,i,o),t.child=o;o;)o.flags=o.flags&-3|4096,o=o.sibling;else{if(Yr(),i===a){t=pn(e,t,o);break e}it(e,t,i,o)}t=t.child}return t;case 5:return tf(t),e===null&&ya(t),i=t.type,a=t.pendingProps,c=e!==null?e.memoizedProps:null,h=a.children,aa(i,a)?h=null:c!==null&&aa(i,c)&&(t.flags|=32),Lf(e,t),it(e,t,h,o),t.child;case 6:return e===null&&ya(t),null;case 13:return Wf(e,t,o);case 4:return ba(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=Kr(t,null,i,o):it(e,t,i,o),t.child;case 11:return i=t.type,a=t.pendingProps,a=t.elementType===i?a:It(i,a),Mf(e,t,i,a,o);case 7:return it(e,t,t.pendingProps,o),t.child;case 8:return it(e,t,t.pendingProps.children,o),t.child;case 12:return it(e,t,t.pendingProps.children,o),t.child;case 10:e:{if(i=t.type._context,a=t.pendingProps,c=t.memoizedProps,h=a.value,je(pi,i._currentValue),i._currentValue=h,c!==null)if(Dt(c.value,h)){if(c.children===a.children&&!ct.current){t=pn(e,t,o);break e}}else for(c=t.child,c!==null&&(c.return=t);c!==null;){var w=c.dependencies;if(w!==null){h=c.child;for(var b=w.firstContext;b!==null;){if(b.context===i){if(c.tag===1){b=hn(-1,o&-o),b.tag=2;var _=c.updateQueue;if(_!==null){_=_.shared;var U=_.pending;U===null?b.next=b:(b.next=U.next,U.next=b),_.pending=b}}c.lanes|=o,b=c.alternate,b!==null&&(b.lanes|=o),Ca(c.return,o,t),w.lanes|=o;break}b=b.next}}else if(c.tag===10)h=c.type===t.type?null:c.child;else if(c.tag===18){if(h=c.return,h===null)throw Error(s(341));h.lanes|=o,w=h.alternate,w!==null&&(w.lanes|=o),Ca(h,o,t),h=c.sibling}else h=c.child;if(h!==null)h.return=c;else for(h=c;h!==null;){if(h===t){h=null;break}if(c=h.sibling,c!==null){c.return=h.return,h=c;break}h=h.return}c=h}it(e,t,a.children,o),t=t.child}return t;case 9:return a=t.type,i=t.pendingProps.children,Xr(t,o),a=Tt(a),i=i(a),t.flags|=1,it(e,t,i,o),t.child;case 14:return i=t.type,a=It(i,t.pendingProps),a=It(i.type,a),_f(e,t,i,a,o);case 15:return Af(e,t,t.type,t.pendingProps,o);case 17:return i=t.type,a=t.pendingProps,a=t.elementType===i?a:It(i,a),Pi(e,t),t.tag=1,dt(i)?(e=!0,ai(t)):e=!1,Xr(t,o),bf(t,i,a),Fa(t,i,a,o),$a(null,t,i,!0,e,o);case 19:return $f(e,t,o);case 22:return Df(e,t,o)}throw Error(s(156,t.tag))};function fh(e,t){return Vc(e,t)}function fv(e,t,o,i){this.tag=e,this.key=o,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function jt(e,t,o,i){return new fv(e,t,o,i)}function au(e){return e=e.prototype,!(!e||!e.isReactComponent)}function hv(e){if(typeof e=="function")return au(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ee)return 11;if(e===ve)return 14}return 2}function Wn(e,t){var o=e.alternate;return o===null?(o=jt(e.tag,t,e.key,e.mode),o.elementType=e.elementType,o.type=e.type,o.stateNode=e.stateNode,o.alternate=e,e.alternate=o):(o.pendingProps=t,o.type=e.type,o.flags=0,o.subtreeFlags=0,o.deletions=null),o.flags=e.flags&14680064,o.childLanes=e.childLanes,o.lanes=e.lanes,o.child=e.child,o.memoizedProps=e.memoizedProps,o.memoizedState=e.memoizedState,o.updateQueue=e.updateQueue,t=e.dependencies,o.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},o.sibling=e.sibling,o.index=e.index,o.ref=e.ref,o}function Ii(e,t,o,i,a,c){var h=2;if(i=e,typeof e=="function")au(e)&&(h=1);else if(typeof e=="string")h=5;else e:switch(e){case B:return vr(o.children,a,c,t);case J:h=8,a|=8;break;case re:return e=jt(12,o,t,a|2),e.elementType=re,e.lanes=c,e;case me:return e=jt(13,o,t,a),e.elementType=me,e.lanes=c,e;case K:return e=jt(19,o,t,a),e.elementType=K,e.lanes=c,e;case ae:return Fi(o,a,c,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case de:h=10;break e;case pe:h=9;break e;case ee:h=11;break e;case ve:h=14;break e;case ue:h=16,i=null;break e}throw Error(s(130,e==null?e:typeof e,""))}return t=jt(h,o,t,a),t.elementType=e,t.type=i,t.lanes=c,t}function vr(e,t,o,i){return e=jt(7,e,i,t),e.lanes=o,e}function Fi(e,t,o,i){return e=jt(22,e,i,t),e.elementType=ae,e.lanes=o,e.stateNode={isHidden:!1},e}function uu(e,t,o){return e=jt(6,e,null,t),e.lanes=o,e}function cu(e,t,o){return t=jt(4,e.children!==null?e.children:[],e.key,t),t.lanes=o,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function pv(e,t,o,i,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Il(0),this.expirationTimes=Il(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Il(0),this.identifierPrefix=i,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function du(e,t,o,i,a,c,h,w,b){return e=new pv(e,t,o,w,b),t===1?(t=1,c===!0&&(t|=8)):t=0,c=jt(3,null,null,t),e.current=c,c.stateNode=e,c.memoizedState={element:i,isDehydrated:o,cache:null,transitions:null,pendingSuspenseBoundaries:null},ka(c),e}function mv(e,t,o){var i=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:q,key:i==null?null:""+i,children:e,containerInfo:t,implementation:o}}function hh(e){if(!e)return jn;e=e._reactInternals;e:{if(ir(e)!==e||e.tag!==1)throw Error(s(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(dt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(s(171))}if(e.tag===1){var o=e.type;if(dt(o))return Wd(e,o,t)}return t}function ph(e,t,o,i,a,c,h,w,b){return e=du(o,i,!0,e,a,c,h,w,b),e.context=hh(null),o=e.current,i=lt(),a=Fn(o),c=hn(i,a),c.callback=t??null,An(o,c,a),e.current.lanes=a,Io(e,a,i),pt(e,i),e}function zi(e,t,o,i){var a=t.current,c=lt(),h=Fn(a);return o=hh(o),t.context===null?t.context=o:t.pendingContext=o,t=hn(c,h),t.payload={element:e},i=i===void 0?null:i,i!==null&&(t.callback=i),e=An(a,t,h),e!==null&&(Wt(e,a,h,c),gi(e,a,h)),h}function Wi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function mh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var o=e.retryLane;e.retryLane=o!==0&&o<t?o:t}}function fu(e,t){mh(e,t),(e=e.alternate)&&mh(e,t)}function gv(){return null}var gh=typeof reportError=="function"?reportError:function(e){console.error(e)};function hu(e){this._internalRoot=e}Ui.prototype.render=hu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));zi(e,t,null,null)},Ui.prototype.unmount=hu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;mr(function(){zi(null,e,null,null)}),t[an]=null}};function Ui(e){this._internalRoot=e}Ui.prototype.unstable_scheduleHydration=function(e){if(e){var t=Jc();e={blockedOn:null,target:e,priority:t};for(var o=0;o<Pn.length&&t!==0&&t<Pn[o].priority;o++);Pn.splice(o,0,e),o===0&&td(e)}};function pu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function $i(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function yh(){}function yv(e,t,o,i,a){if(a){if(typeof i=="function"){var c=i;i=function(){var _=Wi(h);c.call(_)}}var h=ph(t,i,e,0,null,!1,!1,"",yh);return e._reactRootContainer=h,e[an]=h.current,Xo(e.nodeType===8?e.parentNode:e),mr(),h}for(;a=e.lastChild;)e.removeChild(a);if(typeof i=="function"){var w=i;i=function(){var _=Wi(b);w.call(_)}}var b=du(e,0,!1,null,null,!1,!1,"",yh);return e._reactRootContainer=b,e[an]=b.current,Xo(e.nodeType===8?e.parentNode:e),mr(function(){zi(t,b,o,i)}),b}function Hi(e,t,o,i,a){var c=o._reactRootContainer;if(c){var h=c;if(typeof a=="function"){var w=a;a=function(){var b=Wi(h);w.call(b)}}zi(t,h,e,a)}else h=yv(o,t,e,a,i);return Wi(h)}Gc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var o=Lo(t.pendingLanes);o!==0&&(Fl(t,o|1),pt(t,We()),(Ee&6)===0&&(no=We()+500,Mn()))}break;case 13:mr(function(){var i=fn(e,1);if(i!==null){var a=lt();Wt(i,e,1,a)}}),fu(e,1)}},zl=function(e){if(e.tag===13){var t=fn(e,134217728);if(t!==null){var o=lt();Wt(t,e,134217728,o)}fu(e,134217728)}},Xc=function(e){if(e.tag===13){var t=Fn(e),o=fn(e,t);if(o!==null){var i=lt();Wt(o,e,t,i)}fu(e,t)}},Jc=function(){return Te},Zc=function(e,t){var o=Te;try{return Te=e,t()}finally{Te=o}},jl=function(e,t,o){switch(t){case"input":if(Ro(e,o),t=o.name,o.type==="radio"&&t!=null){for(o=e;o.parentNode;)o=o.parentNode;for(o=o.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<o.length;t++){var i=o[t];if(i!==e&&i.form===e.form){var a=ii(i);if(!a)throw Error(s(90));To(i),Ro(i,a)}}}break;case"textarea":Tc(e,o);break;case"select":t=o.value,t!=null&&_r(e,!!o.multiple,t,!1)}},Ic=su,Fc=mr;var vv={usingClientEntryPoint:!1,Events:[es,Hr,ii,Dc,Lc,su]},ps={findFiberByHostInstance:lr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},xv={bundleType:ps.bundleType,version:ps.version,rendererPackageName:ps.rendererPackageName,rendererConfig:ps.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:F.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=$c(e),e===null?null:e.stateNode},findFiberByHostInstance:ps.findFiberByHostInstance||gv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Vi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Vi.isDisabled&&Vi.supportsFiber)try{Us=Vi.inject(xv),Yt=Vi}catch{}}return mt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=vv,mt.createPortal=function(e,t){var o=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!pu(t))throw Error(s(200));return mv(e,t,null,o)},mt.createRoot=function(e,t){if(!pu(e))throw Error(s(299));var o=!1,i="",a=gh;return t!=null&&(t.unstable_strictMode===!0&&(o=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onRecoverableError!==void 0&&(a=t.onRecoverableError)),t=du(e,1,!1,null,null,o,!1,i,a),e[an]=t.current,Xo(e.nodeType===8?e.parentNode:e),new hu(t)},mt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=$c(t),e=e===null?null:e.stateNode,e},mt.flushSync=function(e){return mr(e)},mt.hydrate=function(e,t,o){if(!$i(t))throw Error(s(200));return Hi(null,e,t,!0,o)},mt.hydrateRoot=function(e,t,o){if(!pu(e))throw Error(s(405));var i=o!=null&&o.hydratedSources||null,a=!1,c="",h=gh;if(o!=null&&(o.unstable_strictMode===!0&&(a=!0),o.identifierPrefix!==void 0&&(c=o.identifierPrefix),o.onRecoverableError!==void 0&&(h=o.onRecoverableError)),t=ph(t,null,e,1,o??null,a,!1,c,h),e[an]=t.current,Xo(e),i)for(e=0;e<i.length;e++)o=i[e],a=o._getVersion,a=a(o._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[o,a]:t.mutableSourceEagerHydrationData.push(o,a);return new Ui(t)},mt.render=function(e,t,o){if(!$i(t))throw Error(s(200));return Hi(null,e,t,!1,o)},mt.unmountComponentAtNode=function(e){if(!$i(e))throw Error(s(40));return e._reactRootContainer?(mr(function(){Hi(null,null,e,!1,function(){e._reactRootContainer=null,e[an]=null})}),!0):!1},mt.unstable_batchedUpdates=su,mt.unstable_renderSubtreeIntoContainer=function(e,t,o,i){if(!$i(o))throw Error(s(200));if(e==null||e._reactInternals===void 0)throw Error(s(38));return Hi(e,t,o,!1,i)},mt.version="18.3.1-next-f1338f8080-20240426",mt}var Ph;function Np(){if(Ph)return vu.exports;Ph=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),vu.exports=Nv(),vu.exports}var Nh;function Tv(){if(Nh)return Qi;Nh=1;var n=Np();return Qi.createRoot=n.createRoot,Qi.hydrateRoot=n.hydrateRoot,Qi}var Rv=Tv();function Ov(n,r){if(n instanceof RegExp)return{keys:!1,pattern:n};var s,l,u,d,f=[],p="",g=n.split("/");for(g[0]||g.shift();u=g.shift();)s=u[0],s==="*"?(f.push(s),p+=u[1]==="?"?"(?:/(.*))?":"/(.*)"):s===":"?(l=u.indexOf("?",1),d=u.indexOf(".",1),f.push(u.substring(1,~l?l:~d?d:u.length)),p+=~l&&!~d?"(?:/([^/]+?))?":"/([^/]+?)",~d&&(p+=(~l?"?":"")+"\\"+u.substring(d))):p+="/"+u;return{keys:f,pattern:new RegExp("^"+p+(r?"(?=$|/)":"/?$"),"i")}}var C=fl();const Bn=Pp(C),jv=Sv({__proto__:null,default:Bn},[C]);var Su={exports:{}},Cu={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Th;function Mv(){if(Th)return Cu;Th=1;var n=fl();function r(v,E){return v===E&&(v!==0||1/v===1/E)||v!==v&&E!==E}var s=typeof Object.is=="function"?Object.is:r,l=n.useState,u=n.useEffect,d=n.useLayoutEffect,f=n.useDebugValue;function p(v,E){var N=E(),j=l({inst:{value:N,getSnapshot:E}}),S=j[0].inst,k=j[1];return d(function(){S.value=N,S.getSnapshot=E,g(S)&&k({inst:S})},[v,N,E]),u(function(){return g(S)&&k({inst:S}),v(function(){g(S)&&k({inst:S})})},[v]),f(N),N}function g(v){var E=v.getSnapshot;v=v.value;try{var N=E();return!s(v,N)}catch{return!0}}function y(v,E){return E()}var x=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?y:p;return Cu.useSyncExternalStore=n.useSyncExternalStore!==void 0?n.useSyncExternalStore:x,Cu}var Rh;function _v(){return Rh||(Rh=1,Su.exports=Mv()),Su.exports}var Av=_v();const Dv=jv.useInsertionEffect,Lv=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Iv=Lv?C.useLayoutEffect:C.useEffect,Fv=Dv||Iv,Tp=n=>{const r=C.useRef([n,(...s)=>r[0](...s)]).current;return Fv(()=>{r[0]=n}),r[1]},zv="popstate",rc="pushState",oc="replaceState",Wv="hashchange",Oh=[zv,rc,oc,Wv],Uv=n=>{for(const r of Oh)addEventListener(r,n);return()=>{for(const r of Oh)removeEventListener(r,n)}},Rp=(n,r)=>Av.useSyncExternalStore(Uv,n,r),$v=()=>location.search,Hv=({ssrSearch:n=""}={})=>Rp($v,()=>n),jh=()=>location.pathname,Vv=({ssrPath:n}={})=>Rp(jh,n?()=>n:jh),Bv=(n,{replace:r=!1,state:s=null}={})=>history[r?oc:rc](s,"",n),Qv=(n={})=>[Vv(n),Bv],Mh=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[Mh]>"u"){for(const n of[rc,oc]){const r=history[n];history[n]=function(){const s=r.apply(this,arguments),l=new Event(n);return l.arguments=arguments,dispatchEvent(l),s}}Object.defineProperty(window,Mh,{value:!0})}const qv=(n,r)=>r.toLowerCase().indexOf(n.toLowerCase())?"~"+r:r.slice(n.length)||"/",Op=(n="")=>n==="/"?"":n,Yv=(n,r)=>n[0]==="~"?n.slice(1):Op(r)+n,Kv=(n="",r)=>qv(_h(Op(n)),_h(r)),_h=n=>{try{return decodeURI(n)}catch{return n}},jp={hook:Qv,searchHook:Hv,parser:Ov,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:n=>n},Mp=C.createContext(jp),hl=()=>C.useContext(Mp),_p={},Ap=C.createContext(_p),Gv=()=>C.useContext(Ap),sc=n=>{const[r,s]=n.hook(n);return[Kv(n.base,r),Tp((l,u)=>s(Yv(l,n.base),u))]},Dp=(n,r,s,l)=>{const{pattern:u,keys:d}=r instanceof RegExp?{keys:!1,pattern:r}:n(r||"*",l),f=u.exec(s)||[],[p,...g]=f;return p!==void 0?[!0,(()=>{const y=d!==!1?Object.fromEntries(d.map((v,E)=>[v,g[E]])):f.groups;let x={...g};return y&&Object.assign(x,y),x})(),...l?[p]:[]]:[!1,null]},Xv=({children:n,...r})=>{var x,v;const s=hl(),l=r.hook?jp:s;let u=l;const[d,f]=((x=r.ssrPath)==null?void 0:x.split("?"))??[];f&&(r.ssrSearch=f,r.ssrPath=d),r.hrefs=r.hrefs??((v=r.hook)==null?void 0:v.hrefs);let p=C.useRef({}),g=p.current,y=g;for(let E in l){const N=E==="base"?l[E]+(r[E]||""):r[E]||l[E];g===y&&N!==y[E]&&(p.current=y={...y}),y[E]=N,N!==l[E]&&(u=y)}return C.createElement(Mp.Provider,{value:u,children:n})},Ah=({children:n,component:r},s)=>r?C.createElement(r,{params:s}):typeof n=="function"?n(s):n,Jv=n=>{let r=C.useRef(_p),s=r.current;for(const l in n)n[l]!==s[l]&&(s=n);return Object.keys(n).length===0&&(s=n),r.current=s},Eu=({path:n,nest:r,match:s,...l})=>{const u=hl(),[d]=sc(u),[f,p,g]=s??Dp(u.parser,n,d,r),y=Jv({...Gv(),...p});if(!f)return null;const x=g?C.createElement(Xv,{base:g},Ah(l,y)):Ah(l,y);return C.createElement(Ap.Provider,{value:y,children:x})};C.forwardRef((n,r)=>{const s=hl(),[l,u]=sc(s),{to:d="",href:f=d,onClick:p,asChild:g,children:y,className:x,replace:v,state:E,...N}=n,j=Tp(k=>{k.ctrlKey||k.metaKey||k.altKey||k.shiftKey||k.button!==0||(p==null||p(k),k.defaultPrevented||(k.preventDefault(),u(f,n)))}),S=s.hrefs(f[0]==="~"?f.slice(1):s.base+f,s);return g&&C.isValidElement(y)?C.cloneElement(y,{onClick:j,href:S}):C.createElement("a",{...N,onClick:j,href:S,className:x!=null&&x.call?x(l===f):x,children:y,ref:r})});const Lp=n=>Array.isArray(n)?n.flatMap(r=>Lp(r&&r.type===C.Fragment?r.props.children:r)):[n],Zv=({children:n,location:r})=>{const s=hl(),[l]=sc(s);for(const u of Lp(n)){let d=0;if(C.isValidElement(u)&&(d=Dp(s.parser,u.props.path,r||l,u.props.nest))[0])return C.cloneElement(u,{match:d})}return null};var ko=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(n){return this.listeners.add(n),this.onSubscribe(),()=>{this.listeners.delete(n),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Nr=typeof window>"u"||"Deno"in globalThis;function _t(){}function ex(n,r){return typeof n=="function"?n(r):n}function Au(n){return typeof n=="number"&&n>=0&&n!==1/0}function Ip(n,r){return Math.max(n+(r||0)-Date.now(),0)}function so(n,r){return typeof n=="function"?n(r):n}function $t(n,r){return typeof n=="function"?n(r):n}function Dh(n,r){const{type:s="all",exact:l,fetchStatus:u,predicate:d,queryKey:f,stale:p}=n;if(f){if(l){if(r.queryHash!==ic(f,r.options))return!1}else if(!bs(r.queryKey,f))return!1}if(s!=="all"){const g=r.isActive();if(s==="active"&&!g||s==="inactive"&&g)return!1}return!(typeof p=="boolean"&&r.isStale()!==p||u&&u!==r.state.fetchStatus||d&&!d(r))}function Lh(n,r){const{exact:s,status:l,predicate:u,mutationKey:d}=n;if(d){if(!r.options.mutationKey)return!1;if(s){if(Tr(r.options.mutationKey)!==Tr(d))return!1}else if(!bs(r.options.mutationKey,d))return!1}return!(l&&r.state.status!==l||u&&!u(r))}function ic(n,r){return((r==null?void 0:r.queryKeyHashFn)||Tr)(n)}function Tr(n){return JSON.stringify(n,(r,s)=>Du(s)?Object.keys(s).sort().reduce((l,u)=>(l[u]=s[u],l),{}):s)}function bs(n,r){return n===r?!0:typeof n!=typeof r?!1:n&&r&&typeof n=="object"&&typeof r=="object"?!Object.keys(r).some(s=>!bs(n[s],r[s])):!1}function Fp(n,r){if(n===r)return n;const s=Ih(n)&&Ih(r);if(s||Du(n)&&Du(r)){const l=s?n:Object.keys(n),u=l.length,d=s?r:Object.keys(r),f=d.length,p=s?[]:{};let g=0;for(let y=0;y<f;y++){const x=s?y:d[y];(!s&&l.includes(x)||s)&&n[x]===void 0&&r[x]===void 0?(p[x]=void 0,g++):(p[x]=Fp(n[x],r[x]),p[x]===n[x]&&n[x]!==void 0&&g++)}return u===f&&g===u?n:p}return r}function ol(n,r){if(!r||Object.keys(n).length!==Object.keys(r).length)return!1;for(const s in n)if(n[s]!==r[s])return!1;return!0}function Ih(n){return Array.isArray(n)&&n.length===Object.keys(n).length}function Du(n){if(!Fh(n))return!1;const r=n.constructor;if(r===void 0)return!0;const s=r.prototype;return!(!Fh(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(n)!==Object.prototype)}function Fh(n){return Object.prototype.toString.call(n)==="[object Object]"}function tx(n){return new Promise(r=>{setTimeout(r,n)})}function Lu(n,r,s){return typeof s.structuralSharing=="function"?s.structuralSharing(n,r):s.structuralSharing!==!1?Fp(n,r):r}function nx(n,r,s=0){const l=[...n,r];return s&&l.length>s?l.slice(1):l}function rx(n,r,s=0){const l=[r,...n];return s&&l.length>s?l.slice(0,-1):l}var lc=Symbol();function zp(n,r){return!n.queryFn&&(r!=null&&r.initialPromise)?()=>r.initialPromise:!n.queryFn||n.queryFn===lc?()=>Promise.reject(new Error(`Missing queryFn: '${n.queryHash}'`)):n.queryFn}var wr,qn,ao,gp,ox=(gp=class extends ko{constructor(){super();le(this,wr);le(this,qn);le(this,ao);X(this,ao,r=>{if(!Nr&&window.addEventListener){const s=()=>r();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){P(this,qn)||this.setEventListener(P(this,ao))}onUnsubscribe(){var r;this.hasListeners()||((r=P(this,qn))==null||r.call(this),X(this,qn,void 0))}setEventListener(r){var s;X(this,ao,r),(s=P(this,qn))==null||s.call(this),X(this,qn,r(l=>{typeof l=="boolean"?this.setFocused(l):this.onFocus()}))}setFocused(r){P(this,wr)!==r&&(X(this,wr,r),this.onFocus())}onFocus(){const r=this.isFocused();this.listeners.forEach(s=>{s(r)})}isFocused(){var r;return typeof P(this,wr)=="boolean"?P(this,wr):((r=globalThis.document)==null?void 0:r.visibilityState)!=="hidden"}},wr=new WeakMap,qn=new WeakMap,ao=new WeakMap,gp),ac=new ox,uo,Yn,co,yp,sx=(yp=class extends ko{constructor(){super();le(this,uo,!0);le(this,Yn);le(this,co);X(this,co,r=>{if(!Nr&&window.addEventListener){const s=()=>r(!0),l=()=>r(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",l,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",l)}}})}onSubscribe(){P(this,Yn)||this.setEventListener(P(this,co))}onUnsubscribe(){var r;this.hasListeners()||((r=P(this,Yn))==null||r.call(this),X(this,Yn,void 0))}setEventListener(r){var s;X(this,co,r),(s=P(this,Yn))==null||s.call(this),X(this,Yn,r(this.setOnline.bind(this)))}setOnline(r){P(this,uo)!==r&&(X(this,uo,r),this.listeners.forEach(l=>{l(r)}))}isOnline(){return P(this,uo)}},uo=new WeakMap,Yn=new WeakMap,co=new WeakMap,yp),sl=new sx;function Iu(){let n,r;const s=new Promise((u,d)=>{n=u,r=d});s.status="pending",s.catch(()=>{});function l(u){Object.assign(s,u),delete s.resolve,delete s.reject}return s.resolve=u=>{l({status:"fulfilled",value:u}),n(u)},s.reject=u=>{l({status:"rejected",reason:u}),r(u)},s}function ix(n){return Math.min(1e3*2**n,3e4)}function Wp(n){return(n??"online")==="online"?sl.isOnline():!0}var Up=class extends Error{constructor(n){super("CancelledError"),this.revert=n==null?void 0:n.revert,this.silent=n==null?void 0:n.silent}};function ku(n){return n instanceof Up}function $p(n){let r=!1,s=0,l=!1,u;const d=Iu(),f=S=>{var k;l||(E(new Up(S)),(k=n.abort)==null||k.call(n))},p=()=>{r=!0},g=()=>{r=!1},y=()=>ac.isFocused()&&(n.networkMode==="always"||sl.isOnline())&&n.canRun(),x=()=>Wp(n.networkMode)&&n.canRun(),v=S=>{var k;l||(l=!0,(k=n.onSuccess)==null||k.call(n,S),u==null||u(),d.resolve(S))},E=S=>{var k;l||(l=!0,(k=n.onError)==null||k.call(n,S),u==null||u(),d.reject(S))},N=()=>new Promise(S=>{var k;u=A=>{(l||y())&&S(A)},(k=n.onPause)==null||k.call(n)}).then(()=>{var S;u=void 0,l||(S=n.onContinue)==null||S.call(n)}),j=()=>{if(l)return;let S;const k=s===0?n.initialPromise:void 0;try{S=k??n.fn()}catch(A){S=Promise.reject(A)}Promise.resolve(S).then(v).catch(A=>{var q;if(l)return;const D=n.retry??(Nr?0:3),I=n.retryDelay??ix,F=typeof I=="function"?I(s,A):I,H=D===!0||typeof D=="number"&&s<D||typeof D=="function"&&D(s,A);if(r||!H){E(A);return}s++,(q=n.onFail)==null||q.call(n,s,A),tx(F).then(()=>y()?void 0:N()).then(()=>{r?E(A):j()})})};return{promise:d,cancel:f,continue:()=>(u==null||u(),d),cancelRetry:p,continueRetry:g,canStart:x,start:()=>(x()?j():N().then(j),d)}}function lx(){let n=[],r=0,s=p=>{p()},l=p=>{p()},u=p=>setTimeout(p,0);const d=p=>{r?n.push(p):u(()=>{s(p)})},f=()=>{const p=n;n=[],p.length&&u(()=>{l(()=>{p.forEach(g=>{s(g)})})})};return{batch:p=>{let g;r++;try{g=p()}finally{r--,r||f()}return g},batchCalls:p=>(...g)=>{d(()=>{p(...g)})},schedule:d,setNotifyFunction:p=>{s=p},setBatchNotifyFunction:p=>{l=p},setScheduler:p=>{u=p}}}var Be=lx(),Sr,vp,Hp=(vp=class{constructor(){le(this,Sr)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Au(this.gcTime)&&X(this,Sr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(n){this.gcTime=Math.max(this.gcTime||0,n??(Nr?1/0:5*60*1e3))}clearGcTimeout(){P(this,Sr)&&(clearTimeout(P(this,Sr)),X(this,Sr,void 0))}},Sr=new WeakMap,vp),fo,ho,Mt,st,Rs,Cr,Ut,yn,xp,ax=(xp=class extends Hp{constructor(r){super();le(this,Ut);le(this,fo);le(this,ho);le(this,Mt);le(this,st);le(this,Rs);le(this,Cr);X(this,Cr,!1),X(this,Rs,r.defaultOptions),this.setOptions(r.options),this.observers=[],X(this,Mt,r.cache),this.queryKey=r.queryKey,this.queryHash=r.queryHash,X(this,fo,ux(this.options)),this.state=r.state??P(this,fo),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var r;return(r=P(this,st))==null?void 0:r.promise}setOptions(r){this.options={...P(this,Rs),...r},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&P(this,Mt).remove(this)}setData(r,s){const l=Lu(this.state.data,r,this.options);return ye(this,Ut,yn).call(this,{data:l,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),l}setState(r,s){ye(this,Ut,yn).call(this,{type:"setState",state:r,setStateOptions:s})}cancel(r){var l,u;const s=(l=P(this,st))==null?void 0:l.promise;return(u=P(this,st))==null||u.cancel(r),s?s.then(_t).catch(_t):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(P(this,fo))}isActive(){return this.observers.some(r=>$t(r.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===lc||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(r=>r.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(r=0){return this.state.isInvalidated||this.state.data===void 0||!Ip(this.state.dataUpdatedAt,r)}onFocus(){var s;const r=this.observers.find(l=>l.shouldFetchOnWindowFocus());r==null||r.refetch({cancelRefetch:!1}),(s=P(this,st))==null||s.continue()}onOnline(){var s;const r=this.observers.find(l=>l.shouldFetchOnReconnect());r==null||r.refetch({cancelRefetch:!1}),(s=P(this,st))==null||s.continue()}addObserver(r){this.observers.includes(r)||(this.observers.push(r),this.clearGcTimeout(),P(this,Mt).notify({type:"observerAdded",query:this,observer:r}))}removeObserver(r){this.observers.includes(r)&&(this.observers=this.observers.filter(s=>s!==r),this.observers.length||(P(this,st)&&(P(this,Cr)?P(this,st).cancel({revert:!0}):P(this,st).cancelRetry()),this.scheduleGc()),P(this,Mt).notify({type:"observerRemoved",query:this,observer:r}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||ye(this,Ut,yn).call(this,{type:"invalidate"})}fetch(r,s){var g,y,x;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(P(this,st))return P(this,st).continueRetry(),P(this,st).promise}if(r&&this.setOptions(r),!this.options.queryFn){const v=this.observers.find(E=>E.options.queryFn);v&&this.setOptions(v.options)}const l=new AbortController,u=v=>{Object.defineProperty(v,"signal",{enumerable:!0,get:()=>(X(this,Cr,!0),l.signal)})},d=()=>{const v=zp(this.options,s),E={queryKey:this.queryKey,meta:this.meta};return u(E),X(this,Cr,!1),this.options.persister?this.options.persister(v,E,this):v(E)},f={fetchOptions:s,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:d};u(f),(g=this.options.behavior)==null||g.onFetch(f,this),X(this,ho,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((y=f.fetchOptions)==null?void 0:y.meta))&&ye(this,Ut,yn).call(this,{type:"fetch",meta:(x=f.fetchOptions)==null?void 0:x.meta});const p=v=>{var E,N,j,S;ku(v)&&v.silent||ye(this,Ut,yn).call(this,{type:"error",error:v}),ku(v)||((N=(E=P(this,Mt).config).onError)==null||N.call(E,v,this),(S=(j=P(this,Mt).config).onSettled)==null||S.call(j,this.state.data,v,this)),this.scheduleGc()};return X(this,st,$p({initialPromise:s==null?void 0:s.initialPromise,fn:f.fetchFn,abort:l.abort.bind(l),onSuccess:v=>{var E,N,j,S;if(v===void 0){p(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(v)}catch(k){p(k);return}(N=(E=P(this,Mt).config).onSuccess)==null||N.call(E,v,this),(S=(j=P(this,Mt).config).onSettled)==null||S.call(j,v,this.state.error,this),this.scheduleGc()},onError:p,onFail:(v,E)=>{ye(this,Ut,yn).call(this,{type:"failed",failureCount:v,error:E})},onPause:()=>{ye(this,Ut,yn).call(this,{type:"pause"})},onContinue:()=>{ye(this,Ut,yn).call(this,{type:"continue"})},retry:f.options.retry,retryDelay:f.options.retryDelay,networkMode:f.options.networkMode,canRun:()=>!0})),P(this,st).start()}},fo=new WeakMap,ho=new WeakMap,Mt=new WeakMap,st=new WeakMap,Rs=new WeakMap,Cr=new WeakMap,Ut=new WeakSet,yn=function(r){const s=l=>{switch(r.type){case"failed":return{...l,fetchFailureCount:r.failureCount,fetchFailureReason:r.error};case"pause":return{...l,fetchStatus:"paused"};case"continue":return{...l,fetchStatus:"fetching"};case"fetch":return{...l,...Vp(l.data,this.options),fetchMeta:r.meta??null};case"success":return{...l,data:r.data,dataUpdateCount:l.dataUpdateCount+1,dataUpdatedAt:r.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!r.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const u=r.error;return ku(u)&&u.revert&&P(this,ho)?{...P(this,ho),fetchStatus:"idle"}:{...l,error:u,errorUpdateCount:l.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:l.fetchFailureCount+1,fetchFailureReason:u,fetchStatus:"idle",status:"error"};case"invalidate":return{...l,isInvalidated:!0};case"setState":return{...l,...r.state}}};this.state=s(this.state),Be.batch(()=>{this.observers.forEach(l=>{l.onQueryUpdate()}),P(this,Mt).notify({query:this,type:"updated",action:r})})},xp);function Vp(n,r){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Wp(r.networkMode)?"fetching":"paused",...n===void 0&&{error:null,status:"pending"}}}function ux(n){const r=typeof n.initialData=="function"?n.initialData():n.initialData,s=r!==void 0,l=s?typeof n.initialDataUpdatedAt=="function"?n.initialDataUpdatedAt():n.initialDataUpdatedAt:0;return{data:r,dataUpdateCount:0,dataUpdatedAt:s?l??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var Zt,wp,cx=(wp=class extends ko{constructor(r={}){super();le(this,Zt);this.config=r,X(this,Zt,new Map)}build(r,s,l){const u=s.queryKey,d=s.queryHash??ic(u,s);let f=this.get(d);return f||(f=new ax({cache:this,queryKey:u,queryHash:d,options:r.defaultQueryOptions(s),state:l,defaultOptions:r.getQueryDefaults(u)}),this.add(f)),f}add(r){P(this,Zt).has(r.queryHash)||(P(this,Zt).set(r.queryHash,r),this.notify({type:"added",query:r}))}remove(r){const s=P(this,Zt).get(r.queryHash);s&&(r.destroy(),s===r&&P(this,Zt).delete(r.queryHash),this.notify({type:"removed",query:r}))}clear(){Be.batch(()=>{this.getAll().forEach(r=>{this.remove(r)})})}get(r){return P(this,Zt).get(r)}getAll(){return[...P(this,Zt).values()]}find(r){const s={exact:!0,...r};return this.getAll().find(l=>Dh(s,l))}findAll(r={}){const s=this.getAll();return Object.keys(r).length>0?s.filter(l=>Dh(r,l)):s}notify(r){Be.batch(()=>{this.listeners.forEach(s=>{s(r)})})}onFocus(){Be.batch(()=>{this.getAll().forEach(r=>{r.onFocus()})})}onOnline(){Be.batch(()=>{this.getAll().forEach(r=>{r.onOnline()})})}},Zt=new WeakMap,wp),en,at,Er,tn,Qn,Sp,dx=(Sp=class extends Hp{constructor(r){super();le(this,tn);le(this,en);le(this,at);le(this,Er);this.mutationId=r.mutationId,X(this,at,r.mutationCache),X(this,en,[]),this.state=r.state||Bp(),this.setOptions(r.options),this.scheduleGc()}setOptions(r){this.options=r,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(r){P(this,en).includes(r)||(P(this,en).push(r),this.clearGcTimeout(),P(this,at).notify({type:"observerAdded",mutation:this,observer:r}))}removeObserver(r){X(this,en,P(this,en).filter(s=>s!==r)),this.scheduleGc(),P(this,at).notify({type:"observerRemoved",mutation:this,observer:r})}optionalRemove(){P(this,en).length||(this.state.status==="pending"?this.scheduleGc():P(this,at).remove(this))}continue(){var r;return((r=P(this,Er))==null?void 0:r.continue())??this.execute(this.state.variables)}async execute(r){var u,d,f,p,g,y,x,v,E,N,j,S,k,A,D,I,F,H,q,B;X(this,Er,$p({fn:()=>this.options.mutationFn?this.options.mutationFn(r):Promise.reject(new Error("No mutationFn found")),onFail:(J,re)=>{ye(this,tn,Qn).call(this,{type:"failed",failureCount:J,error:re})},onPause:()=>{ye(this,tn,Qn).call(this,{type:"pause"})},onContinue:()=>{ye(this,tn,Qn).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>P(this,at).canRun(this)}));const s=this.state.status==="pending",l=!P(this,Er).canStart();try{if(!s){ye(this,tn,Qn).call(this,{type:"pending",variables:r,isPaused:l}),await((d=(u=P(this,at).config).onMutate)==null?void 0:d.call(u,r,this));const re=await((p=(f=this.options).onMutate)==null?void 0:p.call(f,r));re!==this.state.context&&ye(this,tn,Qn).call(this,{type:"pending",context:re,variables:r,isPaused:l})}const J=await P(this,Er).start();return await((y=(g=P(this,at).config).onSuccess)==null?void 0:y.call(g,J,r,this.state.context,this)),await((v=(x=this.options).onSuccess)==null?void 0:v.call(x,J,r,this.state.context)),await((N=(E=P(this,at).config).onSettled)==null?void 0:N.call(E,J,null,this.state.variables,this.state.context,this)),await((S=(j=this.options).onSettled)==null?void 0:S.call(j,J,null,r,this.state.context)),ye(this,tn,Qn).call(this,{type:"success",data:J}),J}catch(J){try{throw await((A=(k=P(this,at).config).onError)==null?void 0:A.call(k,J,r,this.state.context,this)),await((I=(D=this.options).onError)==null?void 0:I.call(D,J,r,this.state.context)),await((H=(F=P(this,at).config).onSettled)==null?void 0:H.call(F,void 0,J,this.state.variables,this.state.context,this)),await((B=(q=this.options).onSettled)==null?void 0:B.call(q,void 0,J,r,this.state.context)),J}finally{ye(this,tn,Qn).call(this,{type:"error",error:J})}}finally{P(this,at).runNext(this)}}},en=new WeakMap,at=new WeakMap,Er=new WeakMap,tn=new WeakSet,Qn=function(r){const s=l=>{switch(r.type){case"failed":return{...l,failureCount:r.failureCount,failureReason:r.error};case"pause":return{...l,isPaused:!0};case"continue":return{...l,isPaused:!1};case"pending":return{...l,context:r.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:r.isPaused,status:"pending",variables:r.variables,submittedAt:Date.now()};case"success":return{...l,data:r.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...l,data:void 0,error:r.error,failureCount:l.failureCount+1,failureReason:r.error,isPaused:!1,status:"error"}}};this.state=s(this.state),Be.batch(()=>{P(this,en).forEach(l=>{l.onMutationUpdate(r)}),P(this,at).notify({mutation:this,type:"updated",action:r})})},Sp);function Bp(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Et,Os,Cp,fx=(Cp=class extends ko{constructor(r={}){super();le(this,Et);le(this,Os);this.config=r,X(this,Et,new Map),X(this,Os,Date.now())}build(r,s,l){const u=new dx({mutationCache:this,mutationId:++Bi(this,Os)._,options:r.defaultMutationOptions(s),state:l});return this.add(u),u}add(r){const s=qi(r),l=P(this,Et).get(s)??[];l.push(r),P(this,Et).set(s,l),this.notify({type:"added",mutation:r})}remove(r){var l;const s=qi(r);if(P(this,Et).has(s)){const u=(l=P(this,Et).get(s))==null?void 0:l.filter(d=>d!==r);u&&(u.length===0?P(this,Et).delete(s):P(this,Et).set(s,u))}this.notify({type:"removed",mutation:r})}canRun(r){var l;const s=(l=P(this,Et).get(qi(r)))==null?void 0:l.find(u=>u.state.status==="pending");return!s||s===r}runNext(r){var l;const s=(l=P(this,Et).get(qi(r)))==null?void 0:l.find(u=>u!==r&&u.state.isPaused);return(s==null?void 0:s.continue())??Promise.resolve()}clear(){Be.batch(()=>{this.getAll().forEach(r=>{this.remove(r)})})}getAll(){return[...P(this,Et).values()].flat()}find(r){const s={exact:!0,...r};return this.getAll().find(l=>Lh(s,l))}findAll(r={}){return this.getAll().filter(s=>Lh(r,s))}notify(r){Be.batch(()=>{this.listeners.forEach(s=>{s(r)})})}resumePausedMutations(){const r=this.getAll().filter(s=>s.state.isPaused);return Be.batch(()=>Promise.all(r.map(s=>s.continue().catch(_t))))}},Et=new WeakMap,Os=new WeakMap,Cp);function qi(n){var r;return((r=n.options.scope)==null?void 0:r.id)??String(n.mutationId)}function zh(n){return{onFetch:(r,s)=>{var x,v,E,N,j;const l=r.options,u=(E=(v=(x=r.fetchOptions)==null?void 0:x.meta)==null?void 0:v.fetchMore)==null?void 0:E.direction,d=((N=r.state.data)==null?void 0:N.pages)||[],f=((j=r.state.data)==null?void 0:j.pageParams)||[];let p={pages:[],pageParams:[]},g=0;const y=async()=>{let S=!1;const k=I=>{Object.defineProperty(I,"signal",{enumerable:!0,get:()=>(r.signal.aborted?S=!0:r.signal.addEventListener("abort",()=>{S=!0}),r.signal)})},A=zp(r.options,r.fetchOptions),D=async(I,F,H)=>{if(S)return Promise.reject();if(F==null&&I.pages.length)return Promise.resolve(I);const q={queryKey:r.queryKey,pageParam:F,direction:H?"backward":"forward",meta:r.options.meta};k(q);const B=await A(q),{maxPages:J}=r.options,re=H?rx:nx;return{pages:re(I.pages,B,J),pageParams:re(I.pageParams,F,J)}};if(u&&d.length){const I=u==="backward",F=I?hx:Wh,H={pages:d,pageParams:f},q=F(l,H);p=await D(H,q,I)}else{const I=n??d.length;do{const F=g===0?f[0]??l.initialPageParam:Wh(l,p);if(g>0&&F==null)break;p=await D(p,F),g++}while(g<I)}return p};r.options.persister?r.fetchFn=()=>{var S,k;return(k=(S=r.options).persister)==null?void 0:k.call(S,y,{queryKey:r.queryKey,meta:r.options.meta,signal:r.signal},s)}:r.fetchFn=y}}}function Wh(n,{pages:r,pageParams:s}){const l=r.length-1;return r.length>0?n.getNextPageParam(r[l],r,s[l],s):void 0}function hx(n,{pages:r,pageParams:s}){var l;return r.length>0?(l=n.getPreviousPageParam)==null?void 0:l.call(n,r[0],r,s[0],s):void 0}var ze,Kn,Gn,po,mo,Xn,go,yo,Ep,px=(Ep=class{constructor(n={}){le(this,ze);le(this,Kn);le(this,Gn);le(this,po);le(this,mo);le(this,Xn);le(this,go);le(this,yo);X(this,ze,n.queryCache||new cx),X(this,Kn,n.mutationCache||new fx),X(this,Gn,n.defaultOptions||{}),X(this,po,new Map),X(this,mo,new Map),X(this,Xn,0)}mount(){Bi(this,Xn)._++,P(this,Xn)===1&&(X(this,go,ac.subscribe(async n=>{n&&(await this.resumePausedMutations(),P(this,ze).onFocus())})),X(this,yo,sl.subscribe(async n=>{n&&(await this.resumePausedMutations(),P(this,ze).onOnline())})))}unmount(){var n,r;Bi(this,Xn)._--,P(this,Xn)===0&&((n=P(this,go))==null||n.call(this),X(this,go,void 0),(r=P(this,yo))==null||r.call(this),X(this,yo,void 0))}isFetching(n){return P(this,ze).findAll({...n,fetchStatus:"fetching"}).length}isMutating(n){return P(this,Kn).findAll({...n,status:"pending"}).length}getQueryData(n){var s;const r=this.defaultQueryOptions({queryKey:n});return(s=P(this,ze).get(r.queryHash))==null?void 0:s.state.data}ensureQueryData(n){const r=this.getQueryData(n.queryKey);if(r===void 0)return this.fetchQuery(n);{const s=this.defaultQueryOptions(n),l=P(this,ze).build(this,s);return n.revalidateIfStale&&l.isStaleByTime(so(s.staleTime,l))&&this.prefetchQuery(s),Promise.resolve(r)}}getQueriesData(n){return P(this,ze).findAll(n).map(({queryKey:r,state:s})=>{const l=s.data;return[r,l]})}setQueryData(n,r,s){const l=this.defaultQueryOptions({queryKey:n}),u=P(this,ze).get(l.queryHash),d=u==null?void 0:u.state.data,f=ex(r,d);if(f!==void 0)return P(this,ze).build(this,l).setData(f,{...s,manual:!0})}setQueriesData(n,r,s){return Be.batch(()=>P(this,ze).findAll(n).map(({queryKey:l})=>[l,this.setQueryData(l,r,s)]))}getQueryState(n){var s;const r=this.defaultQueryOptions({queryKey:n});return(s=P(this,ze).get(r.queryHash))==null?void 0:s.state}removeQueries(n){const r=P(this,ze);Be.batch(()=>{r.findAll(n).forEach(s=>{r.remove(s)})})}resetQueries(n,r){const s=P(this,ze),l={type:"active",...n};return Be.batch(()=>(s.findAll(n).forEach(u=>{u.reset()}),this.refetchQueries(l,r)))}cancelQueries(n={},r={}){const s={revert:!0,...r},l=Be.batch(()=>P(this,ze).findAll(n).map(u=>u.cancel(s)));return Promise.all(l).then(_t).catch(_t)}invalidateQueries(n={},r={}){return Be.batch(()=>{if(P(this,ze).findAll(n).forEach(l=>{l.invalidate()}),n.refetchType==="none")return Promise.resolve();const s={...n,type:n.refetchType??n.type??"active"};return this.refetchQueries(s,r)})}refetchQueries(n={},r){const s={...r,cancelRefetch:(r==null?void 0:r.cancelRefetch)??!0},l=Be.batch(()=>P(this,ze).findAll(n).filter(u=>!u.isDisabled()).map(u=>{let d=u.fetch(void 0,s);return s.throwOnError||(d=d.catch(_t)),u.state.fetchStatus==="paused"?Promise.resolve():d}));return Promise.all(l).then(_t)}fetchQuery(n){const r=this.defaultQueryOptions(n);r.retry===void 0&&(r.retry=!1);const s=P(this,ze).build(this,r);return s.isStaleByTime(so(r.staleTime,s))?s.fetch(r):Promise.resolve(s.state.data)}prefetchQuery(n){return this.fetchQuery(n).then(_t).catch(_t)}fetchInfiniteQuery(n){return n.behavior=zh(n.pages),this.fetchQuery(n)}prefetchInfiniteQuery(n){return this.fetchInfiniteQuery(n).then(_t).catch(_t)}ensureInfiniteQueryData(n){return n.behavior=zh(n.pages),this.ensureQueryData(n)}resumePausedMutations(){return sl.isOnline()?P(this,Kn).resumePausedMutations():Promise.resolve()}getQueryCache(){return P(this,ze)}getMutationCache(){return P(this,Kn)}getDefaultOptions(){return P(this,Gn)}setDefaultOptions(n){X(this,Gn,n)}setQueryDefaults(n,r){P(this,po).set(Tr(n),{queryKey:n,defaultOptions:r})}getQueryDefaults(n){const r=[...P(this,po).values()];let s={};return r.forEach(l=>{bs(n,l.queryKey)&&(s={...s,...l.defaultOptions})}),s}setMutationDefaults(n,r){P(this,mo).set(Tr(n),{mutationKey:n,defaultOptions:r})}getMutationDefaults(n){const r=[...P(this,mo).values()];let s={};return r.forEach(l=>{bs(n,l.mutationKey)&&(s={...s,...l.defaultOptions})}),s}defaultQueryOptions(n){if(n._defaulted)return n;const r={...P(this,Gn).queries,...this.getQueryDefaults(n.queryKey),...n,_defaulted:!0};return r.queryHash||(r.queryHash=ic(r.queryKey,r)),r.refetchOnReconnect===void 0&&(r.refetchOnReconnect=r.networkMode!=="always"),r.throwOnError===void 0&&(r.throwOnError=!!r.suspense),!r.networkMode&&r.persister&&(r.networkMode="offlineFirst"),r.enabled!==!0&&r.queryFn===lc&&(r.enabled=!1),r}defaultMutationOptions(n){return n!=null&&n._defaulted?n:{...P(this,Gn).mutations,...(n==null?void 0:n.mutationKey)&&this.getMutationDefaults(n.mutationKey),...n,_defaulted:!0}}clear(){P(this,ze).clear(),P(this,Kn).clear()}},ze=new WeakMap,Kn=new WeakMap,Gn=new WeakMap,po=new WeakMap,mo=new WeakMap,Xn=new WeakMap,go=new WeakMap,yo=new WeakMap,Ep),gt,we,js,ut,kr,vo,Jn,nn,Ms,xo,wo,br,Pr,Zn,So,Pe,ws,Fu,zu,Wu,Uu,$u,Hu,Vu,Qp,kp,mx=(kp=class extends ko{constructor(r,s){super();le(this,Pe);le(this,gt);le(this,we);le(this,js);le(this,ut);le(this,kr);le(this,vo);le(this,Jn);le(this,nn);le(this,Ms);le(this,xo);le(this,wo);le(this,br);le(this,Pr);le(this,Zn);le(this,So,new Set);this.options=s,X(this,gt,r),X(this,nn,null),X(this,Jn,Iu()),this.options.experimental_prefetchInRender||P(this,Jn).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(P(this,we).addObserver(this),Uh(P(this,we),this.options)?ye(this,Pe,ws).call(this):this.updateResult(),ye(this,Pe,Uu).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Bu(P(this,we),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Bu(P(this,we),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,ye(this,Pe,$u).call(this),ye(this,Pe,Hu).call(this),P(this,we).removeObserver(this)}setOptions(r,s){const l=this.options,u=P(this,we);if(this.options=P(this,gt).defaultQueryOptions(r),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof $t(this.options.enabled,P(this,we))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");ye(this,Pe,Vu).call(this),P(this,we).setOptions(this.options),l._defaulted&&!ol(this.options,l)&&P(this,gt).getQueryCache().notify({type:"observerOptionsUpdated",query:P(this,we),observer:this});const d=this.hasListeners();d&&$h(P(this,we),u,this.options,l)&&ye(this,Pe,ws).call(this),this.updateResult(s),d&&(P(this,we)!==u||$t(this.options.enabled,P(this,we))!==$t(l.enabled,P(this,we))||so(this.options.staleTime,P(this,we))!==so(l.staleTime,P(this,we)))&&ye(this,Pe,Fu).call(this);const f=ye(this,Pe,zu).call(this);d&&(P(this,we)!==u||$t(this.options.enabled,P(this,we))!==$t(l.enabled,P(this,we))||f!==P(this,Zn))&&ye(this,Pe,Wu).call(this,f)}getOptimisticResult(r){const s=P(this,gt).getQueryCache().build(P(this,gt),r),l=this.createResult(s,r);return yx(this,l)&&(X(this,ut,l),X(this,vo,this.options),X(this,kr,P(this,we).state)),l}getCurrentResult(){return P(this,ut)}trackResult(r,s){const l={};return Object.keys(r).forEach(u=>{Object.defineProperty(l,u,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(u),s==null||s(u),r[u])})}),l}trackProp(r){P(this,So).add(r)}getCurrentQuery(){return P(this,we)}refetch({...r}={}){return this.fetch({...r})}fetchOptimistic(r){const s=P(this,gt).defaultQueryOptions(r),l=P(this,gt).getQueryCache().build(P(this,gt),s);return l.fetch().then(()=>this.createResult(l,s))}fetch(r){return ye(this,Pe,ws).call(this,{...r,cancelRefetch:r.cancelRefetch??!0}).then(()=>(this.updateResult(),P(this,ut)))}createResult(r,s){var J;const l=P(this,we),u=this.options,d=P(this,ut),f=P(this,kr),p=P(this,vo),y=r!==l?r.state:P(this,js),{state:x}=r;let v={...x},E=!1,N;if(s._optimisticResults){const re=this.hasListeners(),de=!re&&Uh(r,s),pe=re&&$h(r,l,s,u);(de||pe)&&(v={...v,...Vp(x.data,r.options)}),s._optimisticResults==="isRestoring"&&(v.fetchStatus="idle")}let{error:j,errorUpdatedAt:S,status:k}=v;if(s.select&&v.data!==void 0)if(d&&v.data===(f==null?void 0:f.data)&&s.select===P(this,Ms))N=P(this,xo);else try{X(this,Ms,s.select),N=s.select(v.data),N=Lu(d==null?void 0:d.data,N,s),X(this,xo,N),X(this,nn,null)}catch(re){X(this,nn,re)}else N=v.data;if(s.placeholderData!==void 0&&N===void 0&&k==="pending"){let re;if(d!=null&&d.isPlaceholderData&&s.placeholderData===(p==null?void 0:p.placeholderData))re=d.data;else if(re=typeof s.placeholderData=="function"?s.placeholderData((J=P(this,wo))==null?void 0:J.state.data,P(this,wo)):s.placeholderData,s.select&&re!==void 0)try{re=s.select(re),X(this,nn,null)}catch(de){X(this,nn,de)}re!==void 0&&(k="success",N=Lu(d==null?void 0:d.data,re,s),E=!0)}P(this,nn)&&(j=P(this,nn),N=P(this,xo),S=Date.now(),k="error");const A=v.fetchStatus==="fetching",D=k==="pending",I=k==="error",F=D&&A,H=N!==void 0,B={status:k,fetchStatus:v.fetchStatus,isPending:D,isSuccess:k==="success",isError:I,isInitialLoading:F,isLoading:F,data:N,dataUpdatedAt:v.dataUpdatedAt,error:j,errorUpdatedAt:S,failureCount:v.fetchFailureCount,failureReason:v.fetchFailureReason,errorUpdateCount:v.errorUpdateCount,isFetched:v.dataUpdateCount>0||v.errorUpdateCount>0,isFetchedAfterMount:v.dataUpdateCount>y.dataUpdateCount||v.errorUpdateCount>y.errorUpdateCount,isFetching:A,isRefetching:A&&!D,isLoadingError:I&&!H,isPaused:v.fetchStatus==="paused",isPlaceholderData:E,isRefetchError:I&&H,isStale:uc(r,s),refetch:this.refetch,promise:P(this,Jn)};if(this.options.experimental_prefetchInRender){const re=ee=>{B.status==="error"?ee.reject(B.error):B.data!==void 0&&ee.resolve(B.data)},de=()=>{const ee=X(this,Jn,B.promise=Iu());re(ee)},pe=P(this,Jn);switch(pe.status){case"pending":r.queryHash===l.queryHash&&re(pe);break;case"fulfilled":(B.status==="error"||B.data!==pe.value)&&de();break;case"rejected":(B.status!=="error"||B.error!==pe.reason)&&de();break}}return B}updateResult(r){const s=P(this,ut),l=this.createResult(P(this,we),this.options);if(X(this,kr,P(this,we).state),X(this,vo,this.options),P(this,kr).data!==void 0&&X(this,wo,P(this,we)),ol(l,s))return;X(this,ut,l);const u={},d=()=>{if(!s)return!0;const{notifyOnChangeProps:f}=this.options,p=typeof f=="function"?f():f;if(p==="all"||!p&&!P(this,So).size)return!0;const g=new Set(p??P(this,So));return this.options.throwOnError&&g.add("error"),Object.keys(P(this,ut)).some(y=>{const x=y;return P(this,ut)[x]!==s[x]&&g.has(x)})};(r==null?void 0:r.listeners)!==!1&&d()&&(u.listeners=!0),ye(this,Pe,Qp).call(this,{...u,...r})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&ye(this,Pe,Uu).call(this)}},gt=new WeakMap,we=new WeakMap,js=new WeakMap,ut=new WeakMap,kr=new WeakMap,vo=new WeakMap,Jn=new WeakMap,nn=new WeakMap,Ms=new WeakMap,xo=new WeakMap,wo=new WeakMap,br=new WeakMap,Pr=new WeakMap,Zn=new WeakMap,So=new WeakMap,Pe=new WeakSet,ws=function(r){ye(this,Pe,Vu).call(this);let s=P(this,we).fetch(this.options,r);return r!=null&&r.throwOnError||(s=s.catch(_t)),s},Fu=function(){ye(this,Pe,$u).call(this);const r=so(this.options.staleTime,P(this,we));if(Nr||P(this,ut).isStale||!Au(r))return;const l=Ip(P(this,ut).dataUpdatedAt,r)+1;X(this,br,setTimeout(()=>{P(this,ut).isStale||this.updateResult()},l))},zu=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(P(this,we)):this.options.refetchInterval)??!1},Wu=function(r){ye(this,Pe,Hu).call(this),X(this,Zn,r),!(Nr||$t(this.options.enabled,P(this,we))===!1||!Au(P(this,Zn))||P(this,Zn)===0)&&X(this,Pr,setInterval(()=>{(this.options.refetchIntervalInBackground||ac.isFocused())&&ye(this,Pe,ws).call(this)},P(this,Zn)))},Uu=function(){ye(this,Pe,Fu).call(this),ye(this,Pe,Wu).call(this,ye(this,Pe,zu).call(this))},$u=function(){P(this,br)&&(clearTimeout(P(this,br)),X(this,br,void 0))},Hu=function(){P(this,Pr)&&(clearInterval(P(this,Pr)),X(this,Pr,void 0))},Vu=function(){const r=P(this,gt).getQueryCache().build(P(this,gt),this.options);if(r===P(this,we))return;const s=P(this,we);X(this,we,r),X(this,js,r.state),this.hasListeners()&&(s==null||s.removeObserver(this),r.addObserver(this))},Qp=function(r){Be.batch(()=>{r.listeners&&this.listeners.forEach(s=>{s(P(this,ut))}),P(this,gt).getQueryCache().notify({query:P(this,we),type:"observerResultsUpdated"})})},kp);function gx(n,r){return $t(r.enabled,n)!==!1&&n.state.data===void 0&&!(n.state.status==="error"&&r.retryOnMount===!1)}function Uh(n,r){return gx(n,r)||n.state.data!==void 0&&Bu(n,r,r.refetchOnMount)}function Bu(n,r,s){if($t(r.enabled,n)!==!1){const l=typeof s=="function"?s(n):s;return l==="always"||l!==!1&&uc(n,r)}return!1}function $h(n,r,s,l){return(n!==r||$t(l.enabled,n)===!1)&&(!s.suspense||n.state.status!=="error")&&uc(n,s)}function uc(n,r){return $t(r.enabled,n)!==!1&&n.isStaleByTime(so(r.staleTime,n))}function yx(n,r){return!ol(n.getCurrentResult(),r)}var er,tr,yt,vn,xn,Xi,Qu,bp,vx=(bp=class extends ko{constructor(s,l){super();le(this,xn);le(this,er);le(this,tr);le(this,yt);le(this,vn);X(this,er,s),this.setOptions(l),this.bindMethods(),ye(this,xn,Xi).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(s){var u;const l=this.options;this.options=P(this,er).defaultMutationOptions(s),ol(this.options,l)||P(this,er).getMutationCache().notify({type:"observerOptionsUpdated",mutation:P(this,yt),observer:this}),l!=null&&l.mutationKey&&this.options.mutationKey&&Tr(l.mutationKey)!==Tr(this.options.mutationKey)?this.reset():((u=P(this,yt))==null?void 0:u.state.status)==="pending"&&P(this,yt).setOptions(this.options)}onUnsubscribe(){var s;this.hasListeners()||(s=P(this,yt))==null||s.removeObserver(this)}onMutationUpdate(s){ye(this,xn,Xi).call(this),ye(this,xn,Qu).call(this,s)}getCurrentResult(){return P(this,tr)}reset(){var s;(s=P(this,yt))==null||s.removeObserver(this),X(this,yt,void 0),ye(this,xn,Xi).call(this),ye(this,xn,Qu).call(this)}mutate(s,l){var u;return X(this,vn,l),(u=P(this,yt))==null||u.removeObserver(this),X(this,yt,P(this,er).getMutationCache().build(P(this,er),this.options)),P(this,yt).addObserver(this),P(this,yt).execute(s)}},er=new WeakMap,tr=new WeakMap,yt=new WeakMap,vn=new WeakMap,xn=new WeakSet,Xi=function(){var l;const s=((l=P(this,yt))==null?void 0:l.state)??Bp();X(this,tr,{...s,isPending:s.status==="pending",isSuccess:s.status==="success",isError:s.status==="error",isIdle:s.status==="idle",mutate:this.mutate,reset:this.reset})},Qu=function(s){Be.batch(()=>{var l,u,d,f,p,g,y,x;if(P(this,vn)&&this.hasListeners()){const v=P(this,tr).variables,E=P(this,tr).context;(s==null?void 0:s.type)==="success"?((u=(l=P(this,vn)).onSuccess)==null||u.call(l,s.data,v,E),(f=(d=P(this,vn)).onSettled)==null||f.call(d,s.data,null,v,E)):(s==null?void 0:s.type)==="error"&&((g=(p=P(this,vn)).onError)==null||g.call(p,s.error,v,E),(x=(y=P(this,vn)).onSettled)==null||x.call(y,void 0,s.error,v,E))}this.listeners.forEach(v=>{v(P(this,tr))})})},bp),qp=C.createContext(void 0),Yp=n=>{const r=C.useContext(qp);if(!r)throw new Error("No QueryClient set, use QueryClientProvider to set one");return r},xx=({client:n,children:r})=>(C.useEffect(()=>(n.mount(),()=>{n.unmount()}),[n]),m.jsx(qp.Provider,{value:n,children:r})),Kp=C.createContext(!1),wx=()=>C.useContext(Kp);Kp.Provider;function Sx(){let n=!1;return{clearReset:()=>{n=!1},reset:()=>{n=!0},isReset:()=>n}}var Cx=C.createContext(Sx()),Ex=()=>C.useContext(Cx);function Gp(n,r){return typeof n=="function"?n(...r):!!n}function qu(){}var kx=(n,r)=>{(n.suspense||n.throwOnError||n.experimental_prefetchInRender)&&(r.isReset()||(n.retryOnMount=!1))},bx=n=>{C.useEffect(()=>{n.clearReset()},[n])},Px=({result:n,errorResetBoundary:r,throwOnError:s,query:l})=>n.isError&&!r.isReset()&&!n.isFetching&&l&&Gp(s,[n.error,l]),Nx=n=>{n.suspense&&(n.staleTime===void 0&&(n.staleTime=1e3),typeof n.gcTime=="number"&&(n.gcTime=Math.max(n.gcTime,1e3)))},Tx=(n,r)=>n.isLoading&&n.isFetching&&!r,Rx=(n,r)=>(n==null?void 0:n.suspense)&&r.isPending,Hh=(n,r,s)=>r.fetchOptimistic(n).catch(()=>{s.clearReset()});function Ox(n,r,s){var x,v,E,N,j;const l=Yp(),u=wx(),d=Ex(),f=l.defaultQueryOptions(n);(v=(x=l.getDefaultOptions().queries)==null?void 0:x._experimental_beforeQuery)==null||v.call(x,f),f._optimisticResults=u?"isRestoring":"optimistic",Nx(f),kx(f,d),bx(d);const p=!l.getQueryCache().get(f.queryHash),[g]=C.useState(()=>new r(l,f)),y=g.getOptimisticResult(f);if(C.useSyncExternalStore(C.useCallback(S=>{const k=u?qu:g.subscribe(Be.batchCalls(S));return g.updateResult(),k},[g,u]),()=>g.getCurrentResult(),()=>g.getCurrentResult()),C.useEffect(()=>{g.setOptions(f,{listeners:!1})},[f,g]),Rx(f,y))throw Hh(f,g,d);if(Px({result:y,errorResetBoundary:d,throwOnError:f.throwOnError,query:l.getQueryCache().get(f.queryHash)}))throw y.error;if((N=(E=l.getDefaultOptions().queries)==null?void 0:E._experimental_afterQuery)==null||N.call(E,f,y),f.experimental_prefetchInRender&&!Nr&&Tx(y,u)){const S=p?Hh(f,g,d):(j=l.getQueryCache().get(f.queryHash))==null?void 0:j.promise;S==null||S.catch(qu).finally(()=>{g.updateResult()})}return f.notifyOnChangeProps?y:g.trackResult(y)}function bu(n,r){return Ox(n,mx)}function jx(n,r){const s=Yp(),[l]=C.useState(()=>new vx(s,n));C.useEffect(()=>{l.setOptions(n)},[l,n]);const u=C.useSyncExternalStore(C.useCallback(f=>l.subscribe(Be.batchCalls(f)),[l]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),d=C.useCallback((f,p)=>{l.mutate(f,p).catch(qu)},[l]);if(u.error&&Gp(l.options.throwOnError,[u.error]))throw u.error;return{...u,mutate:d,mutateAsync:u.mutate}}async function Xp(n){if(!n.ok){const r=await n.text()||n.statusText;throw new Error(`${n.status}: ${r}`)}}async function Mx(n,r,s){const l=await fetch(r,{method:n,headers:s?{"Content-Type":"application/json"}:{},body:s?JSON.stringify(s):void 0,credentials:"include"});return await Xp(l),l}const Ji=({on401:n})=>async({queryKey:r})=>{const s=await fetch(r[0],{credentials:"include"});return await Xp(s),await s.json()},_x=new px({defaultOptions:{queries:{queryFn:Ji({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}}),Ax=1,Dx=1e6;let Pu=0;function Lx(){return Pu=(Pu+1)%Number.MAX_SAFE_INTEGER,Pu.toString()}const Nu=new Map,Vh=n=>{if(Nu.has(n))return;const r=setTimeout(()=>{Nu.delete(n),Cs({type:"REMOVE_TOAST",toastId:n})},Dx);Nu.set(n,r)},Ix=(n,r)=>{switch(r.type){case"ADD_TOAST":return{...n,toasts:[r.toast,...n.toasts].slice(0,Ax)};case"UPDATE_TOAST":return{...n,toasts:n.toasts.map(s=>s.id===r.toast.id?{...s,...r.toast}:s)};case"DISMISS_TOAST":{const{toastId:s}=r;return s?Vh(s):n.toasts.forEach(l=>{Vh(l.id)}),{...n,toasts:n.toasts.map(l=>l.id===s||s===void 0?{...l,open:!1}:l)}}case"REMOVE_TOAST":return r.toastId===void 0?{...n,toasts:[]}:{...n,toasts:n.toasts.filter(s=>s.id!==r.toastId)}}},Zi=[];let el={toasts:[]};function Cs(n){el=Ix(el,n),Zi.forEach(r=>{r(el)})}function Fx({...n}){const r=Lx(),s=u=>Cs({type:"UPDATE_TOAST",toast:{...u,id:r}}),l=()=>Cs({type:"DISMISS_TOAST",toastId:r});return Cs({type:"ADD_TOAST",toast:{...n,id:r,open:!0,onOpenChange:u=>{u||l()}}}),{id:r,dismiss:l,update:s}}function Jp(){const[n,r]=C.useState(el);return C.useEffect(()=>(Zi.push(r),()=>{const s=Zi.indexOf(r);s>-1&&Zi.splice(s,1)}),[n]),{...n,toast:Fx,dismiss:s=>Cs({type:"DISMISS_TOAST",toastId:s})}}var pl=Np();const zx=Pp(pl);function Qe(n,r,{checkForDefaultPrevented:s=!0}={}){return function(u){if(n==null||n(u),s===!1||!u.defaultPrevented)return r==null?void 0:r(u)}}function Bh(n,r){if(typeof n=="function")return n(r);n!=null&&(n.current=r)}function Zp(...n){return r=>{let s=!1;const l=n.map(u=>{const d=Bh(u,r);return!s&&typeof d=="function"&&(s=!0),d});if(s)return()=>{for(let u=0;u<l.length;u++){const d=l[u];typeof d=="function"?d():Bh(n[u],null)}}}}function Ht(...n){return C.useCallback(Zp(...n),n)}function ml(n,r=[]){let s=[];function l(d,f){const p=C.createContext(f),g=s.length;s=[...s,f];const y=v=>{var A;const{scope:E,children:N,...j}=v,S=((A=E==null?void 0:E[n])==null?void 0:A[g])||p,k=C.useMemo(()=>j,Object.values(j));return m.jsx(S.Provider,{value:k,children:N})};y.displayName=d+"Provider";function x(v,E){var S;const N=((S=E==null?void 0:E[n])==null?void 0:S[g])||p,j=C.useContext(N);if(j)return j;if(f!==void 0)return f;throw new Error(`\`${v}\` must be used within \`${d}\``)}return[y,x]}const u=()=>{const d=s.map(f=>C.createContext(f));return function(p){const g=(p==null?void 0:p[n])||d;return C.useMemo(()=>({[`__scope${n}`]:{...p,[n]:g}}),[p,g])}};return u.scopeName=n,[l,Wx(u,...r)]}function Wx(...n){const r=n[0];if(n.length===1)return r;const s=()=>{const l=n.map(u=>({useScope:u(),scopeName:u.scopeName}));return function(d){const f=l.reduce((p,{useScope:g,scopeName:y})=>{const v=g(d)[`__scope${y}`];return{...p,...v}},{});return C.useMemo(()=>({[`__scope${r.scopeName}`]:f}),[f])}};return s.scopeName=r.scopeName,s}function il(n){const r=$x(n),s=C.forwardRef((l,u)=>{const{children:d,...f}=l,p=C.Children.toArray(d),g=p.find(Vx);if(g){const y=g.props.children,x=p.map(v=>v===g?C.Children.count(y)>1?C.Children.only(null):C.isValidElement(y)?y.props.children:null:v);return m.jsx(r,{...f,ref:u,children:C.isValidElement(y)?C.cloneElement(y,void 0,x):null})}return m.jsx(r,{...f,ref:u,children:d})});return s.displayName=`${n}.Slot`,s}var Ux=il("Slot");function $x(n){const r=C.forwardRef((s,l)=>{const{children:u,...d}=s;if(C.isValidElement(u)){const f=Qx(u),p=Bx(d,u.props);return u.type!==C.Fragment&&(p.ref=l?Zp(l,f):f),C.cloneElement(u,p)}return C.Children.count(u)>1?C.Children.only(null):null});return r.displayName=`${n}.SlotClone`,r}var em=Symbol("radix.slottable");function Hx(n){const r=({children:s})=>m.jsx(m.Fragment,{children:s});return r.displayName=`${n}.Slottable`,r.__radixId=em,r}function Vx(n){return C.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===em}function Bx(n,r){const s={...r};for(const l in r){const u=n[l],d=r[l];/^on[A-Z]/.test(l)?u&&d?s[l]=(...p)=>{d(...p),u(...p)}:u&&(s[l]=u):l==="style"?s[l]={...u,...d}:l==="className"&&(s[l]=[u,d].filter(Boolean).join(" "))}return{...n,...s}}function Qx(n){var l,u;let r=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=r&&"isReactWarning"in r&&r.isReactWarning;return s?n.ref:(r=(u=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:u.get,s=r&&"isReactWarning"in r&&r.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}function qx(n){const r=n+"CollectionProvider",[s,l]=ml(r),[u,d]=s(r,{collectionRef:{current:null},itemMap:new Map}),f=S=>{const{scope:k,children:A}=S,D=Bn.useRef(null),I=Bn.useRef(new Map).current;return m.jsx(u,{scope:k,itemMap:I,collectionRef:D,children:A})};f.displayName=r;const p=n+"CollectionSlot",g=il(p),y=Bn.forwardRef((S,k)=>{const{scope:A,children:D}=S,I=d(p,A),F=Ht(k,I.collectionRef);return m.jsx(g,{ref:F,children:D})});y.displayName=p;const x=n+"CollectionItemSlot",v="data-radix-collection-item",E=il(x),N=Bn.forwardRef((S,k)=>{const{scope:A,children:D,...I}=S,F=Bn.useRef(null),H=Ht(k,F),q=d(x,A);return Bn.useEffect(()=>(q.itemMap.set(F,{ref:F,...I}),()=>void q.itemMap.delete(F))),m.jsx(E,{[v]:"",ref:H,children:D})});N.displayName=x;function j(S){const k=d(n+"CollectionConsumer",S);return Bn.useCallback(()=>{const D=k.collectionRef.current;if(!D)return[];const I=Array.from(D.querySelectorAll(`[${v}]`));return Array.from(k.itemMap.values()).sort((q,B)=>I.indexOf(q.ref.current)-I.indexOf(B.ref.current))},[k.collectionRef,k.itemMap])}return[{Provider:f,Slot:y,ItemSlot:N},j,l]}var Yx=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],vt=Yx.reduce((n,r)=>{const s=il(`Primitive.${r}`),l=C.forwardRef((u,d)=>{const{asChild:f,...p}=u,g=f?s:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),m.jsx(g,{...p,ref:d})});return l.displayName=`Primitive.${r}`,{...n,[r]:l}},{});function tm(n,r){n&&pl.flushSync(()=>n.dispatchEvent(r))}function on(n){const r=C.useRef(n);return C.useEffect(()=>{r.current=n}),C.useMemo(()=>(...s)=>{var l;return(l=r.current)==null?void 0:l.call(r,...s)},[])}function Kx(n,r=globalThis==null?void 0:globalThis.document){const s=on(n);C.useEffect(()=>{const l=u=>{u.key==="Escape"&&s(u)};return r.addEventListener("keydown",l,{capture:!0}),()=>r.removeEventListener("keydown",l,{capture:!0})},[s,r])}var Gx="DismissableLayer",Yu="dismissableLayer.update",Xx="dismissableLayer.pointerDownOutside",Jx="dismissableLayer.focusOutside",Qh,nm=C.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),cc=C.forwardRef((n,r)=>{const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:d,onInteractOutside:f,onDismiss:p,...g}=n,y=C.useContext(nm),[x,v]=C.useState(null),E=(x==null?void 0:x.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,N]=C.useState({}),j=Ht(r,B=>v(B)),S=Array.from(y.layers),[k]=[...y.layersWithOutsidePointerEventsDisabled].slice(-1),A=S.indexOf(k),D=x?S.indexOf(x):-1,I=y.layersWithOutsidePointerEventsDisabled.size>0,F=D>=A,H=e0(B=>{const J=B.target,re=[...y.branches].some(de=>de.contains(J));!F||re||(u==null||u(B),f==null||f(B),B.defaultPrevented||p==null||p())},E),q=t0(B=>{const J=B.target;[...y.branches].some(de=>de.contains(J))||(d==null||d(B),f==null||f(B),B.defaultPrevented||p==null||p())},E);return Kx(B=>{D===y.layers.size-1&&(l==null||l(B),!B.defaultPrevented&&p&&(B.preventDefault(),p()))},E),C.useEffect(()=>{if(x)return s&&(y.layersWithOutsidePointerEventsDisabled.size===0&&(Qh=E.body.style.pointerEvents,E.body.style.pointerEvents="none"),y.layersWithOutsidePointerEventsDisabled.add(x)),y.layers.add(x),qh(),()=>{s&&y.layersWithOutsidePointerEventsDisabled.size===1&&(E.body.style.pointerEvents=Qh)}},[x,E,s,y]),C.useEffect(()=>()=>{x&&(y.layers.delete(x),y.layersWithOutsidePointerEventsDisabled.delete(x),qh())},[x,y]),C.useEffect(()=>{const B=()=>N({});return document.addEventListener(Yu,B),()=>document.removeEventListener(Yu,B)},[]),m.jsx(vt.div,{...g,ref:j,style:{pointerEvents:I?F?"auto":"none":void 0,...n.style},onFocusCapture:Qe(n.onFocusCapture,q.onFocusCapture),onBlurCapture:Qe(n.onBlurCapture,q.onBlurCapture),onPointerDownCapture:Qe(n.onPointerDownCapture,H.onPointerDownCapture)})});cc.displayName=Gx;var Zx="DismissableLayerBranch",rm=C.forwardRef((n,r)=>{const s=C.useContext(nm),l=C.useRef(null),u=Ht(r,l);return C.useEffect(()=>{const d=l.current;if(d)return s.branches.add(d),()=>{s.branches.delete(d)}},[s.branches]),m.jsx(vt.div,{...n,ref:u})});rm.displayName=Zx;function e0(n,r=globalThis==null?void 0:globalThis.document){const s=on(n),l=C.useRef(!1),u=C.useRef(()=>{});return C.useEffect(()=>{const d=p=>{if(p.target&&!l.current){let g=function(){om(Xx,s,y,{discrete:!0})};const y={originalEvent:p};p.pointerType==="touch"?(r.removeEventListener("click",u.current),u.current=g,r.addEventListener("click",u.current,{once:!0})):g()}else r.removeEventListener("click",u.current);l.current=!1},f=window.setTimeout(()=>{r.addEventListener("pointerdown",d)},0);return()=>{window.clearTimeout(f),r.removeEventListener("pointerdown",d),r.removeEventListener("click",u.current)}},[r,s]),{onPointerDownCapture:()=>l.current=!0}}function t0(n,r=globalThis==null?void 0:globalThis.document){const s=on(n),l=C.useRef(!1);return C.useEffect(()=>{const u=d=>{d.target&&!l.current&&om(Jx,s,{originalEvent:d},{discrete:!1})};return r.addEventListener("focusin",u),()=>r.removeEventListener("focusin",u)},[r,s]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}function qh(){const n=new CustomEvent(Yu);document.dispatchEvent(n)}function om(n,r,s,{discrete:l}){const u=s.originalEvent.target,d=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:s});r&&u.addEventListener(n,r,{once:!0}),l?tm(u,d):u.dispatchEvent(d)}var n0=cc,r0=rm,Rr=globalThis!=null&&globalThis.document?C.useLayoutEffect:()=>{},o0="Portal",sm=C.forwardRef((n,r)=>{var p;const{container:s,...l}=n,[u,d]=C.useState(!1);Rr(()=>d(!0),[]);const f=s||u&&((p=globalThis==null?void 0:globalThis.document)==null?void 0:p.body);return f?zx.createPortal(m.jsx(vt.div,{...l,ref:r}),f):null});sm.displayName=o0;function s0(n,r){return C.useReducer((s,l)=>r[s][l]??s,n)}var dc=n=>{const{present:r,children:s}=n,l=i0(r),u=typeof s=="function"?s({present:l.isPresent}):C.Children.only(s),d=Ht(l.ref,l0(u));return typeof s=="function"||l.isPresent?C.cloneElement(u,{ref:d}):null};dc.displayName="Presence";function i0(n){const[r,s]=C.useState(),l=C.useRef({}),u=C.useRef(n),d=C.useRef("none"),f=n?"mounted":"unmounted",[p,g]=s0(f,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return C.useEffect(()=>{const y=Yi(l.current);d.current=p==="mounted"?y:"none"},[p]),Rr(()=>{const y=l.current,x=u.current;if(x!==n){const E=d.current,N=Yi(y);n?g("MOUNT"):N==="none"||(y==null?void 0:y.display)==="none"?g("UNMOUNT"):g(x&&E!==N?"ANIMATION_OUT":"UNMOUNT"),u.current=n}},[n,g]),Rr(()=>{if(r){let y;const x=r.ownerDocument.defaultView??window,v=N=>{const S=Yi(l.current).includes(N.animationName);if(N.target===r&&S&&(g("ANIMATION_END"),!u.current)){const k=r.style.animationFillMode;r.style.animationFillMode="forwards",y=x.setTimeout(()=>{r.style.animationFillMode==="forwards"&&(r.style.animationFillMode=k)})}},E=N=>{N.target===r&&(d.current=Yi(l.current))};return r.addEventListener("animationstart",E),r.addEventListener("animationcancel",v),r.addEventListener("animationend",v),()=>{x.clearTimeout(y),r.removeEventListener("animationstart",E),r.removeEventListener("animationcancel",v),r.removeEventListener("animationend",v)}}else g("ANIMATION_END")},[r,g]),{isPresent:["mounted","unmountSuspended"].includes(p),ref:C.useCallback(y=>{y&&(l.current=getComputedStyle(y)),s(y)},[])}}function Yi(n){return(n==null?void 0:n.animationName)||"none"}function l0(n){var l,u;let r=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=r&&"isReactWarning"in r&&r.isReactWarning;return s?n.ref:(r=(u=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:u.get,s=r&&"isReactWarning"in r&&r.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}function a0({prop:n,defaultProp:r,onChange:s=()=>{}}){const[l,u]=u0({defaultProp:r,onChange:s}),d=n!==void 0,f=d?n:l,p=on(s),g=C.useCallback(y=>{if(d){const v=typeof y=="function"?y(n):y;v!==n&&p(v)}else u(y)},[d,n,u,p]);return[f,g]}function u0({defaultProp:n,onChange:r}){const s=C.useState(n),[l]=s,u=C.useRef(l),d=on(r);return C.useEffect(()=>{u.current!==l&&(d(l),u.current=l)},[l,u,d]),s}var c0="VisuallyHidden",gl=C.forwardRef((n,r)=>m.jsx(vt.span,{...n,ref:r,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...n.style}}));gl.displayName=c0;var d0=gl,fc="ToastProvider",[hc,f0,h0]=qx("Toast"),[im,CC]=ml("Toast",[h0]),[p0,yl]=im(fc),lm=n=>{const{__scopeToast:r,label:s="Notification",duration:l=5e3,swipeDirection:u="right",swipeThreshold:d=50,children:f}=n,[p,g]=C.useState(null),[y,x]=C.useState(0),v=C.useRef(!1),E=C.useRef(!1);return s.trim()||console.error(`Invalid prop \`label\` supplied to \`${fc}\`. Expected non-empty \`string\`.`),m.jsx(hc.Provider,{scope:r,children:m.jsx(p0,{scope:r,label:s,duration:l,swipeDirection:u,swipeThreshold:d,toastCount:y,viewport:p,onViewportChange:g,onToastAdd:C.useCallback(()=>x(N=>N+1),[]),onToastRemove:C.useCallback(()=>x(N=>N-1),[]),isFocusedToastEscapeKeyDownRef:v,isClosePausedRef:E,children:f})})};lm.displayName=fc;var am="ToastViewport",m0=["F8"],Ku="toast.viewportPause",Gu="toast.viewportResume",um=C.forwardRef((n,r)=>{const{__scopeToast:s,hotkey:l=m0,label:u="Notifications ({hotkey})",...d}=n,f=yl(am,s),p=f0(s),g=C.useRef(null),y=C.useRef(null),x=C.useRef(null),v=C.useRef(null),E=Ht(r,v,f.onViewportChange),N=l.join("+").replace(/Key/g,"").replace(/Digit/g,""),j=f.toastCount>0;C.useEffect(()=>{const k=A=>{var I;l.length!==0&&l.every(F=>A[F]||A.code===F)&&((I=v.current)==null||I.focus())};return document.addEventListener("keydown",k),()=>document.removeEventListener("keydown",k)},[l]),C.useEffect(()=>{const k=g.current,A=v.current;if(j&&k&&A){const D=()=>{if(!f.isClosePausedRef.current){const q=new CustomEvent(Ku);A.dispatchEvent(q),f.isClosePausedRef.current=!0}},I=()=>{if(f.isClosePausedRef.current){const q=new CustomEvent(Gu);A.dispatchEvent(q),f.isClosePausedRef.current=!1}},F=q=>{!k.contains(q.relatedTarget)&&I()},H=()=>{k.contains(document.activeElement)||I()};return k.addEventListener("focusin",D),k.addEventListener("focusout",F),k.addEventListener("pointermove",D),k.addEventListener("pointerleave",H),window.addEventListener("blur",D),window.addEventListener("focus",I),()=>{k.removeEventListener("focusin",D),k.removeEventListener("focusout",F),k.removeEventListener("pointermove",D),k.removeEventListener("pointerleave",H),window.removeEventListener("blur",D),window.removeEventListener("focus",I)}}},[j,f.isClosePausedRef]);const S=C.useCallback(({tabbingDirection:k})=>{const D=p().map(I=>{const F=I.ref.current,H=[F,...T0(F)];return k==="forwards"?H:H.reverse()});return(k==="forwards"?D.reverse():D).flat()},[p]);return C.useEffect(()=>{const k=v.current;if(k){const A=D=>{var H,q,B;const I=D.altKey||D.ctrlKey||D.metaKey;if(D.key==="Tab"&&!I){const J=document.activeElement,re=D.shiftKey;if(D.target===k&&re){(H=y.current)==null||H.focus();return}const ee=S({tabbingDirection:re?"backwards":"forwards"}),me=ee.findIndex(K=>K===J);Tu(ee.slice(me+1))?D.preventDefault():re?(q=y.current)==null||q.focus():(B=x.current)==null||B.focus()}};return k.addEventListener("keydown",A),()=>k.removeEventListener("keydown",A)}},[p,S]),m.jsxs(r0,{ref:g,role:"region","aria-label":u.replace("{hotkey}",N),tabIndex:-1,style:{pointerEvents:j?void 0:"none"},children:[j&&m.jsx(Xu,{ref:y,onFocusFromOutsideViewport:()=>{const k=S({tabbingDirection:"forwards"});Tu(k)}}),m.jsx(hc.Slot,{scope:s,children:m.jsx(vt.ol,{tabIndex:-1,...d,ref:E})}),j&&m.jsx(Xu,{ref:x,onFocusFromOutsideViewport:()=>{const k=S({tabbingDirection:"backwards"});Tu(k)}})]})});um.displayName=am;var cm="ToastFocusProxy",Xu=C.forwardRef((n,r)=>{const{__scopeToast:s,onFocusFromOutsideViewport:l,...u}=n,d=yl(cm,s);return m.jsx(gl,{"aria-hidden":!0,tabIndex:0,...u,ref:r,style:{position:"fixed"},onFocus:f=>{var y;const p=f.relatedTarget;!((y=d.viewport)!=null&&y.contains(p))&&l()}})});Xu.displayName=cm;var vl="Toast",g0="toast.swipeStart",y0="toast.swipeMove",v0="toast.swipeCancel",x0="toast.swipeEnd",dm=C.forwardRef((n,r)=>{const{forceMount:s,open:l,defaultOpen:u,onOpenChange:d,...f}=n,[p=!0,g]=a0({prop:l,defaultProp:u,onChange:d});return m.jsx(dc,{present:s||p,children:m.jsx(C0,{open:p,...f,ref:r,onClose:()=>g(!1),onPause:on(n.onPause),onResume:on(n.onResume),onSwipeStart:Qe(n.onSwipeStart,y=>{y.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Qe(n.onSwipeMove,y=>{const{x,y:v}=y.detail.delta;y.currentTarget.setAttribute("data-swipe","move"),y.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${x}px`),y.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${v}px`)}),onSwipeCancel:Qe(n.onSwipeCancel,y=>{y.currentTarget.setAttribute("data-swipe","cancel"),y.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),y.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),y.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),y.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Qe(n.onSwipeEnd,y=>{const{x,y:v}=y.detail.delta;y.currentTarget.setAttribute("data-swipe","end"),y.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),y.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),y.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${x}px`),y.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${v}px`),g(!1)})})})});dm.displayName=vl;var[w0,S0]=im(vl,{onClose(){}}),C0=C.forwardRef((n,r)=>{const{__scopeToast:s,type:l="foreground",duration:u,open:d,onClose:f,onEscapeKeyDown:p,onPause:g,onResume:y,onSwipeStart:x,onSwipeMove:v,onSwipeCancel:E,onSwipeEnd:N,...j}=n,S=yl(vl,s),[k,A]=C.useState(null),D=Ht(r,K=>A(K)),I=C.useRef(null),F=C.useRef(null),H=u||S.duration,q=C.useRef(0),B=C.useRef(H),J=C.useRef(0),{onToastAdd:re,onToastRemove:de}=S,pe=on(()=>{var ve;(k==null?void 0:k.contains(document.activeElement))&&((ve=S.viewport)==null||ve.focus()),f()}),ee=C.useCallback(K=>{!K||K===1/0||(window.clearTimeout(J.current),q.current=new Date().getTime(),J.current=window.setTimeout(pe,K))},[pe]);C.useEffect(()=>{const K=S.viewport;if(K){const ve=()=>{ee(B.current),y==null||y()},ue=()=>{const ae=new Date().getTime()-q.current;B.current=B.current-ae,window.clearTimeout(J.current),g==null||g()};return K.addEventListener(Ku,ue),K.addEventListener(Gu,ve),()=>{K.removeEventListener(Ku,ue),K.removeEventListener(Gu,ve)}}},[S.viewport,H,g,y,ee]),C.useEffect(()=>{d&&!S.isClosePausedRef.current&&ee(H)},[d,H,S.isClosePausedRef,ee]),C.useEffect(()=>(re(),()=>de()),[re,de]);const me=C.useMemo(()=>k?vm(k):null,[k]);return S.viewport?m.jsxs(m.Fragment,{children:[me&&m.jsx(E0,{__scopeToast:s,role:"status","aria-live":l==="foreground"?"assertive":"polite","aria-atomic":!0,children:me}),m.jsx(w0,{scope:s,onClose:pe,children:pl.createPortal(m.jsx(hc.ItemSlot,{scope:s,children:m.jsx(n0,{asChild:!0,onEscapeKeyDown:Qe(p,()=>{S.isFocusedToastEscapeKeyDownRef.current||pe(),S.isFocusedToastEscapeKeyDownRef.current=!1}),children:m.jsx(vt.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":d?"open":"closed","data-swipe-direction":S.swipeDirection,...j,ref:D,style:{userSelect:"none",touchAction:"none",...n.style},onKeyDown:Qe(n.onKeyDown,K=>{K.key==="Escape"&&(p==null||p(K.nativeEvent),K.nativeEvent.defaultPrevented||(S.isFocusedToastEscapeKeyDownRef.current=!0,pe()))}),onPointerDown:Qe(n.onPointerDown,K=>{K.button===0&&(I.current={x:K.clientX,y:K.clientY})}),onPointerMove:Qe(n.onPointerMove,K=>{if(!I.current)return;const ve=K.clientX-I.current.x,ue=K.clientY-I.current.y,ae=!!F.current,L=["left","right"].includes(S.swipeDirection),Y=["left","up"].includes(S.swipeDirection)?Math.min:Math.max,Q=L?Y(0,ve):0,R=L?0:Y(0,ue),z=K.pointerType==="touch"?10:2,fe={x:Q,y:R},he={originalEvent:K,delta:fe};ae?(F.current=fe,Ki(y0,v,he,{discrete:!1})):Yh(fe,S.swipeDirection,z)?(F.current=fe,Ki(g0,x,he,{discrete:!1}),K.target.setPointerCapture(K.pointerId)):(Math.abs(ve)>z||Math.abs(ue)>z)&&(I.current=null)}),onPointerUp:Qe(n.onPointerUp,K=>{const ve=F.current,ue=K.target;if(ue.hasPointerCapture(K.pointerId)&&ue.releasePointerCapture(K.pointerId),F.current=null,I.current=null,ve){const ae=K.currentTarget,L={originalEvent:K,delta:ve};Yh(ve,S.swipeDirection,S.swipeThreshold)?Ki(x0,N,L,{discrete:!0}):Ki(v0,E,L,{discrete:!0}),ae.addEventListener("click",Y=>Y.preventDefault(),{once:!0})}})})})}),S.viewport)})]}):null}),E0=n=>{const{__scopeToast:r,children:s,...l}=n,u=yl(vl,r),[d,f]=C.useState(!1),[p,g]=C.useState(!1);return P0(()=>f(!0)),C.useEffect(()=>{const y=window.setTimeout(()=>g(!0),1e3);return()=>window.clearTimeout(y)},[]),p?null:m.jsx(sm,{asChild:!0,children:m.jsx(gl,{...l,children:d&&m.jsxs(m.Fragment,{children:[u.label," ",s]})})})},k0="ToastTitle",fm=C.forwardRef((n,r)=>{const{__scopeToast:s,...l}=n;return m.jsx(vt.div,{...l,ref:r})});fm.displayName=k0;var b0="ToastDescription",hm=C.forwardRef((n,r)=>{const{__scopeToast:s,...l}=n;return m.jsx(vt.div,{...l,ref:r})});hm.displayName=b0;var pm="ToastAction",mm=C.forwardRef((n,r)=>{const{altText:s,...l}=n;return s.trim()?m.jsx(ym,{altText:s,asChild:!0,children:m.jsx(pc,{...l,ref:r})}):(console.error(`Invalid prop \`altText\` supplied to \`${pm}\`. Expected non-empty \`string\`.`),null)});mm.displayName=pm;var gm="ToastClose",pc=C.forwardRef((n,r)=>{const{__scopeToast:s,...l}=n,u=S0(gm,s);return m.jsx(ym,{asChild:!0,children:m.jsx(vt.button,{type:"button",...l,ref:r,onClick:Qe(n.onClick,u.onClose)})})});pc.displayName=gm;var ym=C.forwardRef((n,r)=>{const{__scopeToast:s,altText:l,...u}=n;return m.jsx(vt.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":l||void 0,...u,ref:r})});function vm(n){const r=[];return Array.from(n.childNodes).forEach(l=>{if(l.nodeType===l.TEXT_NODE&&l.textContent&&r.push(l.textContent),N0(l)){const u=l.ariaHidden||l.hidden||l.style.display==="none",d=l.dataset.radixToastAnnounceExclude==="";if(!u)if(d){const f=l.dataset.radixToastAnnounceAlt;f&&r.push(f)}else r.push(...vm(l))}}),r}function Ki(n,r,s,{discrete:l}){const u=s.originalEvent.currentTarget,d=new CustomEvent(n,{bubbles:!0,cancelable:!0,detail:s});r&&u.addEventListener(n,r,{once:!0}),l?tm(u,d):u.dispatchEvent(d)}var Yh=(n,r,s=0)=>{const l=Math.abs(n.x),u=Math.abs(n.y),d=l>u;return r==="left"||r==="right"?d&&l>s:!d&&u>s};function P0(n=()=>{}){const r=on(n);Rr(()=>{let s=0,l=0;return s=window.requestAnimationFrame(()=>l=window.requestAnimationFrame(r)),()=>{window.cancelAnimationFrame(s),window.cancelAnimationFrame(l)}},[r])}function N0(n){return n.nodeType===n.ELEMENT_NODE}function T0(n){const r=[],s=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:l=>{const u=l.tagName==="INPUT"&&l.type==="hidden";return l.disabled||l.hidden||u?NodeFilter.FILTER_SKIP:l.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)r.push(s.currentNode);return r}function Tu(n){const r=document.activeElement;return n.some(s=>s===r?!0:(s.focus(),document.activeElement!==r))}var R0=lm,xm=um,wm=dm,Sm=fm,Cm=hm,Em=mm,km=pc;function bm(n){var r,s,l="";if(typeof n=="string"||typeof n=="number")l+=n;else if(typeof n=="object")if(Array.isArray(n)){var u=n.length;for(r=0;r<u;r++)n[r]&&(s=bm(n[r]))&&(l&&(l+=" "),l+=s)}else for(s in n)n[s]&&(l&&(l+=" "),l+=s);return l}function Pm(){for(var n,r,s=0,l="",u=arguments.length;s<u;s++)(n=arguments[s])&&(r=bm(n))&&(l&&(l+=" "),l+=r);return l}const Kh=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,Gh=Pm,mc=(n,r)=>s=>{var l;if((r==null?void 0:r.variants)==null)return Gh(n,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:u,defaultVariants:d}=r,f=Object.keys(u).map(y=>{const x=s==null?void 0:s[y],v=d==null?void 0:d[y];if(x===null)return null;const E=Kh(x)||Kh(v);return u[y][E]}),p=s&&Object.entries(s).reduce((y,x)=>{let[v,E]=x;return E===void 0||(y[v]=E),y},{}),g=r==null||(l=r.compoundVariants)===null||l===void 0?void 0:l.reduce((y,x)=>{let{class:v,className:E,...N}=x;return Object.entries(N).every(j=>{let[S,k]=j;return Array.isArray(k)?k.includes({...d,...p}[S]):{...d,...p}[S]===k})?[...y,v,E]:y},[]);return Gh(n,f,g,s==null?void 0:s.class,s==null?void 0:s.className)};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O0=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Nm=(...n)=>n.filter((r,s,l)=>!!r&&l.indexOf(r)===s).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var j0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M0=C.forwardRef(({color:n="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:u="",children:d,iconNode:f,...p},g)=>C.createElement("svg",{ref:g,...j0,width:r,height:r,stroke:n,strokeWidth:l?Number(s)*24/Number(r):s,className:Nm("lucide",u),...p},[...f.map(([y,x])=>C.createElement(y,x)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qe=(n,r)=>{const s=C.forwardRef(({className:l,...u},d)=>C.createElement(M0,{ref:d,iconNode:r,className:Nm(`lucide-${O0(n)}`,l),...u}));return s.displayName=`${n}`,s};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _0=qe("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A0=qe("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D0=qe("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L0=qe("Clipboard",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I0=qe("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F0=qe("CodeXml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xh=qe("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gc=qe("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tm=qe("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rm=qe("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Om=qe("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z0=qe("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W0=qe("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U0=qe("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $0=qe("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H0=qe("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jm=qe("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mm=qe("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _m=qe("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),yc="-",V0=n=>{const r=Q0(n),{conflictingClassGroups:s,conflictingClassGroupModifiers:l}=n;return{getClassGroupId:f=>{const p=f.split(yc);return p[0]===""&&p.length!==1&&p.shift(),Am(p,r)||B0(f)},getConflictingClassGroupIds:(f,p)=>{const g=s[f]||[];return p&&l[f]?[...g,...l[f]]:g}}},Am=(n,r)=>{var f;if(n.length===0)return r.classGroupId;const s=n[0],l=r.nextPart.get(s),u=l?Am(n.slice(1),l):void 0;if(u)return u;if(r.validators.length===0)return;const d=n.join(yc);return(f=r.validators.find(({validator:p})=>p(d)))==null?void 0:f.classGroupId},Jh=/^\[(.+)\]$/,B0=n=>{if(Jh.test(n)){const r=Jh.exec(n)[1],s=r==null?void 0:r.substring(0,r.indexOf(":"));if(s)return"arbitrary.."+s}},Q0=n=>{const{theme:r,prefix:s}=n,l={nextPart:new Map,validators:[]};return Y0(Object.entries(n.classGroups),s).forEach(([d,f])=>{Ju(f,l,d,r)}),l},Ju=(n,r,s,l)=>{n.forEach(u=>{if(typeof u=="string"){const d=u===""?r:Zh(r,u);d.classGroupId=s;return}if(typeof u=="function"){if(q0(u)){Ju(u(l),r,s,l);return}r.validators.push({validator:u,classGroupId:s});return}Object.entries(u).forEach(([d,f])=>{Ju(f,Zh(r,d),s,l)})})},Zh=(n,r)=>{let s=n;return r.split(yc).forEach(l=>{s.nextPart.has(l)||s.nextPart.set(l,{nextPart:new Map,validators:[]}),s=s.nextPart.get(l)}),s},q0=n=>n.isThemeGetter,Y0=(n,r)=>r?n.map(([s,l])=>{const u=l.map(d=>typeof d=="string"?r+d:typeof d=="object"?Object.fromEntries(Object.entries(d).map(([f,p])=>[r+f,p])):d);return[s,u]}):n,K0=n=>{if(n<1)return{get:()=>{},set:()=>{}};let r=0,s=new Map,l=new Map;const u=(d,f)=>{s.set(d,f),r++,r>n&&(r=0,l=s,s=new Map)};return{get(d){let f=s.get(d);if(f!==void 0)return f;if((f=l.get(d))!==void 0)return u(d,f),f},set(d,f){s.has(d)?s.set(d,f):u(d,f)}}},Dm="!",G0=n=>{const{separator:r,experimentalParseClassName:s}=n,l=r.length===1,u=r[0],d=r.length,f=p=>{const g=[];let y=0,x=0,v;for(let k=0;k<p.length;k++){let A=p[k];if(y===0){if(A===u&&(l||p.slice(k,k+d)===r)){g.push(p.slice(x,k)),x=k+d;continue}if(A==="/"){v=k;continue}}A==="["?y++:A==="]"&&y--}const E=g.length===0?p:p.substring(x),N=E.startsWith(Dm),j=N?E.substring(1):E,S=v&&v>x?v-x:void 0;return{modifiers:g,hasImportantModifier:N,baseClassName:j,maybePostfixModifierPosition:S}};return s?p=>s({className:p,parseClassName:f}):f},X0=n=>{if(n.length<=1)return n;const r=[];let s=[];return n.forEach(l=>{l[0]==="["?(r.push(...s.sort(),l),s=[]):s.push(l)}),r.push(...s.sort()),r},J0=n=>({cache:K0(n.cacheSize),parseClassName:G0(n),...V0(n)}),Z0=/\s+/,ew=(n,r)=>{const{parseClassName:s,getClassGroupId:l,getConflictingClassGroupIds:u}=r,d=[],f=n.trim().split(Z0);let p="";for(let g=f.length-1;g>=0;g-=1){const y=f[g],{modifiers:x,hasImportantModifier:v,baseClassName:E,maybePostfixModifierPosition:N}=s(y);let j=!!N,S=l(j?E.substring(0,N):E);if(!S){if(!j){p=y+(p.length>0?" "+p:p);continue}if(S=l(E),!S){p=y+(p.length>0?" "+p:p);continue}j=!1}const k=X0(x).join(":"),A=v?k+Dm:k,D=A+S;if(d.includes(D))continue;d.push(D);const I=u(S,j);for(let F=0;F<I.length;++F){const H=I[F];d.push(A+H)}p=y+(p.length>0?" "+p:p)}return p};function tw(){let n=0,r,s,l="";for(;n<arguments.length;)(r=arguments[n++])&&(s=Lm(r))&&(l&&(l+=" "),l+=s);return l}const Lm=n=>{if(typeof n=="string")return n;let r,s="";for(let l=0;l<n.length;l++)n[l]&&(r=Lm(n[l]))&&(s&&(s+=" "),s+=r);return s};function nw(n,...r){let s,l,u,d=f;function f(g){const y=r.reduce((x,v)=>v(x),n());return s=J0(y),l=s.cache.get,u=s.cache.set,d=p,p(g)}function p(g){const y=l(g);if(y)return y;const x=ew(g,s);return u(g,x),x}return function(){return d(tw.apply(null,arguments))}}const Ae=n=>{const r=s=>s[n]||[];return r.isThemeGetter=!0,r},Im=/^\[(?:([a-z-]+):)?(.+)\]$/i,rw=/^\d+\/\d+$/,ow=new Set(["px","full","screen"]),sw=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,iw=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,lw=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,aw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,uw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,gn=n=>io(n)||ow.has(n)||rw.test(n),$n=n=>bo(n,"length",yw),io=n=>!!n&&!Number.isNaN(Number(n)),Ru=n=>bo(n,"number",io),gs=n=>!!n&&Number.isInteger(Number(n)),cw=n=>n.endsWith("%")&&io(n.slice(0,-1)),ge=n=>Im.test(n),Hn=n=>sw.test(n),dw=new Set(["length","size","percentage"]),fw=n=>bo(n,dw,Fm),hw=n=>bo(n,"position",Fm),pw=new Set(["image","url"]),mw=n=>bo(n,pw,xw),gw=n=>bo(n,"",vw),ys=()=>!0,bo=(n,r,s)=>{const l=Im.exec(n);return l?l[1]?typeof r=="string"?l[1]===r:r.has(l[1]):s(l[2]):!1},yw=n=>iw.test(n)&&!lw.test(n),Fm=()=>!1,vw=n=>aw.test(n),xw=n=>uw.test(n),ww=()=>{const n=Ae("colors"),r=Ae("spacing"),s=Ae("blur"),l=Ae("brightness"),u=Ae("borderColor"),d=Ae("borderRadius"),f=Ae("borderSpacing"),p=Ae("borderWidth"),g=Ae("contrast"),y=Ae("grayscale"),x=Ae("hueRotate"),v=Ae("invert"),E=Ae("gap"),N=Ae("gradientColorStops"),j=Ae("gradientColorStopPositions"),S=Ae("inset"),k=Ae("margin"),A=Ae("opacity"),D=Ae("padding"),I=Ae("saturate"),F=Ae("scale"),H=Ae("sepia"),q=Ae("skew"),B=Ae("space"),J=Ae("translate"),re=()=>["auto","contain","none"],de=()=>["auto","hidden","clip","visible","scroll"],pe=()=>["auto",ge,r],ee=()=>[ge,r],me=()=>["",gn,$n],K=()=>["auto",io,ge],ve=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],ue=()=>["solid","dashed","dotted","double","none"],ae=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],L=()=>["start","end","center","between","around","evenly","stretch"],Y=()=>["","0",ge],Q=()=>["auto","avoid","all","avoid-page","page","left","right","column"],R=()=>[io,ge];return{cacheSize:500,separator:":",theme:{colors:[ys],spacing:[gn,$n],blur:["none","",Hn,ge],brightness:R(),borderColor:[n],borderRadius:["none","","full",Hn,ge],borderSpacing:ee(),borderWidth:me(),contrast:R(),grayscale:Y(),hueRotate:R(),invert:Y(),gap:ee(),gradientColorStops:[n],gradientColorStopPositions:[cw,$n],inset:pe(),margin:pe(),opacity:R(),padding:ee(),saturate:R(),scale:R(),sepia:Y(),skew:R(),space:ee(),translate:ee()},classGroups:{aspect:[{aspect:["auto","square","video",ge]}],container:["container"],columns:[{columns:[Hn]}],"break-after":[{"break-after":Q()}],"break-before":[{"break-before":Q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...ve(),ge]}],overflow:[{overflow:de()}],"overflow-x":[{"overflow-x":de()}],"overflow-y":[{"overflow-y":de()}],overscroll:[{overscroll:re()}],"overscroll-x":[{"overscroll-x":re()}],"overscroll-y":[{"overscroll-y":re()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[S]}],"inset-x":[{"inset-x":[S]}],"inset-y":[{"inset-y":[S]}],start:[{start:[S]}],end:[{end:[S]}],top:[{top:[S]}],right:[{right:[S]}],bottom:[{bottom:[S]}],left:[{left:[S]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",gs,ge]}],basis:[{basis:pe()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",ge]}],grow:[{grow:Y()}],shrink:[{shrink:Y()}],order:[{order:["first","last","none",gs,ge]}],"grid-cols":[{"grid-cols":[ys]}],"col-start-end":[{col:["auto",{span:["full",gs,ge]},ge]}],"col-start":[{"col-start":K()}],"col-end":[{"col-end":K()}],"grid-rows":[{"grid-rows":[ys]}],"row-start-end":[{row:["auto",{span:[gs,ge]},ge]}],"row-start":[{"row-start":K()}],"row-end":[{"row-end":K()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",ge]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",ge]}],gap:[{gap:[E]}],"gap-x":[{"gap-x":[E]}],"gap-y":[{"gap-y":[E]}],"justify-content":[{justify:["normal",...L()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...L(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...L(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[D]}],px:[{px:[D]}],py:[{py:[D]}],ps:[{ps:[D]}],pe:[{pe:[D]}],pt:[{pt:[D]}],pr:[{pr:[D]}],pb:[{pb:[D]}],pl:[{pl:[D]}],m:[{m:[k]}],mx:[{mx:[k]}],my:[{my:[k]}],ms:[{ms:[k]}],me:[{me:[k]}],mt:[{mt:[k]}],mr:[{mr:[k]}],mb:[{mb:[k]}],ml:[{ml:[k]}],"space-x":[{"space-x":[B]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[B]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",ge,r]}],"min-w":[{"min-w":[ge,r,"min","max","fit"]}],"max-w":[{"max-w":[ge,r,"none","full","min","max","fit","prose",{screen:[Hn]},Hn]}],h:[{h:[ge,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[ge,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[ge,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[ge,r,"auto","min","max","fit"]}],"font-size":[{text:["base",Hn,$n]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ru]}],"font-family":[{font:[ys]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",ge]}],"line-clamp":[{"line-clamp":["none",io,Ru]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",gn,ge]}],"list-image":[{"list-image":["none",ge]}],"list-style-type":[{list:["none","disc","decimal",ge]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[n]}],"placeholder-opacity":[{"placeholder-opacity":[A]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[n]}],"text-opacity":[{"text-opacity":[A]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ue(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",gn,$n]}],"underline-offset":[{"underline-offset":["auto",gn,ge]}],"text-decoration-color":[{decoration:[n]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:ee()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ge]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ge]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[A]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...ve(),hw]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",fw]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},mw]}],"bg-color":[{bg:[n]}],"gradient-from-pos":[{from:[j]}],"gradient-via-pos":[{via:[j]}],"gradient-to-pos":[{to:[j]}],"gradient-from":[{from:[N]}],"gradient-via":[{via:[N]}],"gradient-to":[{to:[N]}],rounded:[{rounded:[d]}],"rounded-s":[{"rounded-s":[d]}],"rounded-e":[{"rounded-e":[d]}],"rounded-t":[{"rounded-t":[d]}],"rounded-r":[{"rounded-r":[d]}],"rounded-b":[{"rounded-b":[d]}],"rounded-l":[{"rounded-l":[d]}],"rounded-ss":[{"rounded-ss":[d]}],"rounded-se":[{"rounded-se":[d]}],"rounded-ee":[{"rounded-ee":[d]}],"rounded-es":[{"rounded-es":[d]}],"rounded-tl":[{"rounded-tl":[d]}],"rounded-tr":[{"rounded-tr":[d]}],"rounded-br":[{"rounded-br":[d]}],"rounded-bl":[{"rounded-bl":[d]}],"border-w":[{border:[p]}],"border-w-x":[{"border-x":[p]}],"border-w-y":[{"border-y":[p]}],"border-w-s":[{"border-s":[p]}],"border-w-e":[{"border-e":[p]}],"border-w-t":[{"border-t":[p]}],"border-w-r":[{"border-r":[p]}],"border-w-b":[{"border-b":[p]}],"border-w-l":[{"border-l":[p]}],"border-opacity":[{"border-opacity":[A]}],"border-style":[{border:[...ue(),"hidden"]}],"divide-x":[{"divide-x":[p]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[p]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[A]}],"divide-style":[{divide:ue()}],"border-color":[{border:[u]}],"border-color-x":[{"border-x":[u]}],"border-color-y":[{"border-y":[u]}],"border-color-s":[{"border-s":[u]}],"border-color-e":[{"border-e":[u]}],"border-color-t":[{"border-t":[u]}],"border-color-r":[{"border-r":[u]}],"border-color-b":[{"border-b":[u]}],"border-color-l":[{"border-l":[u]}],"divide-color":[{divide:[u]}],"outline-style":[{outline:["",...ue()]}],"outline-offset":[{"outline-offset":[gn,ge]}],"outline-w":[{outline:[gn,$n]}],"outline-color":[{outline:[n]}],"ring-w":[{ring:me()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[n]}],"ring-opacity":[{"ring-opacity":[A]}],"ring-offset-w":[{"ring-offset":[gn,$n]}],"ring-offset-color":[{"ring-offset":[n]}],shadow:[{shadow:["","inner","none",Hn,gw]}],"shadow-color":[{shadow:[ys]}],opacity:[{opacity:[A]}],"mix-blend":[{"mix-blend":[...ae(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":ae()}],filter:[{filter:["","none"]}],blur:[{blur:[s]}],brightness:[{brightness:[l]}],contrast:[{contrast:[g]}],"drop-shadow":[{"drop-shadow":["","none",Hn,ge]}],grayscale:[{grayscale:[y]}],"hue-rotate":[{"hue-rotate":[x]}],invert:[{invert:[v]}],saturate:[{saturate:[I]}],sepia:[{sepia:[H]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[s]}],"backdrop-brightness":[{"backdrop-brightness":[l]}],"backdrop-contrast":[{"backdrop-contrast":[g]}],"backdrop-grayscale":[{"backdrop-grayscale":[y]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[x]}],"backdrop-invert":[{"backdrop-invert":[v]}],"backdrop-opacity":[{"backdrop-opacity":[A]}],"backdrop-saturate":[{"backdrop-saturate":[I]}],"backdrop-sepia":[{"backdrop-sepia":[H]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[f]}],"border-spacing-x":[{"border-spacing-x":[f]}],"border-spacing-y":[{"border-spacing-y":[f]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",ge]}],duration:[{duration:R()}],ease:[{ease:["linear","in","out","in-out",ge]}],delay:[{delay:R()}],animate:[{animate:["none","spin","ping","pulse","bounce",ge]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[F]}],"scale-x":[{"scale-x":[F]}],"scale-y":[{"scale-y":[F]}],rotate:[{rotate:[gs,ge]}],"translate-x":[{"translate-x":[J]}],"translate-y":[{"translate-y":[J]}],"skew-x":[{"skew-x":[q]}],"skew-y":[{"skew-y":[q]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",ge]}],accent:[{accent:["auto",n]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ge]}],"caret-color":[{caret:[n]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":ee()}],"scroll-mx":[{"scroll-mx":ee()}],"scroll-my":[{"scroll-my":ee()}],"scroll-ms":[{"scroll-ms":ee()}],"scroll-me":[{"scroll-me":ee()}],"scroll-mt":[{"scroll-mt":ee()}],"scroll-mr":[{"scroll-mr":ee()}],"scroll-mb":[{"scroll-mb":ee()}],"scroll-ml":[{"scroll-ml":ee()}],"scroll-p":[{"scroll-p":ee()}],"scroll-px":[{"scroll-px":ee()}],"scroll-py":[{"scroll-py":ee()}],"scroll-ps":[{"scroll-ps":ee()}],"scroll-pe":[{"scroll-pe":ee()}],"scroll-pt":[{"scroll-pt":ee()}],"scroll-pr":[{"scroll-pr":ee()}],"scroll-pb":[{"scroll-pb":ee()}],"scroll-pl":[{"scroll-pl":ee()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ge]}],fill:[{fill:[n,"none"]}],"stroke-w":[{stroke:[gn,$n,Ru]}],stroke:[{stroke:[n,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Sw=nw(ww);function Ze(...n){return Sw(Pm(n))}const Cw=R0,zm=C.forwardRef(({className:n,...r},s)=>m.jsx(xm,{ref:s,className:Ze("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",n),...r}));zm.displayName=xm.displayName;const Ew=mc("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Wm=C.forwardRef(({className:n,variant:r,...s},l)=>m.jsx(wm,{ref:l,className:Ze(Ew({variant:r}),n),...s}));Wm.displayName=wm.displayName;const kw=C.forwardRef(({className:n,...r},s)=>m.jsx(Em,{ref:s,className:Ze("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",n),...r}));kw.displayName=Em.displayName;const Um=C.forwardRef(({className:n,...r},s)=>m.jsx(km,{ref:s,className:Ze("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",n),"toast-close":"",...r,children:m.jsx(_m,{className:"h-4 w-4"})}));Um.displayName=km.displayName;const $m=C.forwardRef(({className:n,...r},s)=>m.jsx(Sm,{ref:s,className:Ze("text-sm font-semibold",n),...r}));$m.displayName=Sm.displayName;const Hm=C.forwardRef(({className:n,...r},s)=>m.jsx(Cm,{ref:s,className:Ze("text-sm opacity-90",n),...r}));Hm.displayName=Cm.displayName;function bw(){const{toasts:n}=Jp();return m.jsxs(Cw,{children:[n.map(function({id:r,title:s,description:l,action:u,...d}){return m.jsxs(Wm,{...d,children:[m.jsxs("div",{className:"grid gap-1",children:[s&&m.jsx($m,{children:s}),l&&m.jsx(Hm,{children:l})]}),u,m.jsx(Um,{})]},r)}),m.jsx(zm,{})]})}const Pw=["top","right","bottom","left"],nr=Math.min,kt=Math.max,ll=Math.round,Gi=Math.floor,rn=n=>({x:n,y:n}),Nw={left:"right",right:"left",bottom:"top",top:"bottom"},Tw={start:"end",end:"start"};function Zu(n,r,s){return kt(n,nr(r,s))}function wn(n,r){return typeof n=="function"?n(r):n}function Sn(n){return n.split("-")[0]}function Po(n){return n.split("-")[1]}function vc(n){return n==="x"?"y":"x"}function xc(n){return n==="y"?"height":"width"}function rr(n){return["top","bottom"].includes(Sn(n))?"y":"x"}function wc(n){return vc(rr(n))}function Rw(n,r,s){s===void 0&&(s=!1);const l=Po(n),u=wc(n),d=xc(u);let f=u==="x"?l===(s?"end":"start")?"right":"left":l==="start"?"bottom":"top";return r.reference[d]>r.floating[d]&&(f=al(f)),[f,al(f)]}function Ow(n){const r=al(n);return[ec(n),r,ec(r)]}function ec(n){return n.replace(/start|end/g,r=>Tw[r])}function jw(n,r,s){const l=["left","right"],u=["right","left"],d=["top","bottom"],f=["bottom","top"];switch(n){case"top":case"bottom":return s?r?u:l:r?l:u;case"left":case"right":return r?d:f;default:return[]}}function Mw(n,r,s,l){const u=Po(n);let d=jw(Sn(n),s==="start",l);return u&&(d=d.map(f=>f+"-"+u),r&&(d=d.concat(d.map(ec)))),d}function al(n){return n.replace(/left|right|bottom|top/g,r=>Nw[r])}function _w(n){return{top:0,right:0,bottom:0,left:0,...n}}function Vm(n){return typeof n!="number"?_w(n):{top:n,right:n,bottom:n,left:n}}function ul(n){const{x:r,y:s,width:l,height:u}=n;return{width:l,height:u,top:s,left:r,right:r+l,bottom:s+u,x:r,y:s}}function ep(n,r,s){let{reference:l,floating:u}=n;const d=rr(r),f=wc(r),p=xc(f),g=Sn(r),y=d==="y",x=l.x+l.width/2-u.width/2,v=l.y+l.height/2-u.height/2,E=l[p]/2-u[p]/2;let N;switch(g){case"top":N={x,y:l.y-u.height};break;case"bottom":N={x,y:l.y+l.height};break;case"right":N={x:l.x+l.width,y:v};break;case"left":N={x:l.x-u.width,y:v};break;default:N={x:l.x,y:l.y}}switch(Po(r)){case"start":N[f]-=E*(s&&y?-1:1);break;case"end":N[f]+=E*(s&&y?-1:1);break}return N}const Aw=async(n,r,s)=>{const{placement:l="bottom",strategy:u="absolute",middleware:d=[],platform:f}=s,p=d.filter(Boolean),g=await(f.isRTL==null?void 0:f.isRTL(r));let y=await f.getElementRects({reference:n,floating:r,strategy:u}),{x,y:v}=ep(y,l,g),E=l,N={},j=0;for(let S=0;S<p.length;S++){const{name:k,fn:A}=p[S],{x:D,y:I,data:F,reset:H}=await A({x,y:v,initialPlacement:l,placement:E,strategy:u,middlewareData:N,rects:y,platform:f,elements:{reference:n,floating:r}});x=D??x,v=I??v,N={...N,[k]:{...N[k],...F}},H&&j<=50&&(j++,typeof H=="object"&&(H.placement&&(E=H.placement),H.rects&&(y=H.rects===!0?await f.getElementRects({reference:n,floating:r,strategy:u}):H.rects),{x,y:v}=ep(y,E,g)),S=-1)}return{x,y:v,placement:E,strategy:u,middlewareData:N}};async function Ps(n,r){var s;r===void 0&&(r={});const{x:l,y:u,platform:d,rects:f,elements:p,strategy:g}=n,{boundary:y="clippingAncestors",rootBoundary:x="viewport",elementContext:v="floating",altBoundary:E=!1,padding:N=0}=wn(r,n),j=Vm(N),k=p[E?v==="floating"?"reference":"floating":v],A=ul(await d.getClippingRect({element:(s=await(d.isElement==null?void 0:d.isElement(k)))==null||s?k:k.contextElement||await(d.getDocumentElement==null?void 0:d.getDocumentElement(p.floating)),boundary:y,rootBoundary:x,strategy:g})),D=v==="floating"?{x:l,y:u,width:f.floating.width,height:f.floating.height}:f.reference,I=await(d.getOffsetParent==null?void 0:d.getOffsetParent(p.floating)),F=await(d.isElement==null?void 0:d.isElement(I))?await(d.getScale==null?void 0:d.getScale(I))||{x:1,y:1}:{x:1,y:1},H=ul(d.convertOffsetParentRelativeRectToViewportRelativeRect?await d.convertOffsetParentRelativeRectToViewportRelativeRect({elements:p,rect:D,offsetParent:I,strategy:g}):D);return{top:(A.top-H.top+j.top)/F.y,bottom:(H.bottom-A.bottom+j.bottom)/F.y,left:(A.left-H.left+j.left)/F.x,right:(H.right-A.right+j.right)/F.x}}const Dw=n=>({name:"arrow",options:n,async fn(r){const{x:s,y:l,placement:u,rects:d,platform:f,elements:p,middlewareData:g}=r,{element:y,padding:x=0}=wn(n,r)||{};if(y==null)return{};const v=Vm(x),E={x:s,y:l},N=wc(u),j=xc(N),S=await f.getDimensions(y),k=N==="y",A=k?"top":"left",D=k?"bottom":"right",I=k?"clientHeight":"clientWidth",F=d.reference[j]+d.reference[N]-E[N]-d.floating[j],H=E[N]-d.reference[N],q=await(f.getOffsetParent==null?void 0:f.getOffsetParent(y));let B=q?q[I]:0;(!B||!await(f.isElement==null?void 0:f.isElement(q)))&&(B=p.floating[I]||d.floating[j]);const J=F/2-H/2,re=B/2-S[j]/2-1,de=nr(v[A],re),pe=nr(v[D],re),ee=de,me=B-S[j]-pe,K=B/2-S[j]/2+J,ve=Zu(ee,K,me),ue=!g.arrow&&Po(u)!=null&&K!==ve&&d.reference[j]/2-(K<ee?de:pe)-S[j]/2<0,ae=ue?K<ee?K-ee:K-me:0;return{[N]:E[N]+ae,data:{[N]:ve,centerOffset:K-ve-ae,...ue&&{alignmentOffset:ae}},reset:ue}}}),Lw=function(n){return n===void 0&&(n={}),{name:"flip",options:n,async fn(r){var s,l;const{placement:u,middlewareData:d,rects:f,initialPlacement:p,platform:g,elements:y}=r,{mainAxis:x=!0,crossAxis:v=!0,fallbackPlacements:E,fallbackStrategy:N="bestFit",fallbackAxisSideDirection:j="none",flipAlignment:S=!0,...k}=wn(n,r);if((s=d.arrow)!=null&&s.alignmentOffset)return{};const A=Sn(u),D=rr(p),I=Sn(p)===p,F=await(g.isRTL==null?void 0:g.isRTL(y.floating)),H=E||(I||!S?[al(p)]:Ow(p)),q=j!=="none";!E&&q&&H.push(...Mw(p,S,j,F));const B=[p,...H],J=await Ps(r,k),re=[];let de=((l=d.flip)==null?void 0:l.overflows)||[];if(x&&re.push(J[A]),v){const K=Rw(u,f,F);re.push(J[K[0]],J[K[1]])}if(de=[...de,{placement:u,overflows:re}],!re.every(K=>K<=0)){var pe,ee;const K=(((pe=d.flip)==null?void 0:pe.index)||0)+1,ve=B[K];if(ve)return{data:{index:K,overflows:de},reset:{placement:ve}};let ue=(ee=de.filter(ae=>ae.overflows[0]<=0).sort((ae,L)=>ae.overflows[1]-L.overflows[1])[0])==null?void 0:ee.placement;if(!ue)switch(N){case"bestFit":{var me;const ae=(me=de.filter(L=>{if(q){const Y=rr(L.placement);return Y===D||Y==="y"}return!0}).map(L=>[L.placement,L.overflows.filter(Y=>Y>0).reduce((Y,Q)=>Y+Q,0)]).sort((L,Y)=>L[1]-Y[1])[0])==null?void 0:me[0];ae&&(ue=ae);break}case"initialPlacement":ue=p;break}if(u!==ue)return{reset:{placement:ue}}}return{}}}};function tp(n,r){return{top:n.top-r.height,right:n.right-r.width,bottom:n.bottom-r.height,left:n.left-r.width}}function np(n){return Pw.some(r=>n[r]>=0)}const Iw=function(n){return n===void 0&&(n={}),{name:"hide",options:n,async fn(r){const{rects:s}=r,{strategy:l="referenceHidden",...u}=wn(n,r);switch(l){case"referenceHidden":{const d=await Ps(r,{...u,elementContext:"reference"}),f=tp(d,s.reference);return{data:{referenceHiddenOffsets:f,referenceHidden:np(f)}}}case"escaped":{const d=await Ps(r,{...u,altBoundary:!0}),f=tp(d,s.floating);return{data:{escapedOffsets:f,escaped:np(f)}}}default:return{}}}}};async function Fw(n,r){const{placement:s,platform:l,elements:u}=n,d=await(l.isRTL==null?void 0:l.isRTL(u.floating)),f=Sn(s),p=Po(s),g=rr(s)==="y",y=["left","top"].includes(f)?-1:1,x=d&&g?-1:1,v=wn(r,n);let{mainAxis:E,crossAxis:N,alignmentAxis:j}=typeof v=="number"?{mainAxis:v,crossAxis:0,alignmentAxis:null}:{mainAxis:v.mainAxis||0,crossAxis:v.crossAxis||0,alignmentAxis:v.alignmentAxis};return p&&typeof j=="number"&&(N=p==="end"?j*-1:j),g?{x:N*x,y:E*y}:{x:E*y,y:N*x}}const zw=function(n){return n===void 0&&(n=0),{name:"offset",options:n,async fn(r){var s,l;const{x:u,y:d,placement:f,middlewareData:p}=r,g=await Fw(r,n);return f===((s=p.offset)==null?void 0:s.placement)&&(l=p.arrow)!=null&&l.alignmentOffset?{}:{x:u+g.x,y:d+g.y,data:{...g,placement:f}}}}},Ww=function(n){return n===void 0&&(n={}),{name:"shift",options:n,async fn(r){const{x:s,y:l,placement:u}=r,{mainAxis:d=!0,crossAxis:f=!1,limiter:p={fn:k=>{let{x:A,y:D}=k;return{x:A,y:D}}},...g}=wn(n,r),y={x:s,y:l},x=await Ps(r,g),v=rr(Sn(u)),E=vc(v);let N=y[E],j=y[v];if(d){const k=E==="y"?"top":"left",A=E==="y"?"bottom":"right",D=N+x[k],I=N-x[A];N=Zu(D,N,I)}if(f){const k=v==="y"?"top":"left",A=v==="y"?"bottom":"right",D=j+x[k],I=j-x[A];j=Zu(D,j,I)}const S=p.fn({...r,[E]:N,[v]:j});return{...S,data:{x:S.x-s,y:S.y-l,enabled:{[E]:d,[v]:f}}}}}},Uw=function(n){return n===void 0&&(n={}),{options:n,fn(r){const{x:s,y:l,placement:u,rects:d,middlewareData:f}=r,{offset:p=0,mainAxis:g=!0,crossAxis:y=!0}=wn(n,r),x={x:s,y:l},v=rr(u),E=vc(v);let N=x[E],j=x[v];const S=wn(p,r),k=typeof S=="number"?{mainAxis:S,crossAxis:0}:{mainAxis:0,crossAxis:0,...S};if(g){const I=E==="y"?"height":"width",F=d.reference[E]-d.floating[I]+k.mainAxis,H=d.reference[E]+d.reference[I]-k.mainAxis;N<F?N=F:N>H&&(N=H)}if(y){var A,D;const I=E==="y"?"width":"height",F=["top","left"].includes(Sn(u)),H=d.reference[v]-d.floating[I]+(F&&((A=f.offset)==null?void 0:A[v])||0)+(F?0:k.crossAxis),q=d.reference[v]+d.reference[I]+(F?0:((D=f.offset)==null?void 0:D[v])||0)-(F?k.crossAxis:0);j<H?j=H:j>q&&(j=q)}return{[E]:N,[v]:j}}}},$w=function(n){return n===void 0&&(n={}),{name:"size",options:n,async fn(r){var s,l;const{placement:u,rects:d,platform:f,elements:p}=r,{apply:g=()=>{},...y}=wn(n,r),x=await Ps(r,y),v=Sn(u),E=Po(u),N=rr(u)==="y",{width:j,height:S}=d.floating;let k,A;v==="top"||v==="bottom"?(k=v,A=E===(await(f.isRTL==null?void 0:f.isRTL(p.floating))?"start":"end")?"left":"right"):(A=v,k=E==="end"?"top":"bottom");const D=S-x.top-x.bottom,I=j-x.left-x.right,F=nr(S-x[k],D),H=nr(j-x[A],I),q=!r.middlewareData.shift;let B=F,J=H;if((s=r.middlewareData.shift)!=null&&s.enabled.x&&(J=I),(l=r.middlewareData.shift)!=null&&l.enabled.y&&(B=D),q&&!E){const de=kt(x.left,0),pe=kt(x.right,0),ee=kt(x.top,0),me=kt(x.bottom,0);N?J=j-2*(de!==0||pe!==0?de+pe:kt(x.left,x.right)):B=S-2*(ee!==0||me!==0?ee+me:kt(x.top,x.bottom))}await g({...r,availableWidth:J,availableHeight:B});const re=await f.getDimensions(p.floating);return j!==re.width||S!==re.height?{reset:{rects:!0}}:{}}}};function xl(){return typeof window<"u"}function No(n){return Bm(n)?(n.nodeName||"").toLowerCase():"#document"}function bt(n){var r;return(n==null||(r=n.ownerDocument)==null?void 0:r.defaultView)||window}function ln(n){var r;return(r=(Bm(n)?n.ownerDocument:n.document)||window.document)==null?void 0:r.documentElement}function Bm(n){return xl()?n instanceof Node||n instanceof bt(n).Node:!1}function Vt(n){return xl()?n instanceof Element||n instanceof bt(n).Element:!1}function sn(n){return xl()?n instanceof HTMLElement||n instanceof bt(n).HTMLElement:!1}function rp(n){return!xl()||typeof ShadowRoot>"u"?!1:n instanceof ShadowRoot||n instanceof bt(n).ShadowRoot}function _s(n){const{overflow:r,overflowX:s,overflowY:l,display:u}=Bt(n);return/auto|scroll|overlay|hidden|clip/.test(r+l+s)&&!["inline","contents"].includes(u)}function Hw(n){return["table","td","th"].includes(No(n))}function wl(n){return[":popover-open",":modal"].some(r=>{try{return n.matches(r)}catch{return!1}})}function Sc(n){const r=Cc(),s=Vt(n)?Bt(n):n;return["transform","translate","scale","rotate","perspective"].some(l=>s[l]?s[l]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!r&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!r&&(s.filter?s.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(l=>(s.willChange||"").includes(l))||["paint","layout","strict","content"].some(l=>(s.contain||"").includes(l))}function Vw(n){let r=or(n);for(;sn(r)&&!Co(r);){if(Sc(r))return r;if(wl(r))return null;r=or(r)}return null}function Cc(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Co(n){return["html","body","#document"].includes(No(n))}function Bt(n){return bt(n).getComputedStyle(n)}function Sl(n){return Vt(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function or(n){if(No(n)==="html")return n;const r=n.assignedSlot||n.parentNode||rp(n)&&n.host||ln(n);return rp(r)?r.host:r}function Qm(n){const r=or(n);return Co(r)?n.ownerDocument?n.ownerDocument.body:n.body:sn(r)&&_s(r)?r:Qm(r)}function Ns(n,r,s){var l;r===void 0&&(r=[]),s===void 0&&(s=!0);const u=Qm(n),d=u===((l=n.ownerDocument)==null?void 0:l.body),f=bt(u);if(d){const p=tc(f);return r.concat(f,f.visualViewport||[],_s(u)?u:[],p&&s?Ns(p):[])}return r.concat(u,Ns(u,[],s))}function tc(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function qm(n){const r=Bt(n);let s=parseFloat(r.width)||0,l=parseFloat(r.height)||0;const u=sn(n),d=u?n.offsetWidth:s,f=u?n.offsetHeight:l,p=ll(s)!==d||ll(l)!==f;return p&&(s=d,l=f),{width:s,height:l,$:p}}function Ec(n){return Vt(n)?n:n.contextElement}function lo(n){const r=Ec(n);if(!sn(r))return rn(1);const s=r.getBoundingClientRect(),{width:l,height:u,$:d}=qm(r);let f=(d?ll(s.width):s.width)/l,p=(d?ll(s.height):s.height)/u;return(!f||!Number.isFinite(f))&&(f=1),(!p||!Number.isFinite(p))&&(p=1),{x:f,y:p}}const Bw=rn(0);function Ym(n){const r=bt(n);return!Cc()||!r.visualViewport?Bw:{x:r.visualViewport.offsetLeft,y:r.visualViewport.offsetTop}}function Qw(n,r,s){return r===void 0&&(r=!1),!s||r&&s!==bt(n)?!1:r}function Or(n,r,s,l){r===void 0&&(r=!1),s===void 0&&(s=!1);const u=n.getBoundingClientRect(),d=Ec(n);let f=rn(1);r&&(l?Vt(l)&&(f=lo(l)):f=lo(n));const p=Qw(d,s,l)?Ym(d):rn(0);let g=(u.left+p.x)/f.x,y=(u.top+p.y)/f.y,x=u.width/f.x,v=u.height/f.y;if(d){const E=bt(d),N=l&&Vt(l)?bt(l):l;let j=E,S=tc(j);for(;S&&l&&N!==j;){const k=lo(S),A=S.getBoundingClientRect(),D=Bt(S),I=A.left+(S.clientLeft+parseFloat(D.paddingLeft))*k.x,F=A.top+(S.clientTop+parseFloat(D.paddingTop))*k.y;g*=k.x,y*=k.y,x*=k.x,v*=k.y,g+=I,y+=F,j=bt(S),S=tc(j)}}return ul({width:x,height:v,x:g,y})}function kc(n,r){const s=Sl(n).scrollLeft;return r?r.left+s:Or(ln(n)).left+s}function Km(n,r,s){s===void 0&&(s=!1);const l=n.getBoundingClientRect(),u=l.left+r.scrollLeft-(s?0:kc(n,l)),d=l.top+r.scrollTop;return{x:u,y:d}}function qw(n){let{elements:r,rect:s,offsetParent:l,strategy:u}=n;const d=u==="fixed",f=ln(l),p=r?wl(r.floating):!1;if(l===f||p&&d)return s;let g={scrollLeft:0,scrollTop:0},y=rn(1);const x=rn(0),v=sn(l);if((v||!v&&!d)&&((No(l)!=="body"||_s(f))&&(g=Sl(l)),sn(l))){const N=Or(l);y=lo(l),x.x=N.x+l.clientLeft,x.y=N.y+l.clientTop}const E=f&&!v&&!d?Km(f,g,!0):rn(0);return{width:s.width*y.x,height:s.height*y.y,x:s.x*y.x-g.scrollLeft*y.x+x.x+E.x,y:s.y*y.y-g.scrollTop*y.y+x.y+E.y}}function Yw(n){return Array.from(n.getClientRects())}function Kw(n){const r=ln(n),s=Sl(n),l=n.ownerDocument.body,u=kt(r.scrollWidth,r.clientWidth,l.scrollWidth,l.clientWidth),d=kt(r.scrollHeight,r.clientHeight,l.scrollHeight,l.clientHeight);let f=-s.scrollLeft+kc(n);const p=-s.scrollTop;return Bt(l).direction==="rtl"&&(f+=kt(r.clientWidth,l.clientWidth)-u),{width:u,height:d,x:f,y:p}}function Gw(n,r){const s=bt(n),l=ln(n),u=s.visualViewport;let d=l.clientWidth,f=l.clientHeight,p=0,g=0;if(u){d=u.width,f=u.height;const y=Cc();(!y||y&&r==="fixed")&&(p=u.offsetLeft,g=u.offsetTop)}return{width:d,height:f,x:p,y:g}}function Xw(n,r){const s=Or(n,!0,r==="fixed"),l=s.top+n.clientTop,u=s.left+n.clientLeft,d=sn(n)?lo(n):rn(1),f=n.clientWidth*d.x,p=n.clientHeight*d.y,g=u*d.x,y=l*d.y;return{width:f,height:p,x:g,y}}function op(n,r,s){let l;if(r==="viewport")l=Gw(n,s);else if(r==="document")l=Kw(ln(n));else if(Vt(r))l=Xw(r,s);else{const u=Ym(n);l={x:r.x-u.x,y:r.y-u.y,width:r.width,height:r.height}}return ul(l)}function Gm(n,r){const s=or(n);return s===r||!Vt(s)||Co(s)?!1:Bt(s).position==="fixed"||Gm(s,r)}function Jw(n,r){const s=r.get(n);if(s)return s;let l=Ns(n,[],!1).filter(p=>Vt(p)&&No(p)!=="body"),u=null;const d=Bt(n).position==="fixed";let f=d?or(n):n;for(;Vt(f)&&!Co(f);){const p=Bt(f),g=Sc(f);!g&&p.position==="fixed"&&(u=null),(d?!g&&!u:!g&&p.position==="static"&&!!u&&["absolute","fixed"].includes(u.position)||_s(f)&&!g&&Gm(n,f))?l=l.filter(x=>x!==f):u=p,f=or(f)}return r.set(n,l),l}function Zw(n){let{element:r,boundary:s,rootBoundary:l,strategy:u}=n;const f=[...s==="clippingAncestors"?wl(r)?[]:Jw(r,this._c):[].concat(s),l],p=f[0],g=f.reduce((y,x)=>{const v=op(r,x,u);return y.top=kt(v.top,y.top),y.right=nr(v.right,y.right),y.bottom=nr(v.bottom,y.bottom),y.left=kt(v.left,y.left),y},op(r,p,u));return{width:g.right-g.left,height:g.bottom-g.top,x:g.left,y:g.top}}function e1(n){const{width:r,height:s}=qm(n);return{width:r,height:s}}function t1(n,r,s){const l=sn(r),u=ln(r),d=s==="fixed",f=Or(n,!0,d,r);let p={scrollLeft:0,scrollTop:0};const g=rn(0);if(l||!l&&!d)if((No(r)!=="body"||_s(u))&&(p=Sl(r)),l){const E=Or(r,!0,d,r);g.x=E.x+r.clientLeft,g.y=E.y+r.clientTop}else u&&(g.x=kc(u));const y=u&&!l&&!d?Km(u,p):rn(0),x=f.left+p.scrollLeft-g.x-y.x,v=f.top+p.scrollTop-g.y-y.y;return{x,y:v,width:f.width,height:f.height}}function Ou(n){return Bt(n).position==="static"}function sp(n,r){if(!sn(n)||Bt(n).position==="fixed")return null;if(r)return r(n);let s=n.offsetParent;return ln(n)===s&&(s=s.ownerDocument.body),s}function Xm(n,r){const s=bt(n);if(wl(n))return s;if(!sn(n)){let u=or(n);for(;u&&!Co(u);){if(Vt(u)&&!Ou(u))return u;u=or(u)}return s}let l=sp(n,r);for(;l&&Hw(l)&&Ou(l);)l=sp(l,r);return l&&Co(l)&&Ou(l)&&!Sc(l)?s:l||Vw(n)||s}const n1=async function(n){const r=this.getOffsetParent||Xm,s=this.getDimensions,l=await s(n.floating);return{reference:t1(n.reference,await r(n.floating),n.strategy),floating:{x:0,y:0,width:l.width,height:l.height}}};function r1(n){return Bt(n).direction==="rtl"}const o1={convertOffsetParentRelativeRectToViewportRelativeRect:qw,getDocumentElement:ln,getClippingRect:Zw,getOffsetParent:Xm,getElementRects:n1,getClientRects:Yw,getDimensions:e1,getScale:lo,isElement:Vt,isRTL:r1};function Jm(n,r){return n.x===r.x&&n.y===r.y&&n.width===r.width&&n.height===r.height}function s1(n,r){let s=null,l;const u=ln(n);function d(){var p;clearTimeout(l),(p=s)==null||p.disconnect(),s=null}function f(p,g){p===void 0&&(p=!1),g===void 0&&(g=1),d();const y=n.getBoundingClientRect(),{left:x,top:v,width:E,height:N}=y;if(p||r(),!E||!N)return;const j=Gi(v),S=Gi(u.clientWidth-(x+E)),k=Gi(u.clientHeight-(v+N)),A=Gi(x),I={rootMargin:-j+"px "+-S+"px "+-k+"px "+-A+"px",threshold:kt(0,nr(1,g))||1};let F=!0;function H(q){const B=q[0].intersectionRatio;if(B!==g){if(!F)return f();B?f(!1,B):l=setTimeout(()=>{f(!1,1e-7)},1e3)}B===1&&!Jm(y,n.getBoundingClientRect())&&f(),F=!1}try{s=new IntersectionObserver(H,{...I,root:u.ownerDocument})}catch{s=new IntersectionObserver(H,I)}s.observe(n)}return f(!0),d}function i1(n,r,s,l){l===void 0&&(l={});const{ancestorScroll:u=!0,ancestorResize:d=!0,elementResize:f=typeof ResizeObserver=="function",layoutShift:p=typeof IntersectionObserver=="function",animationFrame:g=!1}=l,y=Ec(n),x=u||d?[...y?Ns(y):[],...Ns(r)]:[];x.forEach(A=>{u&&A.addEventListener("scroll",s,{passive:!0}),d&&A.addEventListener("resize",s)});const v=y&&p?s1(y,s):null;let E=-1,N=null;f&&(N=new ResizeObserver(A=>{let[D]=A;D&&D.target===y&&N&&(N.unobserve(r),cancelAnimationFrame(E),E=requestAnimationFrame(()=>{var I;(I=N)==null||I.observe(r)})),s()}),y&&!g&&N.observe(y),N.observe(r));let j,S=g?Or(n):null;g&&k();function k(){const A=Or(n);S&&!Jm(S,A)&&s(),S=A,j=requestAnimationFrame(k)}return s(),()=>{var A;x.forEach(D=>{u&&D.removeEventListener("scroll",s),d&&D.removeEventListener("resize",s)}),v==null||v(),(A=N)==null||A.disconnect(),N=null,g&&cancelAnimationFrame(j)}}const l1=zw,a1=Ww,u1=Lw,c1=$w,d1=Iw,ip=Dw,f1=Uw,h1=(n,r,s)=>{const l=new Map,u={platform:o1,...s},d={...u.platform,_c:l};return Aw(n,r,{...u,platform:d})};var tl=typeof document<"u"?C.useLayoutEffect:C.useEffect;function cl(n,r){if(n===r)return!0;if(typeof n!=typeof r)return!1;if(typeof n=="function"&&n.toString()===r.toString())return!0;let s,l,u;if(n&&r&&typeof n=="object"){if(Array.isArray(n)){if(s=n.length,s!==r.length)return!1;for(l=s;l--!==0;)if(!cl(n[l],r[l]))return!1;return!0}if(u=Object.keys(n),s=u.length,s!==Object.keys(r).length)return!1;for(l=s;l--!==0;)if(!{}.hasOwnProperty.call(r,u[l]))return!1;for(l=s;l--!==0;){const d=u[l];if(!(d==="_owner"&&n.$$typeof)&&!cl(n[d],r[d]))return!1}return!0}return n!==n&&r!==r}function Zm(n){return typeof window>"u"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function lp(n,r){const s=Zm(n);return Math.round(r*s)/s}function ju(n){const r=C.useRef(n);return tl(()=>{r.current=n}),r}function p1(n){n===void 0&&(n={});const{placement:r="bottom",strategy:s="absolute",middleware:l=[],platform:u,elements:{reference:d,floating:f}={},transform:p=!0,whileElementsMounted:g,open:y}=n,[x,v]=C.useState({x:0,y:0,strategy:s,placement:r,middlewareData:{},isPositioned:!1}),[E,N]=C.useState(l);cl(E,l)||N(l);const[j,S]=C.useState(null),[k,A]=C.useState(null),D=C.useCallback(L=>{L!==q.current&&(q.current=L,S(L))},[]),I=C.useCallback(L=>{L!==B.current&&(B.current=L,A(L))},[]),F=d||j,H=f||k,q=C.useRef(null),B=C.useRef(null),J=C.useRef(x),re=g!=null,de=ju(g),pe=ju(u),ee=ju(y),me=C.useCallback(()=>{if(!q.current||!B.current)return;const L={placement:r,strategy:s,middleware:E};pe.current&&(L.platform=pe.current),h1(q.current,B.current,L).then(Y=>{const Q={...Y,isPositioned:ee.current!==!1};K.current&&!cl(J.current,Q)&&(J.current=Q,pl.flushSync(()=>{v(Q)}))})},[E,r,s,pe,ee]);tl(()=>{y===!1&&J.current.isPositioned&&(J.current.isPositioned=!1,v(L=>({...L,isPositioned:!1})))},[y]);const K=C.useRef(!1);tl(()=>(K.current=!0,()=>{K.current=!1}),[]),tl(()=>{if(F&&(q.current=F),H&&(B.current=H),F&&H){if(de.current)return de.current(F,H,me);me()}},[F,H,me,de,re]);const ve=C.useMemo(()=>({reference:q,floating:B,setReference:D,setFloating:I}),[D,I]),ue=C.useMemo(()=>({reference:F,floating:H}),[F,H]),ae=C.useMemo(()=>{const L={position:s,left:0,top:0};if(!ue.floating)return L;const Y=lp(ue.floating,x.x),Q=lp(ue.floating,x.y);return p?{...L,transform:"translate("+Y+"px, "+Q+"px)",...Zm(ue.floating)>=1.5&&{willChange:"transform"}}:{position:s,left:Y,top:Q}},[s,p,ue.floating,x.x,x.y]);return C.useMemo(()=>({...x,update:me,refs:ve,elements:ue,floatingStyles:ae}),[x,me,ve,ue,ae])}const m1=n=>{function r(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:n,fn(s){const{element:l,padding:u}=typeof n=="function"?n(s):n;return l&&r(l)?l.current!=null?ip({element:l.current,padding:u}).fn(s):{}:l?ip({element:l,padding:u}).fn(s):{}}}},g1=(n,r)=>({...l1(n),options:[n,r]}),y1=(n,r)=>({...a1(n),options:[n,r]}),v1=(n,r)=>({...f1(n),options:[n,r]}),x1=(n,r)=>({...u1(n),options:[n,r]}),w1=(n,r)=>({...c1(n),options:[n,r]}),S1=(n,r)=>({...d1(n),options:[n,r]}),C1=(n,r)=>({...m1(n),options:[n,r]});var E1="Arrow",eg=C.forwardRef((n,r)=>{const{children:s,width:l=10,height:u=5,...d}=n;return m.jsx(vt.svg,{...d,ref:r,width:l,height:u,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?s:m.jsx("polygon",{points:"0,0 30,0 15,10"})})});eg.displayName=E1;var k1=eg;function b1(n){const[r,s]=C.useState(void 0);return Rr(()=>{if(n){s({width:n.offsetWidth,height:n.offsetHeight});const l=new ResizeObserver(u=>{if(!Array.isArray(u)||!u.length)return;const d=u[0];let f,p;if("borderBoxSize"in d){const g=d.borderBoxSize,y=Array.isArray(g)?g[0]:g;f=y.inlineSize,p=y.blockSize}else f=n.offsetWidth,p=n.offsetHeight;s({width:f,height:p})});return l.observe(n,{box:"border-box"}),()=>l.unobserve(n)}else s(void 0)},[n]),r}var tg="Popper",[ng,rg]=ml(tg),[EC,og]=ng(tg),sg="PopperAnchor",ig=C.forwardRef((n,r)=>{const{__scopePopper:s,virtualRef:l,...u}=n,d=og(sg,s),f=C.useRef(null),p=Ht(r,f);return C.useEffect(()=>{d.onAnchorChange((l==null?void 0:l.current)||f.current)}),l?null:m.jsx(vt.div,{...u,ref:p})});ig.displayName=sg;var bc="PopperContent",[P1,N1]=ng(bc),lg=C.forwardRef((n,r)=>{var Ne,ke,Oe,et,sr,To;const{__scopePopper:s,side:l="bottom",sideOffset:u=0,align:d="center",alignOffset:f=0,arrowPadding:p=0,avoidCollisions:g=!0,collisionBoundary:y=[],collisionPadding:x=0,sticky:v="partial",hideWhenDetached:E=!1,updatePositionStrategy:N="optimized",onPlaced:j,...S}=n,k=og(bc,s),[A,D]=C.useState(null),I=Ht(r,qt=>D(qt)),[F,H]=C.useState(null),q=b1(F),B=(q==null?void 0:q.width)??0,J=(q==null?void 0:q.height)??0,re=l+(d!=="center"?"-"+d:""),de=typeof x=="number"?x:{top:0,right:0,bottom:0,left:0,...x},pe=Array.isArray(y)?y:[y],ee=pe.length>0,me={padding:de,boundary:pe.filter(R1),altBoundary:ee},{refs:K,floatingStyles:ve,placement:ue,isPositioned:ae,middlewareData:L}=p1({strategy:"fixed",placement:re,whileElementsMounted:(...qt)=>i1(...qt,{animationFrame:N==="always"}),elements:{reference:k.anchor},middleware:[g1({mainAxis:u+J,alignmentAxis:f}),g&&y1({mainAxis:!0,crossAxis:!1,limiter:v==="partial"?v1():void 0,...me}),g&&x1({...me}),w1({...me,apply:({elements:qt,rects:Mr,availableWidth:As,availableHeight:Ds})=>{const{width:Ro,height:Ls}=Mr.reference,Cn=qt.floating.style;Cn.setProperty("--radix-popper-available-width",`${As}px`),Cn.setProperty("--radix-popper-available-height",`${Ds}px`),Cn.setProperty("--radix-popper-anchor-width",`${Ro}px`),Cn.setProperty("--radix-popper-anchor-height",`${Ls}px`)}}),F&&C1({element:F,padding:p}),O1({arrowWidth:B,arrowHeight:J}),E&&S1({strategy:"referenceHidden",...me})]}),[Y,Q]=cg(ue),R=on(j);Rr(()=>{ae&&(R==null||R())},[ae,R]);const z=(Ne=L.arrow)==null?void 0:Ne.x,fe=(ke=L.arrow)==null?void 0:ke.y,he=((Oe=L.arrow)==null?void 0:Oe.centerOffset)!==0,[Se,Ce]=C.useState();return Rr(()=>{A&&Ce(window.getComputedStyle(A).zIndex)},[A]),m.jsx("div",{ref:K.setFloating,"data-radix-popper-content-wrapper":"",style:{...ve,transform:ae?ve.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Se,"--radix-popper-transform-origin":[(et=L.transformOrigin)==null?void 0:et.x,(sr=L.transformOrigin)==null?void 0:sr.y].join(" "),...((To=L.hide)==null?void 0:To.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:m.jsx(P1,{scope:s,placedSide:Y,onArrowChange:H,arrowX:z,arrowY:fe,shouldHideArrow:he,children:m.jsx(vt.div,{"data-side":Y,"data-align":Q,...S,ref:I,style:{...S.style,animation:ae?void 0:"none"}})})})});lg.displayName=bc;var ag="PopperArrow",T1={top:"bottom",right:"left",bottom:"top",left:"right"},ug=C.forwardRef(function(r,s){const{__scopePopper:l,...u}=r,d=N1(ag,l),f=T1[d.placedSide];return m.jsx("span",{ref:d.onArrowChange,style:{position:"absolute",left:d.arrowX,top:d.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[d.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[d.placedSide],visibility:d.shouldHideArrow?"hidden":void 0},children:m.jsx(k1,{...u,ref:s,style:{...u.style,display:"block"}})})});ug.displayName=ag;function R1(n){return n!==null}var O1=n=>({name:"transformOrigin",options:n,fn(r){var k,A,D;const{placement:s,rects:l,middlewareData:u}=r,f=((k=u.arrow)==null?void 0:k.centerOffset)!==0,p=f?0:n.arrowWidth,g=f?0:n.arrowHeight,[y,x]=cg(s),v={start:"0%",center:"50%",end:"100%"}[x],E=(((A=u.arrow)==null?void 0:A.x)??0)+p/2,N=(((D=u.arrow)==null?void 0:D.y)??0)+g/2;let j="",S="";return y==="bottom"?(j=f?v:`${E}px`,S=`${-g}px`):y==="top"?(j=f?v:`${E}px`,S=`${l.floating.height+g}px`):y==="right"?(j=`${-g}px`,S=f?v:`${N}px`):y==="left"&&(j=`${l.floating.width+g}px`,S=f?v:`${N}px`),{data:{x:j,y:S}}}});function cg(n){const[r,s="center"]=n.split("-");return[r,s]}var j1=ig,M1=lg,_1=ug,[Cl,kC]=ml("Tooltip",[rg]),Pc=rg(),dg="TooltipProvider",A1=700,ap="tooltip.open",[D1,fg]=Cl(dg),hg=n=>{const{__scopeTooltip:r,delayDuration:s=A1,skipDelayDuration:l=300,disableHoverableContent:u=!1,children:d}=n,f=C.useRef(!0),p=C.useRef(!1),g=C.useRef(0);return C.useEffect(()=>{const y=g.current;return()=>window.clearTimeout(y)},[]),m.jsx(D1,{scope:r,isOpenDelayedRef:f,delayDuration:s,onOpen:C.useCallback(()=>{window.clearTimeout(g.current),f.current=!1},[]),onClose:C.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>f.current=!0,l)},[l]),isPointerInTransitRef:p,onPointerInTransitChange:C.useCallback(y=>{p.current=y},[]),disableHoverableContent:u,children:d})};hg.displayName=dg;var pg="Tooltip",[bC,El]=Cl(pg),nc="TooltipTrigger",L1=C.forwardRef((n,r)=>{const{__scopeTooltip:s,...l}=n,u=El(nc,s),d=fg(nc,s),f=Pc(s),p=C.useRef(null),g=Ht(r,p,u.onTriggerChange),y=C.useRef(!1),x=C.useRef(!1),v=C.useCallback(()=>y.current=!1,[]);return C.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),m.jsx(j1,{asChild:!0,...f,children:m.jsx(vt.button,{"aria-describedby":u.open?u.contentId:void 0,"data-state":u.stateAttribute,...l,ref:g,onPointerMove:Qe(n.onPointerMove,E=>{E.pointerType!=="touch"&&!x.current&&!d.isPointerInTransitRef.current&&(u.onTriggerEnter(),x.current=!0)}),onPointerLeave:Qe(n.onPointerLeave,()=>{u.onTriggerLeave(),x.current=!1}),onPointerDown:Qe(n.onPointerDown,()=>{u.open&&u.onClose(),y.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:Qe(n.onFocus,()=>{y.current||u.onOpen()}),onBlur:Qe(n.onBlur,u.onClose),onClick:Qe(n.onClick,u.onClose)})})});L1.displayName=nc;var I1="TooltipPortal",[PC,F1]=Cl(I1,{forceMount:void 0}),Eo="TooltipContent",mg=C.forwardRef((n,r)=>{const s=F1(Eo,n.__scopeTooltip),{forceMount:l=s.forceMount,side:u="top",...d}=n,f=El(Eo,n.__scopeTooltip);return m.jsx(dc,{present:l||f.open,children:f.disableHoverableContent?m.jsx(gg,{side:u,...d,ref:r}):m.jsx(z1,{side:u,...d,ref:r})})}),z1=C.forwardRef((n,r)=>{const s=El(Eo,n.__scopeTooltip),l=fg(Eo,n.__scopeTooltip),u=C.useRef(null),d=Ht(r,u),[f,p]=C.useState(null),{trigger:g,onClose:y}=s,x=u.current,{onPointerInTransitChange:v}=l,E=C.useCallback(()=>{p(null),v(!1)},[v]),N=C.useCallback((j,S)=>{const k=j.currentTarget,A={x:j.clientX,y:j.clientY},D=V1(A,k.getBoundingClientRect()),I=B1(A,D),F=Q1(S.getBoundingClientRect()),H=Y1([...I,...F]);p(H),v(!0)},[v]);return C.useEffect(()=>()=>E(),[E]),C.useEffect(()=>{if(g&&x){const j=k=>N(k,x),S=k=>N(k,g);return g.addEventListener("pointerleave",j),x.addEventListener("pointerleave",S),()=>{g.removeEventListener("pointerleave",j),x.removeEventListener("pointerleave",S)}}},[g,x,N,E]),C.useEffect(()=>{if(f){const j=S=>{const k=S.target,A={x:S.clientX,y:S.clientY},D=(g==null?void 0:g.contains(k))||(x==null?void 0:x.contains(k)),I=!q1(A,f);D?E():I&&(E(),y())};return document.addEventListener("pointermove",j),()=>document.removeEventListener("pointermove",j)}},[g,x,f,y,E]),m.jsx(gg,{...n,ref:d})}),[W1,U1]=Cl(pg,{isInside:!1}),$1=Hx("TooltipContent"),gg=C.forwardRef((n,r)=>{const{__scopeTooltip:s,children:l,"aria-label":u,onEscapeKeyDown:d,onPointerDownOutside:f,...p}=n,g=El(Eo,s),y=Pc(s),{onClose:x}=g;return C.useEffect(()=>(document.addEventListener(ap,x),()=>document.removeEventListener(ap,x)),[x]),C.useEffect(()=>{if(g.trigger){const v=E=>{const N=E.target;N!=null&&N.contains(g.trigger)&&x()};return window.addEventListener("scroll",v,{capture:!0}),()=>window.removeEventListener("scroll",v,{capture:!0})}},[g.trigger,x]),m.jsx(cc,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:v=>v.preventDefault(),onDismiss:x,children:m.jsxs(M1,{"data-state":g.stateAttribute,...y,...p,ref:r,style:{...p.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[m.jsx($1,{children:l}),m.jsx(W1,{scope:s,isInside:!0,children:m.jsx(d0,{id:g.contentId,role:"tooltip",children:u||l})})]})})});mg.displayName=Eo;var yg="TooltipArrow",H1=C.forwardRef((n,r)=>{const{__scopeTooltip:s,...l}=n,u=Pc(s);return U1(yg,s).isInside?null:m.jsx(_1,{...u,...l,ref:r})});H1.displayName=yg;function V1(n,r){const s=Math.abs(r.top-n.y),l=Math.abs(r.bottom-n.y),u=Math.abs(r.right-n.x),d=Math.abs(r.left-n.x);switch(Math.min(s,l,u,d)){case d:return"left";case u:return"right";case s:return"top";case l:return"bottom";default:throw new Error("unreachable")}}function B1(n,r,s=5){const l=[];switch(r){case"top":l.push({x:n.x-s,y:n.y+s},{x:n.x+s,y:n.y+s});break;case"bottom":l.push({x:n.x-s,y:n.y-s},{x:n.x+s,y:n.y-s});break;case"left":l.push({x:n.x+s,y:n.y-s},{x:n.x+s,y:n.y+s});break;case"right":l.push({x:n.x-s,y:n.y-s},{x:n.x-s,y:n.y+s});break}return l}function Q1(n){const{top:r,right:s,bottom:l,left:u}=n;return[{x:u,y:r},{x:s,y:r},{x:s,y:l},{x:u,y:l}]}function q1(n,r){const{x:s,y:l}=n;let u=!1;for(let d=0,f=r.length-1;d<r.length;f=d++){const p=r[d].x,g=r[d].y,y=r[f].x,x=r[f].y;g>l!=x>l&&s<(y-p)*(l-g)/(x-g)+p&&(u=!u)}return u}function Y1(n){const r=n.slice();return r.sort((s,l)=>s.x<l.x?-1:s.x>l.x?1:s.y<l.y?-1:s.y>l.y?1:0),K1(r)}function K1(n){if(n.length<=1)return n.slice();const r=[];for(let l=0;l<n.length;l++){const u=n[l];for(;r.length>=2;){const d=r[r.length-1],f=r[r.length-2];if((d.x-f.x)*(u.y-f.y)>=(d.y-f.y)*(u.x-f.x))r.pop();else break}r.push(u)}r.pop();const s=[];for(let l=n.length-1;l>=0;l--){const u=n[l];for(;s.length>=2;){const d=s[s.length-1],f=s[s.length-2];if((d.x-f.x)*(u.y-f.y)>=(d.y-f.y)*(u.x-f.x))s.pop();else break}s.push(u)}return s.pop(),r.length===1&&s.length===1&&r[0].x===s[0].x&&r[0].y===s[0].y?r:r.concat(s)}var G1=hg,vg=mg;const X1=G1,J1=C.forwardRef(({className:n,sideOffset:r=4,...s},l)=>m.jsx(vg,{ref:l,sideOffset:r,className:Ze("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",n),...s}));J1.displayName=vg.displayName;function Z1(){const[n,r]=C.useState(!1),[s,l]=C.useState(!1);C.useEffect(()=>{const f=()=>{l(window.scrollY>50)};return window.addEventListener("scroll",f),()=>window.removeEventListener("scroll",f)},[]);const u=f=>{const p=document.getElementById(f);p&&(p.scrollIntoView({behavior:"smooth",block:"start"}),r(!1))},d=[{id:"home",label:"Home"},{id:"about",label:"About"},{id:"skills",label:"Skills"},{id:"projects",label:"Projects"},{id:"contact",label:"Contact"}];return m.jsxs("nav",{className:`fixed top-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm z-50 transition-all duration-300 ${s?"shadow-lg":"shadow-sm"}`,children:[m.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:m.jsxs("div",{className:"flex justify-between items-center h-14 sm:h-16",children:[m.jsx("div",{className:"flex-shrink-0",children:m.jsx("span",{className:"text-lg sm:text-xl font-bold text-primary dark:text-white",children:"Your Name"})}),m.jsx("div",{className:"hidden md:block",children:m.jsx("div",{className:"ml-10 flex items-baseline space-x-6 lg:space-x-8",children:d.map(f=>m.jsx("button",{onClick:()=>u(f.id),className:"text-gray-700 dark:text-gray-300 hover:text-secondary dark:hover:text-secondary transition-colors duration-200 font-medium text-sm lg:text-base",children:f.label},f.id))})}),m.jsx("div",{className:"md:hidden",children:m.jsx("button",{onClick:()=>r(!n),className:"text-gray-700 dark:text-gray-300 hover:text-secondary transition-colors duration-200 p-2 -m-2",children:n?m.jsx(_m,{className:"h-5 w-5 sm:h-6 sm:w-6"}):m.jsx(z0,{className:"h-5 w-5 sm:h-6 sm:w-6"})})})]})}),n&&m.jsx("div",{className:"md:hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 shadow-lg",children:m.jsx("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:d.map(f=>m.jsx("button",{onClick:()=>u(f.id),className:"block w-full text-left px-3 py-3 text-gray-700 dark:text-gray-300 hover:text-secondary dark:hover:text-secondary hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200 font-medium rounded-md",children:f.label},f.id))})})]})}function eS(){const n=r=>{const s=document.getElementById(r);s&&s.scrollIntoView({behavior:"smooth",block:"start"})};return m.jsxs("section",{id:"home",className:"min-h-screen flex items-center justify-center gradient-bg text-white relative overflow-hidden pt-16",children:[m.jsx("div",{className:"absolute inset-0 bg-black opacity-20"}),m.jsx("div",{className:"relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto py-8",children:m.jsxs("div",{className:"animate-fade-in",children:[m.jsx("div",{className:"mb-6 sm:mb-8 inline-block",children:m.jsx("div",{className:"w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 rounded-full bg-white/20 glass-effect mx-auto flex items-center justify-center",children:m.jsx(Mm,{className:"w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 text-white/70"})})}),m.jsx("h1",{className:"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 leading-tight px-2",children:"Your Name"}),m.jsx("p",{className:"text-lg sm:text-xl md:text-2xl lg:text-3xl font-light mb-6 sm:mb-8 text-white/90 px-2",children:"IT Engineer & Quality Testing Specialist"}),m.jsx("p",{className:"text-base sm:text-lg md:text-xl mb-8 sm:mb-12 text-white/80 max-w-3xl mx-auto leading-relaxed px-4",children:"Specialized in system support, development, and automation testing. Currently pursuing Quality Engineer certification to deliver robust, reliable software solutions."}),m.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4",children:[m.jsx("button",{onClick:()=>n("projects"),className:"w-full sm:w-auto bg-white text-primary px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold hover:bg-gray-100 transform hover:scale-105 transition-all duration-200 shadow-lg text-sm sm:text-base",children:"View My Work"}),m.jsx("button",{onClick:()=>n("contact"),className:"w-full sm:w-auto border-2 border-white text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold hover:bg-white hover:text-primary transition-all duration-200 text-sm sm:text-base",children:"Get In Touch"})]})]})}),m.jsx("div",{className:"absolute bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce",children:m.jsx(_0,{className:"w-5 h-5 sm:w-6 sm:h-6 text-white/70"})})]})}function tS(){return m.jsx("section",{id:"about",className:"py-12 sm:py-16 lg:py-20 bg-white dark:bg-gray-50",children:m.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[m.jsxs("div",{className:"text-center mb-12 sm:mb-16 animate-on-scroll",children:[m.jsx("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-primary mb-4 sm:mb-6",children:"About Me"}),m.jsx("div",{className:"w-16 sm:w-24 h-1 bg-secondary mx-auto mb-6 sm:mb-8"}),m.jsx("p",{className:"text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed px-4",children:"IT Engineer dedicated to system excellence and quality assurance through comprehensive testing methodologies."})]}),m.jsxs("div",{className:"grid lg:grid-cols-2 gap-8 lg:gap-12 items-center",children:[m.jsx("div",{className:"animate-on-scroll order-2 lg:order-1",children:m.jsxs("div",{className:"relative max-w-sm mx-auto lg:max-w-none",children:[m.jsx("div",{className:"aspect-square bg-gradient-to-br from-secondary to-accent rounded-2xl shadow-2xl overflow-hidden",children:m.jsx("div",{className:"w-full h-full bg-gray-200 flex items-center justify-center",children:m.jsx(Mm,{className:"w-16 sm:w-20 md:w-24 h-16 sm:h-20 md:h-24 text-gray-400"})})}),m.jsx("div",{className:"absolute -bottom-4 sm:-bottom-6 -right-4 sm:-right-6 w-16 sm:w-24 h-16 sm:h-24 bg-accent rounded-full opacity-20"}),m.jsx("div",{className:"absolute -top-4 sm:-top-6 -left-4 sm:-left-6 w-12 sm:w-16 h-12 sm:h-16 bg-secondary rounded-full opacity-30"})]})}),m.jsxs("div",{className:"animate-on-scroll order-1 lg:order-2",children:[m.jsx("h3",{className:"text-xl sm:text-2xl md:text-3xl font-bold text-primary mb-4 sm:mb-6",children:"IT Engineering Professional"}),m.jsxs("div",{className:"space-y-3 sm:space-y-4 text-base sm:text-lg text-gray-700 leading-relaxed",children:[m.jsx("p",{children:"With a strong foundation in IT engineering, I specialize in system support and development, ensuring robust and reliable software solutions that meet the highest quality standards."}),m.jsx("p",{children:"Currently advancing my expertise through automation testing studies, working toward Quality Engineer certification. My focus is on implementing comprehensive testing frameworks that guarantee software excellence."}),m.jsx("p",{children:"I combine technical proficiency with systematic problem-solving approaches, delivering solutions that are both innovative and dependable for complex IT environments."})]}),m.jsxs("div",{className:"mt-6 sm:mt-8 flex flex-col sm:flex-row sm:flex-wrap gap-3 sm:gap-4",children:[m.jsxs("div",{className:"flex items-center bg-gray-50 px-3 sm:px-4 py-2 rounded-lg",children:[m.jsx(Om,{className:"w-4 sm:w-5 h-4 sm:h-5 text-secondary mr-2 flex-shrink-0"}),m.jsx("span",{className:"font-medium text-sm sm:text-base",children:"Available Globally"})]}),m.jsxs("div",{className:"flex items-center bg-gray-50 px-3 sm:px-4 py-2 rounded-lg",children:[m.jsx(Rm,{className:"w-4 sm:w-5 h-4 sm:h-5 text-accent mr-2 flex-shrink-0"}),m.jsx("span",{className:"font-medium text-sm sm:text-base",children:"Open to Opportunities"})]})]})]})]})]})})}function nS(){const n=[{title:"System Support",icon:H0,color:"from-secondary to-accent",skills:[{name:"System Administration",level:88},{name:"Troubleshooting",level:92},{name:"Network Configuration",level:85}]},{title:"System Development",icon:D0,color:"from-accent to-secondary",skills:[{name:"Software Architecture",level:82},{name:"Database Design",level:78},{name:"API Development",level:85}]},{title:"Quality Testing",icon:W0,color:"from-green-500 to-blue-500",skills:[{name:"Automation Testing",level:90},{name:"Test Planning",level:88},{name:"Quality Assurance",level:95}]}];return m.jsx("section",{id:"skills",className:"py-12 sm:py-16 lg:py-20 bg-gray-50 dark:bg-white",children:m.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[m.jsxs("div",{className:"text-center mb-12 sm:mb-16 animate-on-scroll",children:[m.jsx("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-primary mb-4 sm:mb-6",children:"Skills & Expertise"}),m.jsx("div",{className:"w-16 sm:w-24 h-1 bg-secondary mx-auto mb-6 sm:mb-8"}),m.jsx("p",{className:"text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed px-4",children:"Core competencies in IT engineering, system development, and quality assurance."})]}),m.jsx("div",{className:"grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8",children:n.map((r,s)=>{const l=r.icon;return m.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 sm:p-8 hover:shadow-xl transition-shadow duration-300 animate-on-scroll",children:[m.jsxs("div",{className:"text-center mb-4 sm:mb-6",children:[m.jsx("div",{className:`w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br ${r.color} rounded-lg mx-auto mb-3 sm:mb-4 flex items-center justify-center`,children:m.jsx(l,{className:"w-6 h-6 sm:w-8 sm:h-8 text-white"})}),m.jsx("h3",{className:"text-lg sm:text-xl font-bold text-primary",children:r.title})]}),m.jsx("div",{className:"space-y-3",children:r.skills.map((u,d)=>m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsx("span",{className:"text-sm sm:text-base text-gray-700 flex-shrink-0 pr-2",children:u.name}),m.jsx("div",{className:"flex-1 mx-2 sm:mx-3 bg-gray-200 rounded-full h-2",children:m.jsx("div",{className:`bg-gradient-to-r ${r.color} h-2 rounded-full transition-all duration-1000`,style:{width:`${u.level}%`}})}),m.jsxs("span",{className:"text-xs sm:text-sm text-gray-500 flex-shrink-0",children:[u.level,"%"]})]},d))})]},s)})})]})})}function rS(){const n=[{title:"Enterprise System Integration",description:"Led comprehensive system integration project connecting multiple legacy systems with modern cloud infrastructure, ensuring seamless data flow and improved operational efficiency.",icon:F0,color:"from-blue-400 to-purple-600",technologies:[{name:"System Integration",color:"bg-secondary/10 text-secondary"},{name:"Cloud Migration",color:"bg-accent/10 text-accent"},{name:"API Development",color:"bg-purple-100 text-purple-700"}]},{title:"Automated Testing Framework",description:"Developed comprehensive automation testing suite for enterprise applications, reducing manual testing time by 75% and improving software quality metrics.",icon:L0,color:"from-green-400 to-blue-600",technologies:[{name:"Test Automation",color:"bg-secondary/10 text-secondary"},{name:"Quality Assurance",color:"bg-accent/10 text-accent"},{name:"CI/CD Pipeline",color:"bg-yellow-100 text-yellow-700"}]},{title:"System Monitoring Dashboard",description:"Built real-time system monitoring solution providing comprehensive insights into system performance, resource utilization, and proactive issue detection.",icon:I0,color:"from-orange-400 to-red-600",technologies:[{name:"System Monitoring",color:"bg-secondary/10 text-secondary"},{name:"Performance Analytics",color:"bg-accent/10 text-accent"},{name:"Alert Management",color:"bg-blue-100 text-blue-700"}]}];return m.jsx("section",{id:"projects",className:"py-12 sm:py-16 lg:py-20 bg-white dark:bg-gray-50",children:m.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[m.jsxs("div",{className:"text-center mb-12 sm:mb-16 animate-on-scroll",children:[m.jsx("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-primary mb-4 sm:mb-6",children:"Featured Projects"}),m.jsx("div",{className:"w-16 sm:w-24 h-1 bg-secondary mx-auto mb-6 sm:mb-8"}),m.jsx("p",{className:"text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed px-4",children:"Key projects demonstrating expertise in system engineering, automation testing, and quality assurance implementation."})]}),m.jsx("div",{className:"grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8",children:n.map((r,s)=>{const l=r.icon;return m.jsxs("div",{className:"group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 animate-on-scroll",children:[m.jsxs("div",{className:`aspect-video bg-gradient-to-br ${r.color} relative overflow-hidden`,children:[m.jsx("div",{className:"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300"}),m.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:m.jsxs("div",{className:"text-white text-center",children:[m.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-white/20 rounded-lg mx-auto mb-2 flex items-center justify-center backdrop-blur-sm",children:m.jsx(l,{className:"w-6 h-6 sm:w-8 sm:h-8"})}),m.jsx("p",{className:"text-xs sm:text-sm font-medium",children:"Project Overview"})]})})]}),m.jsxs("div",{className:"p-4 sm:p-6",children:[m.jsx("h3",{className:"text-lg sm:text-xl font-bold text-primary mb-2 sm:mb-3",children:r.title}),m.jsx("p",{className:"text-sm sm:text-base text-gray-600 mb-3 sm:mb-4 leading-relaxed",children:r.description}),m.jsx("div",{className:"flex flex-wrap gap-1 sm:gap-2 mb-3 sm:mb-4",children:r.technologies.map((u,d)=>m.jsx("span",{className:`px-2 sm:px-3 py-1 ${u.color} text-xs sm:text-sm font-medium rounded-full`,children:u.name},d))}),m.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3",children:[m.jsxs("button",{className:"flex-1 bg-primary text-white text-center py-2 px-3 sm:px-4 rounded-lg hover:bg-primary/90 transition-colors duration-200 font-medium flex items-center justify-center gap-2 text-sm sm:text-base",children:[m.jsx(Xh,{className:"w-3 h-3 sm:w-4 sm:h-4"}),"Details"]}),m.jsxs("button",{className:"flex-1 border border-primary text-primary text-center py-2 px-3 sm:px-4 rounded-lg hover:bg-primary hover:text-white transition-colors duration-200 font-medium flex items-center justify-center gap-2 text-sm sm:text-base",children:[m.jsx(gc,{className:"w-3 h-3 sm:w-4 sm:h-4"}),"Documentation"]})]})]})]},s)})}),m.jsx("div",{className:"text-center mt-8 sm:mt-12 animate-on-scroll",children:m.jsxs("button",{className:"inline-flex items-center bg-secondary text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold hover:bg-secondary/90 transition-colors duration-200 text-sm sm:text-base",children:["View All Projects",m.jsx(Xh,{className:"w-4 h-4 sm:w-5 sm:h-5 ml-2"})]})})]})})}const oS=mc("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),xg=C.forwardRef(({className:n,variant:r,size:s,asChild:l=!1,...u},d)=>{const f=l?Ux:"button";return m.jsx(f,{className:Ze(oS({variant:r,size:s,className:n})),ref:d,...u})});xg.displayName="Button";const Ss=C.forwardRef(({className:n,type:r,...s},l)=>m.jsx("input",{type:r,className:Ze("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",n),ref:l,...s}));Ss.displayName="Input";const wg=C.forwardRef(({className:n,...r},s)=>m.jsx("textarea",{className:Ze("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",n),ref:s,...r}));wg.displayName="Textarea";function sS(){const[n,r]=C.useState({firstName:"",lastName:"",email:"",subject:"",message:""}),{toast:s}=Jp(),l=jx({mutationFn:async g=>await Mx("/api/contacts","POST",g),onSuccess:()=>{s({title:"Message sent!",description:"Thank you for your message! I'll get back to you soon."}),r({firstName:"",lastName:"",email:"",subject:"",message:""})},onError:g=>{s({title:"Error sending message",description:"Please try again later.",variant:"destructive"})}}),u=g=>{r({...n,[g.target.name]:g.target.value})},d=g=>{g.preventDefault(),l.mutate(n)},f=[{icon:Rm,title:"Email",content:"<EMAIL>",href:"mailto:<EMAIL>",color:"bg-secondary/10 text-secondary"},{icon:U0,title:"Phone",content:"+****************",href:"tel:+1234567890",color:"bg-accent/10 text-accent"},{icon:Om,title:"Location",content:"Your City, Country",href:"#",color:"bg-purple-100 text-purple-600"}],p=[{icon:jm,href:"#",color:"bg-blue-600 hover:bg-blue-700"},{icon:Tm,href:"#",color:"bg-blue-800 hover:bg-blue-900"},{icon:gc,href:"#",color:"bg-gray-800 hover:bg-gray-900"}];return m.jsx("section",{id:"contact",className:"py-12 sm:py-16 lg:py-20 bg-gray-50 dark:bg-white",children:m.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[m.jsxs("div",{className:"text-center mb-12 sm:mb-16 animate-on-scroll",children:[m.jsx("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-primary mb-4 sm:mb-6",children:"Let's Work Together"}),m.jsx("div",{className:"w-16 sm:w-24 h-1 bg-secondary mx-auto mb-6 sm:mb-8"}),m.jsx("p",{className:"text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed px-4",children:"Ready to collaborate on your next project or discuss opportunities in IT engineering and quality assurance."})]}),m.jsxs("div",{className:"grid lg:grid-cols-2 gap-8 lg:gap-12 items-start",children:[m.jsx("div",{className:"animate-on-scroll",children:m.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 sm:p-8",children:[m.jsx("h3",{className:"text-xl sm:text-2xl font-bold text-primary mb-4 sm:mb-6",children:"Get In Touch"}),m.jsx("div",{className:"space-y-4 sm:space-y-6",children:f.map((g,y)=>{const x=g.icon;return m.jsxs("div",{className:"flex items-start",children:[m.jsx("div",{className:`flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 ${g.color} rounded-lg flex items-center justify-center mr-3 sm:mr-4`,children:m.jsx(x,{className:"w-5 h-5 sm:w-6 sm:h-6"})}),m.jsxs("div",{children:[m.jsx("h4",{className:"font-semibold text-primary mb-1 text-sm sm:text-base",children:g.title}),m.jsx("a",{href:g.href,className:"text-gray-600 hover:text-secondary transition-colors duration-200 text-sm sm:text-base",children:g.content})]})]},y)})}),m.jsxs("div",{className:"mt-6 sm:mt-8 pt-6 sm:pt-8 border-t border-gray-200",children:[m.jsx("h4",{className:"font-semibold text-primary mb-3 sm:mb-4 text-sm sm:text-base",children:"Connect With Me"}),m.jsx("div",{className:"flex space-x-3 sm:space-x-4",children:p.map((g,y)=>{const x=g.icon;return m.jsx("a",{href:g.href,className:`w-9 h-9 sm:w-10 sm:h-10 ${g.color} text-white rounded-lg flex items-center justify-center transition-colors duration-200`,children:m.jsx(x,{className:"w-4 h-4 sm:w-5 sm:h-5"})},y)})})]})]})}),m.jsx("div",{className:"animate-on-scroll",children:m.jsxs("form",{onSubmit:d,className:"bg-white rounded-xl shadow-lg p-6 sm:p-8",children:[m.jsx("h3",{className:"text-xl sm:text-2xl font-bold text-primary mb-4 sm:mb-6",children:"Send Message"}),m.jsxs("div",{className:"space-y-4 sm:space-y-6",children:[m.jsxs("div",{className:"grid sm:grid-cols-2 gap-3 sm:gap-4",children:[m.jsxs("div",{children:[m.jsx("label",{htmlFor:"firstName",className:"block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"First Name"}),m.jsx(Ss,{type:"text",id:"firstName",name:"firstName",value:n.firstName,onChange:u,placeholder:"John",required:!0,className:"text-sm sm:text-base"})]}),m.jsxs("div",{children:[m.jsx("label",{htmlFor:"lastName",className:"block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Last Name"}),m.jsx(Ss,{type:"text",id:"lastName",name:"lastName",value:n.lastName,onChange:u,placeholder:"Doe",required:!0,className:"text-sm sm:text-base"})]})]}),m.jsxs("div",{children:[m.jsx("label",{htmlFor:"email",className:"block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Email"}),m.jsx(Ss,{type:"email",id:"email",name:"email",value:n.email,onChange:u,placeholder:"<EMAIL>",required:!0,className:"text-sm sm:text-base"})]}),m.jsxs("div",{children:[m.jsx("label",{htmlFor:"subject",className:"block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Subject"}),m.jsx(Ss,{type:"text",id:"subject",name:"subject",value:n.subject,onChange:u,placeholder:"Project Opportunity",required:!0,className:"text-sm sm:text-base"})]}),m.jsxs("div",{children:[m.jsx("label",{htmlFor:"message",className:"block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Message"}),m.jsx(wg,{id:"message",name:"message",rows:4,value:n.message,onChange:u,placeholder:"Tell me about your project or opportunity...",required:!0,className:"resize-none text-sm sm:text-base"})]}),m.jsxs(xg,{type:"submit",disabled:l.isPending,className:"w-full bg-secondary hover:bg-secondary/90 text-white py-3 sm:py-4 px-4 sm:px-6 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center gap-2 text-sm sm:text-base disabled:opacity-50",children:[m.jsx($0,{className:"w-3 h-3 sm:w-4 sm:h-4"}),l.isPending?"Sending...":"Send Message"]})]})]})})]})]})})}function iS(){const n=[{icon:jm,href:"#"},{icon:Tm,href:"#"},{icon:gc,href:"#"}];return m.jsx("footer",{className:"bg-primary text-white py-8 sm:py-12",children:m.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:m.jsxs("div",{className:"text-center",children:[m.jsx("h3",{className:"text-xl sm:text-2xl font-bold mb-3 sm:mb-4",children:"Your Name"}),m.jsx("p",{className:"text-gray-300 mb-4 sm:mb-6 max-w-2xl mx-auto text-sm sm:text-base leading-relaxed px-4",children:"Thank you for visiting my portfolio. Ready to collaborate on innovative IT solutions and quality engineering projects that make a real impact."}),m.jsx("div",{className:"flex justify-center space-x-4 sm:space-x-6 mb-6 sm:mb-8",children:n.map((r,s)=>{const l=r.icon;return m.jsx("a",{href:r.href,className:"text-gray-300 hover:text-white transition-colors duration-200 p-2 -m-2",children:m.jsx(l,{className:"w-5 h-5 sm:w-6 sm:h-6"})},s)})}),m.jsx("div",{className:"border-t border-gray-600 pt-6 sm:pt-8",children:m.jsx("p",{className:"text-gray-300 text-xs sm:text-sm",children:"© 2025 Your Name. All rights reserved. Committed to engineering excellence."})})]})})})}function lS(){return C.useEffect(()=>{const n={threshold:.1,rootMargin:"0px 0px -50px 0px"},r=new IntersectionObserver(s=>{s.forEach(l=>{l.isIntersecting&&l.target.classList.add("animate")})},n);return document.querySelectorAll(".animate-on-scroll").forEach(s=>{r.observe(s)}),()=>r.disconnect()},[]),m.jsxs("div",{className:"min-h-screen",children:[m.jsx(Z1,{}),m.jsx(eS,{}),m.jsx(tS,{}),m.jsx(nS,{}),m.jsx(rS,{}),m.jsx(sS,{}),m.jsx(iS,{})]})}const Es=C.forwardRef(({className:n,...r},s)=>m.jsx("div",{ref:s,className:Ze("rounded-lg border bg-card text-card-foreground shadow-sm",n),...r}));Es.displayName="Card";const nl=C.forwardRef(({className:n,...r},s)=>m.jsx("div",{ref:s,className:Ze("flex flex-col space-y-1.5 p-6",n),...r}));nl.displayName="CardHeader";const rl=C.forwardRef(({className:n,...r},s)=>m.jsx("div",{ref:s,className:Ze("text-2xl font-semibold leading-none tracking-tight",n),...r}));rl.displayName="CardTitle";const aS=C.forwardRef(({className:n,...r},s)=>m.jsx("div",{ref:s,className:Ze("text-sm text-muted-foreground",n),...r}));aS.displayName="CardDescription";const ks=C.forwardRef(({className:n,...r},s)=>m.jsx("div",{ref:s,className:Ze("p-6 pt-0",n),...r}));ks.displayName="CardContent";const uS=C.forwardRef(({className:n,...r},s)=>m.jsx("div",{ref:s,className:Ze("flex items-center p-6 pt-0",n),...r}));uS.displayName="CardFooter";const cS=mc("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Mu({className:n,variant:r,...s}){return m.jsx("div",{className:Ze(cS({variant:r}),n),...s})}function Qt(n){const r=Object.prototype.toString.call(n);return n instanceof Date||typeof n=="object"&&r==="[object Date]"?new n.constructor(+n):typeof n=="number"||r==="[object Number]"||typeof n=="string"||r==="[object String]"?new Date(n):new Date(NaN)}function jr(n,r){return n instanceof Date?new n.constructor(r):new Date(r)}const Sg=6048e5,dS=864e5;let fS={};function kl(){return fS}function Ts(n,r){var p,g,y,x;const s=kl(),l=(r==null?void 0:r.weekStartsOn)??((g=(p=r==null?void 0:r.locale)==null?void 0:p.options)==null?void 0:g.weekStartsOn)??s.weekStartsOn??((x=(y=s.locale)==null?void 0:y.options)==null?void 0:x.weekStartsOn)??0,u=Qt(n),d=u.getDay(),f=(d<l?7:0)+d-l;return u.setDate(u.getDate()-f),u.setHours(0,0,0,0),u}function dl(n){return Ts(n,{weekStartsOn:1})}function Cg(n){const r=Qt(n),s=r.getFullYear(),l=jr(n,0);l.setFullYear(s+1,0,4),l.setHours(0,0,0,0);const u=dl(l),d=jr(n,0);d.setFullYear(s,0,4),d.setHours(0,0,0,0);const f=dl(d);return r.getTime()>=u.getTime()?s+1:r.getTime()>=f.getTime()?s:s-1}function up(n){const r=Qt(n);return r.setHours(0,0,0,0),r}function cp(n){const r=Qt(n),s=new Date(Date.UTC(r.getFullYear(),r.getMonth(),r.getDate(),r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds()));return s.setUTCFullYear(r.getFullYear()),+n-+s}function hS(n,r){const s=up(n),l=up(r),u=+s-cp(s),d=+l-cp(l);return Math.round((u-d)/dS)}function pS(n){const r=Cg(n),s=jr(n,0);return s.setFullYear(r,0,4),s.setHours(0,0,0,0),dl(s)}function mS(n){return n instanceof Date||typeof n=="object"&&Object.prototype.toString.call(n)==="[object Date]"}function gS(n){if(!mS(n)&&typeof n!="number")return!1;const r=Qt(n);return!isNaN(Number(r))}function yS(n){const r=Qt(n),s=jr(n,0);return s.setFullYear(r.getFullYear(),0,1),s.setHours(0,0,0,0),s}const vS={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},xS=(n,r,s)=>{let l;const u=vS[n];return typeof u=="string"?l=u:r===1?l=u.one:l=u.other.replace("{{count}}",r.toString()),s!=null&&s.addSuffix?s.comparison&&s.comparison>0?"in "+l:l+" ago":l};function _u(n){return(r={})=>{const s=r.width?String(r.width):n.defaultWidth;return n.formats[s]||n.formats[n.defaultWidth]}}const wS={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},SS={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},CS={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},ES={date:_u({formats:wS,defaultWidth:"full"}),time:_u({formats:SS,defaultWidth:"full"}),dateTime:_u({formats:CS,defaultWidth:"full"})},kS={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},bS=(n,r,s,l)=>kS[n];function vs(n){return(r,s)=>{const l=s!=null&&s.context?String(s.context):"standalone";let u;if(l==="formatting"&&n.formattingValues){const f=n.defaultFormattingWidth||n.defaultWidth,p=s!=null&&s.width?String(s.width):f;u=n.formattingValues[p]||n.formattingValues[f]}else{const f=n.defaultWidth,p=s!=null&&s.width?String(s.width):n.defaultWidth;u=n.values[p]||n.values[f]}const d=n.argumentCallback?n.argumentCallback(r):r;return u[d]}}const PS={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},NS={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},TS={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},RS={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},OS={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},jS={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},MS=(n,r)=>{const s=Number(n),l=s%100;if(l>20||l<10)switch(l%10){case 1:return s+"st";case 2:return s+"nd";case 3:return s+"rd"}return s+"th"},_S={ordinalNumber:MS,era:vs({values:PS,defaultWidth:"wide"}),quarter:vs({values:NS,defaultWidth:"wide",argumentCallback:n=>n-1}),month:vs({values:TS,defaultWidth:"wide"}),day:vs({values:RS,defaultWidth:"wide"}),dayPeriod:vs({values:OS,defaultWidth:"wide",formattingValues:jS,defaultFormattingWidth:"wide"})};function xs(n){return(r,s={})=>{const l=s.width,u=l&&n.matchPatterns[l]||n.matchPatterns[n.defaultMatchWidth],d=r.match(u);if(!d)return null;const f=d[0],p=l&&n.parsePatterns[l]||n.parsePatterns[n.defaultParseWidth],g=Array.isArray(p)?DS(p,v=>v.test(f)):AS(p,v=>v.test(f));let y;y=n.valueCallback?n.valueCallback(g):g,y=s.valueCallback?s.valueCallback(y):y;const x=r.slice(f.length);return{value:y,rest:x}}}function AS(n,r){for(const s in n)if(Object.prototype.hasOwnProperty.call(n,s)&&r(n[s]))return s}function DS(n,r){for(let s=0;s<n.length;s++)if(r(n[s]))return s}function LS(n){return(r,s={})=>{const l=r.match(n.matchPattern);if(!l)return null;const u=l[0],d=r.match(n.parsePattern);if(!d)return null;let f=n.valueCallback?n.valueCallback(d[0]):d[0];f=s.valueCallback?s.valueCallback(f):f;const p=r.slice(u.length);return{value:f,rest:p}}}const IS=/^(\d+)(th|st|nd|rd)?/i,FS=/\d+/i,zS={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},WS={any:[/^b/i,/^(a|c)/i]},US={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},$S={any:[/1/i,/2/i,/3/i,/4/i]},HS={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},VS={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},BS={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},QS={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},qS={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},YS={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},KS={ordinalNumber:LS({matchPattern:IS,parsePattern:FS,valueCallback:n=>parseInt(n,10)}),era:xs({matchPatterns:zS,defaultMatchWidth:"wide",parsePatterns:WS,defaultParseWidth:"any"}),quarter:xs({matchPatterns:US,defaultMatchWidth:"wide",parsePatterns:$S,defaultParseWidth:"any",valueCallback:n=>n+1}),month:xs({matchPatterns:HS,defaultMatchWidth:"wide",parsePatterns:VS,defaultParseWidth:"any"}),day:xs({matchPatterns:BS,defaultMatchWidth:"wide",parsePatterns:QS,defaultParseWidth:"any"}),dayPeriod:xs({matchPatterns:qS,defaultMatchWidth:"any",parsePatterns:YS,defaultParseWidth:"any"})},GS={code:"en-US",formatDistance:xS,formatLong:ES,formatRelative:bS,localize:_S,match:KS,options:{weekStartsOn:0,firstWeekContainsDate:1}};function XS(n){const r=Qt(n);return hS(r,yS(r))+1}function JS(n){const r=Qt(n),s=+dl(r)-+pS(r);return Math.round(s/Sg)+1}function Eg(n,r){var x,v,E,N;const s=Qt(n),l=s.getFullYear(),u=kl(),d=(r==null?void 0:r.firstWeekContainsDate)??((v=(x=r==null?void 0:r.locale)==null?void 0:x.options)==null?void 0:v.firstWeekContainsDate)??u.firstWeekContainsDate??((N=(E=u.locale)==null?void 0:E.options)==null?void 0:N.firstWeekContainsDate)??1,f=jr(n,0);f.setFullYear(l+1,0,d),f.setHours(0,0,0,0);const p=Ts(f,r),g=jr(n,0);g.setFullYear(l,0,d),g.setHours(0,0,0,0);const y=Ts(g,r);return s.getTime()>=p.getTime()?l+1:s.getTime()>=y.getTime()?l:l-1}function ZS(n,r){var p,g,y,x;const s=kl(),l=(r==null?void 0:r.firstWeekContainsDate)??((g=(p=r==null?void 0:r.locale)==null?void 0:p.options)==null?void 0:g.firstWeekContainsDate)??s.firstWeekContainsDate??((x=(y=s.locale)==null?void 0:y.options)==null?void 0:x.firstWeekContainsDate)??1,u=Eg(n,r),d=jr(n,0);return d.setFullYear(u,0,l),d.setHours(0,0,0,0),Ts(d,r)}function eC(n,r){const s=Qt(n),l=+Ts(s,r)-+ZS(s,r);return Math.round(l/Sg)+1}function Re(n,r){const s=n<0?"-":"",l=Math.abs(n).toString().padStart(r,"0");return s+l}const Vn={y(n,r){const s=n.getFullYear(),l=s>0?s:1-s;return Re(r==="yy"?l%100:l,r.length)},M(n,r){const s=n.getMonth();return r==="M"?String(s+1):Re(s+1,2)},d(n,r){return Re(n.getDate(),r.length)},a(n,r){const s=n.getHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return s.toUpperCase();case"aaa":return s;case"aaaaa":return s[0];case"aaaa":default:return s==="am"?"a.m.":"p.m."}},h(n,r){return Re(n.getHours()%12||12,r.length)},H(n,r){return Re(n.getHours(),r.length)},m(n,r){return Re(n.getMinutes(),r.length)},s(n,r){return Re(n.getSeconds(),r.length)},S(n,r){const s=r.length,l=n.getMilliseconds(),u=Math.trunc(l*Math.pow(10,s-3));return Re(u,r.length)}},oo={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},dp={G:function(n,r,s){const l=n.getFullYear()>0?1:0;switch(r){case"G":case"GG":case"GGG":return s.era(l,{width:"abbreviated"});case"GGGGG":return s.era(l,{width:"narrow"});case"GGGG":default:return s.era(l,{width:"wide"})}},y:function(n,r,s){if(r==="yo"){const l=n.getFullYear(),u=l>0?l:1-l;return s.ordinalNumber(u,{unit:"year"})}return Vn.y(n,r)},Y:function(n,r,s,l){const u=Eg(n,l),d=u>0?u:1-u;if(r==="YY"){const f=d%100;return Re(f,2)}return r==="Yo"?s.ordinalNumber(d,{unit:"year"}):Re(d,r.length)},R:function(n,r){const s=Cg(n);return Re(s,r.length)},u:function(n,r){const s=n.getFullYear();return Re(s,r.length)},Q:function(n,r,s){const l=Math.ceil((n.getMonth()+1)/3);switch(r){case"Q":return String(l);case"QQ":return Re(l,2);case"Qo":return s.ordinalNumber(l,{unit:"quarter"});case"QQQ":return s.quarter(l,{width:"abbreviated",context:"formatting"});case"QQQQQ":return s.quarter(l,{width:"narrow",context:"formatting"});case"QQQQ":default:return s.quarter(l,{width:"wide",context:"formatting"})}},q:function(n,r,s){const l=Math.ceil((n.getMonth()+1)/3);switch(r){case"q":return String(l);case"qq":return Re(l,2);case"qo":return s.ordinalNumber(l,{unit:"quarter"});case"qqq":return s.quarter(l,{width:"abbreviated",context:"standalone"});case"qqqqq":return s.quarter(l,{width:"narrow",context:"standalone"});case"qqqq":default:return s.quarter(l,{width:"wide",context:"standalone"})}},M:function(n,r,s){const l=n.getMonth();switch(r){case"M":case"MM":return Vn.M(n,r);case"Mo":return s.ordinalNumber(l+1,{unit:"month"});case"MMM":return s.month(l,{width:"abbreviated",context:"formatting"});case"MMMMM":return s.month(l,{width:"narrow",context:"formatting"});case"MMMM":default:return s.month(l,{width:"wide",context:"formatting"})}},L:function(n,r,s){const l=n.getMonth();switch(r){case"L":return String(l+1);case"LL":return Re(l+1,2);case"Lo":return s.ordinalNumber(l+1,{unit:"month"});case"LLL":return s.month(l,{width:"abbreviated",context:"standalone"});case"LLLLL":return s.month(l,{width:"narrow",context:"standalone"});case"LLLL":default:return s.month(l,{width:"wide",context:"standalone"})}},w:function(n,r,s,l){const u=eC(n,l);return r==="wo"?s.ordinalNumber(u,{unit:"week"}):Re(u,r.length)},I:function(n,r,s){const l=JS(n);return r==="Io"?s.ordinalNumber(l,{unit:"week"}):Re(l,r.length)},d:function(n,r,s){return r==="do"?s.ordinalNumber(n.getDate(),{unit:"date"}):Vn.d(n,r)},D:function(n,r,s){const l=XS(n);return r==="Do"?s.ordinalNumber(l,{unit:"dayOfYear"}):Re(l,r.length)},E:function(n,r,s){const l=n.getDay();switch(r){case"E":case"EE":case"EEE":return s.day(l,{width:"abbreviated",context:"formatting"});case"EEEEE":return s.day(l,{width:"narrow",context:"formatting"});case"EEEEEE":return s.day(l,{width:"short",context:"formatting"});case"EEEE":default:return s.day(l,{width:"wide",context:"formatting"})}},e:function(n,r,s,l){const u=n.getDay(),d=(u-l.weekStartsOn+8)%7||7;switch(r){case"e":return String(d);case"ee":return Re(d,2);case"eo":return s.ordinalNumber(d,{unit:"day"});case"eee":return s.day(u,{width:"abbreviated",context:"formatting"});case"eeeee":return s.day(u,{width:"narrow",context:"formatting"});case"eeeeee":return s.day(u,{width:"short",context:"formatting"});case"eeee":default:return s.day(u,{width:"wide",context:"formatting"})}},c:function(n,r,s,l){const u=n.getDay(),d=(u-l.weekStartsOn+8)%7||7;switch(r){case"c":return String(d);case"cc":return Re(d,r.length);case"co":return s.ordinalNumber(d,{unit:"day"});case"ccc":return s.day(u,{width:"abbreviated",context:"standalone"});case"ccccc":return s.day(u,{width:"narrow",context:"standalone"});case"cccccc":return s.day(u,{width:"short",context:"standalone"});case"cccc":default:return s.day(u,{width:"wide",context:"standalone"})}},i:function(n,r,s){const l=n.getDay(),u=l===0?7:l;switch(r){case"i":return String(u);case"ii":return Re(u,r.length);case"io":return s.ordinalNumber(u,{unit:"day"});case"iii":return s.day(l,{width:"abbreviated",context:"formatting"});case"iiiii":return s.day(l,{width:"narrow",context:"formatting"});case"iiiiii":return s.day(l,{width:"short",context:"formatting"});case"iiii":default:return s.day(l,{width:"wide",context:"formatting"})}},a:function(n,r,s){const u=n.getHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return s.dayPeriod(u,{width:"abbreviated",context:"formatting"});case"aaa":return s.dayPeriod(u,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return s.dayPeriod(u,{width:"narrow",context:"formatting"});case"aaaa":default:return s.dayPeriod(u,{width:"wide",context:"formatting"})}},b:function(n,r,s){const l=n.getHours();let u;switch(l===12?u=oo.noon:l===0?u=oo.midnight:u=l/12>=1?"pm":"am",r){case"b":case"bb":return s.dayPeriod(u,{width:"abbreviated",context:"formatting"});case"bbb":return s.dayPeriod(u,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return s.dayPeriod(u,{width:"narrow",context:"formatting"});case"bbbb":default:return s.dayPeriod(u,{width:"wide",context:"formatting"})}},B:function(n,r,s){const l=n.getHours();let u;switch(l>=17?u=oo.evening:l>=12?u=oo.afternoon:l>=4?u=oo.morning:u=oo.night,r){case"B":case"BB":case"BBB":return s.dayPeriod(u,{width:"abbreviated",context:"formatting"});case"BBBBB":return s.dayPeriod(u,{width:"narrow",context:"formatting"});case"BBBB":default:return s.dayPeriod(u,{width:"wide",context:"formatting"})}},h:function(n,r,s){if(r==="ho"){let l=n.getHours()%12;return l===0&&(l=12),s.ordinalNumber(l,{unit:"hour"})}return Vn.h(n,r)},H:function(n,r,s){return r==="Ho"?s.ordinalNumber(n.getHours(),{unit:"hour"}):Vn.H(n,r)},K:function(n,r,s){const l=n.getHours()%12;return r==="Ko"?s.ordinalNumber(l,{unit:"hour"}):Re(l,r.length)},k:function(n,r,s){let l=n.getHours();return l===0&&(l=24),r==="ko"?s.ordinalNumber(l,{unit:"hour"}):Re(l,r.length)},m:function(n,r,s){return r==="mo"?s.ordinalNumber(n.getMinutes(),{unit:"minute"}):Vn.m(n,r)},s:function(n,r,s){return r==="so"?s.ordinalNumber(n.getSeconds(),{unit:"second"}):Vn.s(n,r)},S:function(n,r){return Vn.S(n,r)},X:function(n,r,s){const l=n.getTimezoneOffset();if(l===0)return"Z";switch(r){case"X":return hp(l);case"XXXX":case"XX":return xr(l);case"XXXXX":case"XXX":default:return xr(l,":")}},x:function(n,r,s){const l=n.getTimezoneOffset();switch(r){case"x":return hp(l);case"xxxx":case"xx":return xr(l);case"xxxxx":case"xxx":default:return xr(l,":")}},O:function(n,r,s){const l=n.getTimezoneOffset();switch(r){case"O":case"OO":case"OOO":return"GMT"+fp(l,":");case"OOOO":default:return"GMT"+xr(l,":")}},z:function(n,r,s){const l=n.getTimezoneOffset();switch(r){case"z":case"zz":case"zzz":return"GMT"+fp(l,":");case"zzzz":default:return"GMT"+xr(l,":")}},t:function(n,r,s){const l=Math.trunc(n.getTime()/1e3);return Re(l,r.length)},T:function(n,r,s){const l=n.getTime();return Re(l,r.length)}};function fp(n,r=""){const s=n>0?"-":"+",l=Math.abs(n),u=Math.trunc(l/60),d=l%60;return d===0?s+String(u):s+String(u)+r+Re(d,2)}function hp(n,r){return n%60===0?(n>0?"-":"+")+Re(Math.abs(n)/60,2):xr(n,r)}function xr(n,r=""){const s=n>0?"-":"+",l=Math.abs(n),u=Re(Math.trunc(l/60),2),d=Re(l%60,2);return s+u+r+d}const pp=(n,r)=>{switch(n){case"P":return r.date({width:"short"});case"PP":return r.date({width:"medium"});case"PPP":return r.date({width:"long"});case"PPPP":default:return r.date({width:"full"})}},kg=(n,r)=>{switch(n){case"p":return r.time({width:"short"});case"pp":return r.time({width:"medium"});case"ppp":return r.time({width:"long"});case"pppp":default:return r.time({width:"full"})}},tC=(n,r)=>{const s=n.match(/(P+)(p+)?/)||[],l=s[1],u=s[2];if(!u)return pp(n,r);let d;switch(l){case"P":d=r.dateTime({width:"short"});break;case"PP":d=r.dateTime({width:"medium"});break;case"PPP":d=r.dateTime({width:"long"});break;case"PPPP":default:d=r.dateTime({width:"full"});break}return d.replace("{{date}}",pp(l,r)).replace("{{time}}",kg(u,r))},nC={p:kg,P:tC},rC=/^D+$/,oC=/^Y+$/,sC=["D","DD","YY","YYYY"];function iC(n){return rC.test(n)}function lC(n){return oC.test(n)}function aC(n,r,s){const l=uC(n,r,s);if(console.warn(l),sC.includes(n))throw new RangeError(l)}function uC(n,r,s){const l=n[0]==="Y"?"years":"days of the month";return`Use \`${n.toLowerCase()}\` instead of \`${n}\` (in \`${r}\`) for formatting ${l} to the input \`${s}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const cC=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,dC=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,fC=/^'([^]*?)'?$/,hC=/''/g,pC=/[a-zA-Z]/;function mp(n,r,s){var x,v,E,N;const l=kl(),u=l.locale??GS,d=l.firstWeekContainsDate??((v=(x=l.locale)==null?void 0:x.options)==null?void 0:v.firstWeekContainsDate)??1,f=l.weekStartsOn??((N=(E=l.locale)==null?void 0:E.options)==null?void 0:N.weekStartsOn)??0,p=Qt(n);if(!gS(p))throw new RangeError("Invalid time value");let g=r.match(dC).map(j=>{const S=j[0];if(S==="p"||S==="P"){const k=nC[S];return k(j,u.formatLong)}return j}).join("").match(cC).map(j=>{if(j==="''")return{isToken:!1,value:"'"};const S=j[0];if(S==="'")return{isToken:!1,value:mC(j)};if(dp[S])return{isToken:!0,value:j};if(S.match(pC))throw new RangeError("Format string contains an unescaped latin alphabet character `"+S+"`");return{isToken:!1,value:j}});u.localize.preprocessor&&(g=u.localize.preprocessor(p,g));const y={firstWeekContainsDate:d,weekStartsOn:f,locale:u};return g.map(j=>{if(!j.isToken)return j.value;const S=j.value;(lC(S)||iC(S))&&aC(S,r,String(n));const k=dp[S[0]];return k(p,S,u.localize,y)}).join("")}function mC(n){const r=n.match(fC);return r?r[1].replace(hC,"'"):n}function gC(){var p,g,y,x,v,E;const{data:n,isLoading:r}=bu({queryKey:["/api/contacts"],queryFn:Ji({on401:"throw"})}),{data:s,isLoading:l}=bu({queryKey:["/api/projects"],queryFn:Ji({on401:"throw"})}),{data:u,isLoading:d}=bu({queryKey:["/api/skills"],queryFn:Ji({on401:"throw"})});return r||l||d?m.jsx("div",{className:"min-h-screen bg-gray-50 py-16",children:m.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:m.jsx("div",{className:"text-center",children:"Loading..."})})}):m.jsx("div",{className:"min-h-screen bg-gray-50 py-16",children:m.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[m.jsxs("div",{className:"mb-8",children:[m.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Portfolio Admin Dashboard"}),m.jsx("p",{className:"text-gray-600 mt-2",children:"Manage your portfolio data and view contact submissions"})]}),m.jsxs("div",{className:"grid gap-8",children:[m.jsxs(Es,{children:[m.jsx(nl,{children:m.jsxs(rl,{children:["Contact Messages (",((p=n==null?void 0:n.contacts)==null?void 0:p.length)||0,")"]})}),m.jsx(ks,{children:m.jsx("div",{className:"space-y-4",children:((g=n==null?void 0:n.contacts)==null?void 0:g.map(N=>m.jsxs("div",{className:"border rounded-lg p-4 bg-white",children:[m.jsxs("div",{className:"flex justify-between items-start mb-2",children:[m.jsxs("h3",{className:"font-semibold text-lg",children:[N.firstName," ",N.lastName]}),m.jsx("span",{className:"text-sm text-gray-500",children:mp(new Date(N.createdAt),"MMM dd, yyyy")})]}),m.jsx("p",{className:"text-gray-600 mb-2",children:N.email}),m.jsx("p",{className:"font-medium mb-2",children:N.subject}),m.jsx("p",{className:"text-gray-700",children:N.message})]},N.id)))||m.jsx("p",{className:"text-gray-500 text-center py-4",children:"No contact messages yet"})})})]}),m.jsxs(Es,{children:[m.jsx(nl,{children:m.jsxs(rl,{children:["Projects (",((y=s==null?void 0:s.projects)==null?void 0:y.length)||0,")"]})}),m.jsx(ks,{children:m.jsx("div",{className:"grid md:grid-cols-2 gap-4",children:((x=s==null?void 0:s.projects)==null?void 0:x.map(N=>m.jsxs("div",{className:"border rounded-lg p-4 bg-white",children:[m.jsxs("div",{className:"flex justify-between items-start mb-2",children:[m.jsx("h3",{className:"font-semibold text-lg",children:N.title}),N.featured&&m.jsx(Mu,{variant:"secondary",children:"Featured"})]}),m.jsx("p",{className:"text-gray-600 mb-3 text-sm",children:N.description}),m.jsx("div",{className:"flex flex-wrap gap-2",children:N.technologies.map((j,S)=>m.jsx(Mu,{variant:"outline",className:"text-xs",children:j},S))}),m.jsxs("p",{className:"text-xs text-gray-500 mt-2",children:["Created: ",mp(new Date(N.createdAt),"MMM dd, yyyy")]})]},N.id)))||m.jsx("p",{className:"text-gray-500 text-center py-4",children:"No projects yet"})})})]}),m.jsxs(Es,{children:[m.jsx(nl,{children:m.jsxs(rl,{children:["Skills (",((v=u==null?void 0:u.skills)==null?void 0:v.length)||0,")"]})}),m.jsx(ks,{children:m.jsx("div",{className:"grid sm:grid-cols-2 lg:grid-cols-3 gap-4",children:((E=u==null?void 0:u.skills)==null?void 0:E.map(N=>m.jsxs("div",{className:"border rounded-lg p-4 bg-white",children:[m.jsxs("div",{className:"flex justify-between items-center mb-2",children:[m.jsx("h3",{className:"font-semibold",children:N.name}),m.jsxs("span",{className:"text-sm text-gray-500",children:[N.level,"%"]})]}),m.jsx(Mu,{variant:"outline",className:"text-xs mb-2",children:N.category}),m.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:m.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${N.level}%`}})})]},N.id)))||m.jsx("p",{className:"text-gray-500 text-center py-4",children:"No skills yet"})})})]})]})]})})}function yC(){return m.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-gray-50",children:m.jsx(Es,{className:"w-full max-w-md mx-4",children:m.jsxs(ks,{className:"pt-6",children:[m.jsxs("div",{className:"flex mb-4 gap-2",children:[m.jsx(A0,{className:"h-8 w-8 text-red-500"}),m.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"404 Page Not Found"})]}),m.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Did you forget to add the page to the router?"})]})})})}function vC(){return m.jsxs(Zv,{children:[m.jsx(Eu,{path:"/",component:lS}),m.jsx(Eu,{path:"/admin",component:gC}),m.jsx(Eu,{component:yC})]})}function xC(){return m.jsx(xx,{client:_x,children:m.jsxs(X1,{children:[m.jsx(bw,{}),m.jsx(vC,{})]})})}Rv.createRoot(document.getElementById("root")).render(m.jsx(xC,{}));
