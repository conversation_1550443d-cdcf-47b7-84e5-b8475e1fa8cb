"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var $t=require("source-map-support"),Zt=require("url"),ne=require("esbuild"),Wt=require("crypto"),Vt=require("fs"),zt=require("path"),eA=require("os");function T(i){return i&&typeof i=="object"&&"default"in i?i:{default:i}}var tA=T($t),AA=T(Wt),_=T(Vt),G=T(zt),nA=T(eA);const we=process.versions.node.split(".").map(Number),xe=i=>we[0]-i[0]||we[1]-i[1]||we[2]-i[2],Ue=xe([20,0,0])>=0,rA=`
//# sourceMappingURL=data:application/json;base64,`;function iA(i){if("setSourceMapsEnabled"in process&&typeof Error.prepareStackTrace!="function")return process.setSourceMapsEnabled(!0),({code:r,map:s})=>r+rA+Buffer.from(JSON.stringify(s),"utf8").toString("base64");const A=new Map;return tA.default.install({environment:"node",retrieveSourceMap(r){const s=A.get(r);return s?{url:r,map:s}:null}}),Ue&&i&&i.addListener("message",({filePath:r,map:s})=>A.set(r,s)),({code:r,map:s},a,c)=>(Ue&&c?c.postMessage({filePath:a,map:s}):A.set(a,s),r)}const qe=i=>AA.default.createHash("sha1").update(i).digest("hex"),Me=",".charCodeAt(0),sA=";".charCodeAt(0),Ge="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ye=new Uint8Array(64),_e=new Uint8Array(128);for(let i=0;i<Ge.length;i++){const e=Ge.charCodeAt(i);Ye[i]=e,_e[e]=i}const ke=typeof TextDecoder<"u"?new TextDecoder:typeof Buffer<"u"?{decode(i){return Buffer.from(i.buffer,i.byteOffset,i.byteLength).toString()}}:{decode(i){let e="";for(let A=0;A<i.length;A++)e+=String.fromCharCode(i[A]);return e}};function oA(i){const e=new Int32Array(5),A=[];let r=0;do{const s=aA(i,r),a=[];let c=!0,C=0;e[0]=0;for(let Q=r;Q<s;Q++){let g;Q=$(i,Q,e,0);const l=e[0];l<C&&(c=!1),C=l,Oe(i,Q,s)?(Q=$(i,Q,e,1),Q=$(i,Q,e,2),Q=$(i,Q,e,3),Oe(i,Q,s)?(Q=$(i,Q,e,4),g=[l,e[1],e[2],e[3],e[4]]):g=[l,e[1],e[2],e[3]]):g=[l],a.push(g)}c||cA(a),A.push(a),r=s+1}while(r<=i.length);return A}function aA(i,e){const A=i.indexOf(";",e);return A===-1?i.length:A}function $(i,e,A,r){let s=0,a=0,c=0;do{const Q=i.charCodeAt(e++);c=_e[Q],s|=(c&31)<<a,a+=5}while(c&32);const C=s&1;return s>>>=1,C&&(s=-2147483648|-s),A[r]+=s,e}function Oe(i,e,A){return e>=A?!1:i.charCodeAt(e)!==Me}function cA(i){i.sort(uA)}function uA(i,e){return i[0]-e[0]}function He(i){const e=new Int32Array(5),A=1024*16,r=A-36,s=new Uint8Array(A),a=s.subarray(0,r);let c=0,C="";for(let Q=0;Q<i.length;Q++){const g=i[Q];if(Q>0&&(c===A&&(C+=ke.decode(s),c=0),s[c++]=sA),g.length!==0){e[0]=0;for(let l=0;l<g.length;l++){const n=g[l];c>r&&(C+=ke.decode(a),s.copyWithin(0,r,c),c-=r),l>0&&(s[c++]=Me),c=Z(s,c,e,n,0),n.length!==1&&(c=Z(s,c,e,n,1),c=Z(s,c,e,n,2),c=Z(s,c,e,n,3),n.length!==4&&(c=Z(s,c,e,n,4)))}}}return C+ke.decode(s.subarray(0,c))}function Z(i,e,A,r,s){const a=r[s];let c=a-A[s];A[s]=a,c=c<0?-c<<1|1:c<<1;do{let C=c&31;c>>>=5,c>0&&(C|=32),i[e++]=Ye[C]}while(c>0);return e}class ue{constructor(e){this.bits=e instanceof ue?e.bits.slice():[]}add(e){this.bits[e>>5]|=1<<(e&31)}has(e){return!!(this.bits[e>>5]&1<<(e&31))}}class z{constructor(e,A,r){this.start=e,this.end=A,this.original=r,this.intro="",this.outro="",this.content=r,this.storeName=!1,this.edited=!1,this.previous=null,this.next=null}appendLeft(e){this.outro+=e}appendRight(e){this.intro=this.intro+e}clone(){const e=new z(this.start,this.end,this.original);return e.intro=this.intro,e.outro=this.outro,e.content=this.content,e.storeName=this.storeName,e.edited=this.edited,e}contains(e){return this.start<e&&e<this.end}eachNext(e){let A=this;for(;A;)e(A),A=A.next}eachPrevious(e){let A=this;for(;A;)e(A),A=A.previous}edit(e,A,r){return this.content=e,r||(this.intro="",this.outro=""),this.storeName=A,this.edited=!0,this}prependLeft(e){this.outro=e+this.outro}prependRight(e){this.intro=e+this.intro}split(e){const A=e-this.start,r=this.original.slice(0,A),s=this.original.slice(A);this.original=r;const a=new z(e,this.end,s);return a.outro=this.outro,this.outro="",this.end=e,this.edited?(a.edit("",!1),this.content=""):this.content=r,a.next=this.next,a.next&&(a.next.previous=a),a.previous=this,this.next=a,a}toString(){return this.intro+this.content+this.outro}trimEnd(e){if(this.outro=this.outro.replace(e,""),this.outro.length)return!0;const A=this.content.replace(e,"");if(A.length)return A!==this.content&&(this.split(this.start+A.length).edit("",void 0,!0),this.edited&&this.edit(A,this.storeName,!0)),!0;if(this.edit("",void 0,!0),this.intro=this.intro.replace(e,""),this.intro.length)return!0}trimStart(e){if(this.intro=this.intro.replace(e,""),this.intro.length)return!0;const A=this.content.replace(e,"");if(A.length){if(A!==this.content){const r=this.split(this.end-A.length);this.edited&&r.edit(A,this.storeName,!0),this.edit("",void 0,!0)}return!0}else if(this.edit("",void 0,!0),this.outro=this.outro.replace(e,""),this.outro.length)return!0}}function lA(){return typeof window<"u"&&typeof window.btoa=="function"?i=>window.btoa(unescape(encodeURIComponent(i))):typeof Buffer=="function"?i=>Buffer.from(i,"utf-8").toString("base64"):()=>{throw new Error("Unsupported environment: `window.btoa` or `Buffer` should be supported.")}}const QA=lA();class fA{constructor(e){this.version=3,this.file=e.file,this.sources=e.sources,this.sourcesContent=e.sourcesContent,this.names=e.names,this.mappings=He(e.mappings),typeof e.x_google_ignoreList<"u"&&(this.x_google_ignoreList=e.x_google_ignoreList)}toString(){return JSON.stringify(this)}toUrl(){return"data:application/json;charset=utf-8;base64,"+QA(this.toString())}}function gA(i){const e=i.split(`
`),A=e.filter(a=>/^\t+/.test(a)),r=e.filter(a=>/^ {2,}/.test(a));if(A.length===0&&r.length===0)return null;if(A.length>=r.length)return"	";const s=r.reduce((a,c)=>{const C=/^ +/.exec(c)[0].length;return Math.min(C,a)},1/0);return new Array(s+1).join(" ")}function hA(i,e){const A=i.split(/[/\\]/),r=e.split(/[/\\]/);for(A.pop();A[0]===r[0];)A.shift(),r.shift();if(A.length){let s=A.length;for(;s--;)A[s]=".."}return A.concat(r).join("/")}const CA=Object.prototype.toString;function BA(i){return CA.call(i)==="[object Object]"}function je(i){const e=i.split(`
`),A=[];for(let r=0,s=0;r<e.length;r++)A.push(s),s+=e[r].length+1;return function(s){let a=0,c=A.length;for(;a<c;){const g=a+c>>1;s<A[g]?c=g:a=g+1}const C=a-1,Q=s-A[C];return{line:C,column:Q}}}const EA=/\w/;class dA{constructor(e){this.hires=e,this.generatedCodeLine=0,this.generatedCodeColumn=0,this.raw=[],this.rawSegments=this.raw[this.generatedCodeLine]=[],this.pending=null}addEdit(e,A,r,s){if(A.length){const a=[this.generatedCodeColumn,e,r.line,r.column];s>=0&&a.push(s),this.rawSegments.push(a)}else this.pending&&this.rawSegments.push(this.pending);this.advance(A),this.pending=null}addUneditedChunk(e,A,r,s,a){let c=A.start,C=!0,Q=!1;for(;c<A.end;){if(this.hires||C||a.has(c)){const g=[this.generatedCodeColumn,e,s.line,s.column];this.hires==="boundary"?EA.test(r[c])?Q||(this.rawSegments.push(g),Q=!0):(this.rawSegments.push(g),Q=!1):this.rawSegments.push(g)}r[c]===`
`?(s.line+=1,s.column=0,this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,C=!0):(s.column+=1,this.generatedCodeColumn+=1,C=!1),c+=1}this.pending=null}advance(e){if(!e)return;const A=e.split(`
`);if(A.length>1){for(let r=0;r<A.length-1;r++)this.generatedCodeLine++,this.raw[this.generatedCodeLine]=this.rawSegments=[];this.generatedCodeColumn=0}this.generatedCodeColumn+=A[A.length-1].length}}const W=`
`,O={insertLeft:!1,insertRight:!1,storeName:!1};class Se{constructor(e,A={}){const r=new z(0,e.length,e);Object.defineProperties(this,{original:{writable:!0,value:e},outro:{writable:!0,value:""},intro:{writable:!0,value:""},firstChunk:{writable:!0,value:r},lastChunk:{writable:!0,value:r},lastSearchedChunk:{writable:!0,value:r},byStart:{writable:!0,value:{}},byEnd:{writable:!0,value:{}},filename:{writable:!0,value:A.filename},indentExclusionRanges:{writable:!0,value:A.indentExclusionRanges},sourcemapLocations:{writable:!0,value:new ue},storedNames:{writable:!0,value:{}},indentStr:{writable:!0,value:void 0},ignoreList:{writable:!0,value:A.ignoreList}}),this.byStart[0]=r,this.byEnd[e.length]=r}addSourcemapLocation(e){this.sourcemapLocations.add(e)}append(e){if(typeof e!="string")throw new TypeError("outro content must be a string");return this.outro+=e,this}appendLeft(e,A){if(typeof A!="string")throw new TypeError("inserted content must be a string");this._split(e);const r=this.byEnd[e];return r?r.appendLeft(A):this.intro+=A,this}appendRight(e,A){if(typeof A!="string")throw new TypeError("inserted content must be a string");this._split(e);const r=this.byStart[e];return r?r.appendRight(A):this.outro+=A,this}clone(){const e=new Se(this.original,{filename:this.filename});let A=this.firstChunk,r=e.firstChunk=e.lastSearchedChunk=A.clone();for(;A;){e.byStart[r.start]=r,e.byEnd[r.end]=r;const s=A.next,a=s&&s.clone();a&&(r.next=a,a.previous=r,r=a),A=s}return e.lastChunk=r,this.indentExclusionRanges&&(e.indentExclusionRanges=this.indentExclusionRanges.slice()),e.sourcemapLocations=new ue(this.sourcemapLocations),e.intro=this.intro,e.outro=this.outro,e}generateDecodedMap(e){e=e||{};const A=0,r=Object.keys(this.storedNames),s=new dA(e.hires),a=je(this.original);return this.intro&&s.advance(this.intro),this.firstChunk.eachNext(c=>{const C=a(c.start);c.intro.length&&s.advance(c.intro),c.edited?s.addEdit(A,c.content,C,c.storeName?r.indexOf(c.original):-1):s.addUneditedChunk(A,c,this.original,C,this.sourcemapLocations),c.outro.length&&s.advance(c.outro)}),{file:e.file?e.file.split(/[/\\]/).pop():void 0,sources:[e.source?hA(e.file||"",e.source):e.file||""],sourcesContent:e.includeContent?[this.original]:void 0,names:r,mappings:s.raw,x_google_ignoreList:this.ignoreList?[A]:void 0}}generateMap(e){return new fA(this.generateDecodedMap(e))}_ensureindentStr(){this.indentStr===void 0&&(this.indentStr=gA(this.original))}_getRawIndentString(){return this._ensureindentStr(),this.indentStr}getIndentString(){return this._ensureindentStr(),this.indentStr===null?"	":this.indentStr}indent(e,A){const r=/^[^\r\n]/gm;if(BA(e)&&(A=e,e=void 0),e===void 0&&(this._ensureindentStr(),e=this.indentStr||"	"),e==="")return this;A=A||{};const s={};A.exclude&&(typeof A.exclude[0]=="number"?[A.exclude]:A.exclude).forEach(l=>{for(let n=l[0];n<l[1];n+=1)s[n]=!0});let a=A.indentStart!==!1;const c=g=>a?`${e}${g}`:(a=!0,g);this.intro=this.intro.replace(r,c);let C=0,Q=this.firstChunk;for(;Q;){const g=Q.end;if(Q.edited)s[C]||(Q.content=Q.content.replace(r,c),Q.content.length&&(a=Q.content[Q.content.length-1]===`
`));else for(C=Q.start;C<g;){if(!s[C]){const l=this.original[C];l===`
`?a=!0:l!=="\r"&&a&&(a=!1,C===Q.start||(this._splitChunk(Q,C),Q=Q.next),Q.prependRight(e))}C+=1}C=Q.end,Q=Q.next}return this.outro=this.outro.replace(r,c),this}insert(){throw new Error("magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)")}insertLeft(e,A){return O.insertLeft||(console.warn("magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead"),O.insertLeft=!0),this.appendLeft(e,A)}insertRight(e,A){return O.insertRight||(console.warn("magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead"),O.insertRight=!0),this.prependRight(e,A)}move(e,A,r){if(r>=e&&r<=A)throw new Error("Cannot move a selection inside itself");this._split(e),this._split(A),this._split(r);const s=this.byStart[e],a=this.byEnd[A],c=s.previous,C=a.next,Q=this.byStart[r];if(!Q&&a===this.lastChunk)return this;const g=Q?Q.previous:this.lastChunk;return c&&(c.next=C),C&&(C.previous=c),g&&(g.next=s),Q&&(Q.previous=a),s.previous||(this.firstChunk=a.next),a.next||(this.lastChunk=s.previous,this.lastChunk.next=null),s.previous=g,a.next=Q||null,g||(this.firstChunk=s),Q||(this.lastChunk=a),this}overwrite(e,A,r,s){return s=s||{},this.update(e,A,r,{...s,overwrite:!s.contentOnly})}update(e,A,r,s){if(typeof r!="string")throw new TypeError("replacement content must be a string");for(;e<0;)e+=this.original.length;for(;A<0;)A+=this.original.length;if(A>this.original.length)throw new Error("end is out of bounds");if(e===A)throw new Error("Cannot overwrite a zero-length range \u2013 use appendLeft or prependRight instead");this._split(e),this._split(A),s===!0&&(O.storeName||(console.warn("The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string"),O.storeName=!0),s={storeName:!0});const a=s!==void 0?s.storeName:!1,c=s!==void 0?s.overwrite:!1;if(a){const g=this.original.slice(e,A);Object.defineProperty(this.storedNames,g,{writable:!0,value:!0,enumerable:!0})}const C=this.byStart[e],Q=this.byEnd[A];if(C){let g=C;for(;g!==Q;){if(g.next!==this.byStart[g.end])throw new Error("Cannot overwrite across a split point");g=g.next,g.edit("",!1)}C.edit(r,a,!c)}else{const g=new z(e,A,"").edit(r,a);Q.next=g,g.previous=Q}return this}prepend(e){if(typeof e!="string")throw new TypeError("outro content must be a string");return this.intro=e+this.intro,this}prependLeft(e,A){if(typeof A!="string")throw new TypeError("inserted content must be a string");this._split(e);const r=this.byEnd[e];return r?r.prependLeft(A):this.intro=A+this.intro,this}prependRight(e,A){if(typeof A!="string")throw new TypeError("inserted content must be a string");this._split(e);const r=this.byStart[e];return r?r.prependRight(A):this.outro=A+this.outro,this}remove(e,A){for(;e<0;)e+=this.original.length;for(;A<0;)A+=this.original.length;if(e===A)return this;if(e<0||A>this.original.length)throw new Error("Character is out of bounds");if(e>A)throw new Error("end must be greater than start");this._split(e),this._split(A);let r=this.byStart[e];for(;r;)r.intro="",r.outro="",r.edit(""),r=A>r.end?this.byStart[r.end]:null;return this}lastChar(){if(this.outro.length)return this.outro[this.outro.length-1];let e=this.lastChunk;do{if(e.outro.length)return e.outro[e.outro.length-1];if(e.content.length)return e.content[e.content.length-1];if(e.intro.length)return e.intro[e.intro.length-1]}while(e=e.previous);return this.intro.length?this.intro[this.intro.length-1]:""}lastLine(){let e=this.outro.lastIndexOf(W);if(e!==-1)return this.outro.substr(e+1);let A=this.outro,r=this.lastChunk;do{if(r.outro.length>0){if(e=r.outro.lastIndexOf(W),e!==-1)return r.outro.substr(e+1)+A;A=r.outro+A}if(r.content.length>0){if(e=r.content.lastIndexOf(W),e!==-1)return r.content.substr(e+1)+A;A=r.content+A}if(r.intro.length>0){if(e=r.intro.lastIndexOf(W),e!==-1)return r.intro.substr(e+1)+A;A=r.intro+A}}while(r=r.previous);return e=this.intro.lastIndexOf(W),e!==-1?this.intro.substr(e+1)+A:this.intro+A}slice(e=0,A=this.original.length){for(;e<0;)e+=this.original.length;for(;A<0;)A+=this.original.length;let r="",s=this.firstChunk;for(;s&&(s.start>e||s.end<=e);){if(s.start<A&&s.end>=A)return r;s=s.next}if(s&&s.edited&&s.start!==e)throw new Error(`Cannot use replaced character ${e} as slice start anchor.`);const a=s;for(;s;){s.intro&&(a!==s||s.start===e)&&(r+=s.intro);const c=s.start<A&&s.end>=A;if(c&&s.edited&&s.end!==A)throw new Error(`Cannot use replaced character ${A} as slice end anchor.`);const C=a===s?e-s.start:0,Q=c?s.content.length+A-s.end:s.content.length;if(r+=s.content.slice(C,Q),s.outro&&(!c||s.end===A)&&(r+=s.outro),c)break;s=s.next}return r}snip(e,A){const r=this.clone();return r.remove(0,e),r.remove(A,r.original.length),r}_split(e){if(this.byStart[e]||this.byEnd[e])return;let A=this.lastSearchedChunk;const r=e>A.end;for(;A;){if(A.contains(e))return this._splitChunk(A,e);A=r?this.byStart[A.end]:this.byEnd[A.start]}}_splitChunk(e,A){if(e.edited&&e.content.length){const s=je(this.original)(A);throw new Error(`Cannot split a chunk that has already been edited (${s.line}:${s.column} \u2013 "${e.original}")`)}const r=e.split(A);return this.byEnd[A]=e,this.byStart[A]=r,this.byEnd[r.end]=r,e===this.lastChunk&&(this.lastChunk=r),this.lastSearchedChunk=e,!0}toString(){let e=this.intro,A=this.firstChunk;for(;A;)e+=A.toString(),A=A.next;return e+this.outro}isEmpty(){let e=this.firstChunk;do if(e.intro.length&&e.intro.trim()||e.content.length&&e.content.trim()||e.outro.length&&e.outro.trim())return!1;while(e=e.next);return!0}length(){let e=this.firstChunk,A=0;do A+=e.intro.length+e.content.length+e.outro.length;while(e=e.next);return A}trimLines(){return this.trim("[\\r\\n]")}trim(e){return this.trimStart(e).trimEnd(e)}trimEndAborted(e){const A=new RegExp((e||"\\s")+"+$");if(this.outro=this.outro.replace(A,""),this.outro.length)return!0;let r=this.lastChunk;do{const s=r.end,a=r.trimEnd(A);if(r.end!==s&&(this.lastChunk===r&&(this.lastChunk=r.next),this.byEnd[r.end]=r,this.byStart[r.next.start]=r.next,this.byEnd[r.next.end]=r.next),a)return!0;r=r.previous}while(r);return!1}trimEnd(e){return this.trimEndAborted(e),this}trimStartAborted(e){const A=new RegExp("^"+(e||"\\s")+"+");if(this.intro=this.intro.replace(A,""),this.intro.length)return!0;let r=this.firstChunk;do{const s=r.end,a=r.trimStart(A);if(r.end!==s&&(r===this.lastChunk&&(this.lastChunk=r.next),this.byEnd[r.end]=r,this.byStart[r.next.start]=r.next,this.byEnd[r.next.end]=r.next),a)return!0;r=r.next}while(r);return!1}trimStart(e){return this.trimStartAborted(e),this}hasChanged(){return this.original!==this.toString()}_replaceRegexp(e,A){function r(a,c){return typeof A=="string"?A.replace(/\$(\$|&|\d+)/g,(C,Q)=>Q==="$"?"$":Q==="&"?a[0]:+Q<a.length?a[+Q]:`$${Q}`):A(...a,a.index,c,a.groups)}function s(a,c){let C;const Q=[];for(;C=a.exec(c);)Q.push(C);return Q}if(e.global)s(e,this.original).forEach(c=>{c.index!=null&&this.overwrite(c.index,c.index+c[0].length,r(c,this.original))});else{const a=this.original.match(e);a&&a.index!=null&&this.overwrite(a.index,a.index+a[0].length,r(a,this.original))}return this}_replaceString(e,A){const{original:r}=this,s=r.indexOf(e);return s!==-1&&this.overwrite(s,s+e.length,A),this}replace(e,A){return typeof e=="string"?this._replaceString(e,A):this._replaceRegexp(e,A)}_replaceAllString(e,A){const{original:r}=this,s=e.length;for(let a=r.indexOf(e);a!==-1;a=r.indexOf(e,a+s))this.overwrite(a,a+s,A);return this}replaceAll(e,A){if(typeof e=="string")return this._replaceAllString(e,A);if(!e.global)throw new TypeError("MagicString.prototype.replaceAll called with a non-global RegExp argument");return this._replaceRegexp(e,A)}}const wA=new Uint8Array(new Uint16Array([1]).buffer)[0]===1;function Xe(i,e="@"){if(!w)return Pe.then(()=>Xe(i));const A=i.length+1,r=(w.__heap_base.value||w.__heap_base)+4*A-w.memory.buffer.byteLength;r>0&&w.memory.grow(Math.ceil(r/65536));const s=w.sa(A-1);if((wA?IA:kA)(i,new Uint16Array(w.memory.buffer,s,A)),!w.parse())throw Object.assign(new Error(`Parse error ${e}:${i.slice(0,w.e()).split(`
`).length}:${w.e()-i.lastIndexOf(`
`,w.e()-1)}`),{idx:w.e()});const a=[],c=[];for(;w.ri();){const Q=w.is(),g=w.ie(),l=w.ai(),n=w.id(),p=w.ss(),y=w.se();let k;w.ip()&&(k=C(i.slice(n===-1?Q-1:Q,n===-1?g+1:g))),a.push({n:k,s:Q,e:g,ss:p,se:y,d:n,a:l})}for(;w.re();){const Q=w.es(),g=w.ee(),l=w.els(),n=w.ele(),p=i.slice(Q,g),y=p[0],k=l<0?void 0:i.slice(l,n),N=k?k[0]:"";c.push({s:Q,e:g,ls:l,le:n,n:y==='"'||y==="'"?C(p):p,ln:N==='"'||N==="'"?C(k):k})}function C(Q){try{return(0,eval)(Q)}catch{}}return[a,c,!!w.f()]}function kA(i,e){const A=i.length;let r=0;for(;r<A;){const s=i.charCodeAt(r);e[r++]=(255&s)<<8|s>>>8}}function IA(i,e){const A=i.length;let r=0;for(;r<A;)e[r]=i.charCodeAt(r++)}let w;const Pe=WebAssembly.compile((Ie="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",typeof Buffer<"u"?Buffer.from(Ie,"base64"):Uint8Array.from(atob(Ie),i=>i.charCodeAt(0)))).then(WebAssembly.instantiate).then(({exports:i})=>{w=i});var Ie;let D,re,pe,V=2<<19;const Te=new Uint8Array(new Uint16Array([1]).buffer)[0]===1?function(i,e){const A=i.length;let r=0;for(;r<A;)e[r]=i.charCodeAt(r++)}:function(i,e){const A=i.length;let r=0;for(;r<A;){const s=i.charCodeAt(r);e[r++]=(255&s)<<8|s>>>8}},pA="xportmportlassetaromsyncunctionssertvoyiedelecontininstantybreareturdebuggeawaithrwhileforifcatcfinallels";let b,$e,d;function bA(i,e="@"){b=i,$e=e;const A=2*b.length+(2<<18);if(A>V||!D){for(;A>V;)V*=2;re=new ArrayBuffer(V),Te(pA,new Uint16Array(re,16,105)),D=function(c,C,Q){var g=new c.Int8Array(Q),l=new c.Int16Array(Q),n=new c.Int32Array(Q),p=new c.Uint8Array(Q),y=new c.Uint16Array(Q),k=1024;function N(){var t=0,o=0,u=0,h=0,B=0,f=0,x=0;x=k,k=k+10240|0,g[795]=1,l[395]=0,l[396]=0,n[67]=n[2],g[796]=0,n[66]=0,g[794]=0,n[68]=x+2048,n[69]=x,g[797]=0,t=(n[3]|0)+-2|0,n[70]=t,o=t+(n[64]<<1)|0,n[71]=o;e:for(;;){if(u=t+2|0,n[70]=u,t>>>0>=o>>>0){f=18;break}A:do switch(l[u>>1]|0){case 9:case 10:case 11:case 12:case 13:case 32:break;case 101:{if(!(l[396]|0)&&j(u)|0&&!(J(t+4|0,16,10)|0)&&(v(),(g[795]|0)==0)){f=9;break e}else f=17;break}case 105:{j(u)|0&&!(J(t+4|0,26,10)|0)&&ee(),f=17;break}case 59:{f=17;break}case 47:switch(l[t+4>>1]|0){case 47:{he();break A}case 42:{fe(1);break A}default:{f=16;break e}}default:{f=16;break e}}while(0);(f|0)==17&&(f=0,n[67]=n[70]),t=n[70]|0,o=n[71]|0}(f|0)==9?(t=n[70]|0,n[67]=t,f=19):(f|0)==16?(g[795]=0,n[70]=t,f=19):(f|0)==18&&(g[794]|0?t=0:(t=u,f=19));do if((f|0)==19){e:for(;;){if(o=t+2|0,n[70]=o,B=o,t>>>0>=(n[71]|0)>>>0){f=82;break}A:do switch(l[o>>1]|0){case 9:case 10:case 11:case 12:case 13:case 32:break;case 101:{!(l[396]|0)&&j(o)|0&&!(J(t+4|0,16,10)|0)&&v(),f=81;break}case 105:{j(o)|0&&!(J(t+4|0,26,10)|0)&&ee(),f=81;break}case 99:{j(o)|0&&!(J(t+4|0,36,8)|0)&&X(l[t+12>>1]|0)|0&&(g[797]=1),f=81;break}case 40:{B=n[68]|0,u=l[396]|0,f=u&65535,n[B+(f<<3)>>2]=1,h=n[67]|0,l[396]=u+1<<16>>16,n[B+(f<<3)+4>>2]=h,f=81;break}case 41:{if(o=l[396]|0,!(o<<16>>16)){f=36;break e}f=o+-1<<16>>16,l[396]=f,h=l[395]|0,o=h&65535,h<<16>>16&&(n[(n[68]|0)+((f&65535)<<3)>>2]|0)==5&&(o=n[(n[69]|0)+(o+-1<<2)>>2]|0,u=o+4|0,n[u>>2]|0||(n[u>>2]=B),n[o+12>>2]=t+4,l[395]=h+-1<<16>>16),f=81;break}case 123:{f=n[67]|0,B=n[61]|0,t=f;do if((l[f>>1]|0)==41&(B|0)!=0&&(n[B+4>>2]|0)==(f|0))if(o=n[62]|0,n[61]=o,o){n[o+28>>2]=0;break}else{n[57]=0;break}while(0);B=n[68]|0,h=l[396]|0,f=h&65535,n[B+(f<<3)>>2]=g[797]|0?6:2,l[396]=h+1<<16>>16,n[B+(f<<3)+4>>2]=t,g[797]=0,f=81;break}case 125:{if(t=l[396]|0,!(t<<16>>16)){f=49;break e}B=n[68]|0,f=t+-1<<16>>16,l[396]=f,(n[B+((f&65535)<<3)>>2]|0)==4&&Je(),f=81;break}case 39:{L(39),f=81;break}case 34:{L(34),f=81;break}case 47:switch(l[t+4>>1]|0){case 47:{he();break A}case 42:{fe(1);break A}default:{t=n[67]|0,h=l[t>>1]|0;t:do if(yt(h)|0)switch(h<<16>>16){case 46:if(((l[t+-2>>1]|0)+-48&65535)<10){f=66;break t}else{f=69;break t}case 43:if((l[t+-2>>1]|0)==43){f=66;break t}else{f=69;break t}case 45:if((l[t+-2>>1]|0)==45){f=66;break t}else{f=69;break t}default:{f=69;break t}}else{switch(h<<16>>16){case 41:if(Lt(n[(n[68]|0)+(y[396]<<3)+4>>2]|0)|0){f=69;break t}else{f=66;break t}case 125:break;default:{f=66;break t}}o=n[68]|0,u=y[396]|0,!(Kt(n[o+(u<<3)+4>>2]|0)|0)&&(n[o+(u<<3)>>2]|0)!=6?f=66:f=69}while(0);t:do if((f|0)==66)if(f=0,mt(t)|0)f=69;else{switch(h<<16>>16){case 0:{f=69;break t}case 47:{if(g[796]|0){f=69;break t}break}default:}u=n[3]|0,o=h;do{if(t>>>0<=u>>>0)break;t=t+-2|0,n[67]=t,o=l[t>>1]|0}while(!(ge(o)|0));if(Ae(o)|0){do{if(t>>>0<=u>>>0)break;t=t+-2|0,n[67]=t}while(Ae(l[t>>1]|0)|0);if(Jt(t)|0){Le(),g[796]=0,f=81;break A}else t=1}else t=1}while(0);(f|0)==69&&(Le(),t=0),g[796]=t,f=81;break A}}case 96:{B=n[68]|0,h=l[396]|0,f=h&65535,n[B+(f<<3)+4>>2]=n[67],l[396]=h+1<<16>>16,n[B+(f<<3)>>2]=3,Je(),f=81;break}default:f=81}while(0);(f|0)==81&&(f=0,n[67]=n[70]),t=n[70]|0}if((f|0)==36){R(),t=0;break}else if((f|0)==49){R(),t=0;break}else if((f|0)==82){t=g[794]|0?0:(l[395]|l[396])<<16>>16==0;break}}while(0);return k=x,t|0}function v(){var t=0,o=0,u=0,h=0,B=0,f=0,x=0,M=0,Ce=0,Be=0,Ee=0,de=0,I=0,m=0;M=n[70]|0,Ce=n[63]|0,m=M+12|0,n[70]=m,u=E(1)|0,t=n[70]|0,(t|0)==(m|0)&&!(te(u)|0)||(I=3);e:do if((I|0)==3){A:do switch(u<<16>>16){case 123:{for(n[70]=t+2,t=E(1)|0,u=n[70]|0;;){if(P(t)|0?(L(t),t=(n[70]|0)+2|0,n[70]=t):(F(t)|0,t=n[70]|0),E(1)|0,t=Ne(u,t)|0,t<<16>>16==44&&(n[70]=(n[70]|0)+2,t=E(1)|0),o=u,u=n[70]|0,t<<16>>16==125){I=15;break}if((u|0)==(o|0)){I=12;break}if(u>>>0>(n[71]|0)>>>0){I=14;break}}if((I|0)==12){R();break e}else if((I|0)==14){R();break e}else if((I|0)==15){n[70]=u+2;break A}break}case 42:{n[70]=t+2,E(1)|0,m=n[70]|0,Ne(m,m)|0;break}default:{switch(g[795]=0,u<<16>>16){case 100:{switch(M=t+14|0,n[70]=M,(E(1)|0)<<16>>16){case 97:{o=n[70]|0,!(J(o+2|0,56,8)|0)&&(B=o+10|0,Ae(l[B>>1]|0)|0)&&(n[70]=B,E(0)|0,I=22);break}case 102:{I=22;break}case 99:{o=n[70]|0,!(J(o+2|0,36,8)|0)&&(h=o+10|0,m=l[h>>1]|0,X(m)|0|m<<16>>16==123)&&(n[70]=h,f=E(1)|0,f<<16>>16!=123)&&(de=f,I=31);break}default:}t:do if((I|0)==22&&(x=n[70]|0,(J(x+2|0,64,14)|0)==0)){if(u=x+16|0,o=l[u>>1]|0,!(X(o)|0))switch(o<<16>>16){case 40:case 42:break;default:break t}n[70]=u,o=E(1)|0,o<<16>>16==42&&(n[70]=(n[70]|0)+2,o=E(1)|0),o<<16>>16!=40&&(de=o,I=31)}while(0);if((I|0)==31&&(Be=n[70]|0,F(de)|0,Ee=n[70]|0,Ee>>>0>Be>>>0)){Y(t,M,Be,Ee),n[70]=(n[70]|0)+-2;break e}Y(t,M,0,0),n[70]=t+12;break e}case 97:{n[70]=t+10,E(0)|0,t=n[70]|0,I=35;break}case 102:{I=35;break}case 99:{if(!(J(t+2|0,36,8)|0)&&(o=t+10|0,ge(l[o>>1]|0)|0)){n[70]=o,m=E(1)|0,I=n[70]|0,F(m)|0,m=n[70]|0,Y(I,m,I,m),n[70]=(n[70]|0)+-2;break e}t=t+4|0,n[70]=t;break}case 108:case 118:break;default:break e}if((I|0)==35){n[70]=t+16,t=E(1)|0,t<<16>>16==42&&(n[70]=(n[70]|0)+2,t=E(1)|0),I=n[70]|0,F(t)|0,m=n[70]|0,Y(I,m,I,m),n[70]=(n[70]|0)+-2;break e}n[70]=t+6,g[795]=0,u=E(1)|0,t=n[70]|0,u=(F(u)|0|32)<<16>>16==123,h=n[70]|0,u&&(n[70]=h+2,m=E(1)|0,t=n[70]|0,F(m)|0);t:for(;o=n[70]|0,(o|0)!=(t|0);){if(Y(t,o,t,o),o=E(1)|0,u)switch(o<<16>>16){case 93:case 125:break e;default:}if(t=n[70]|0,o<<16>>16!=44){I=51;break}switch(n[70]=t+2,o=E(1)|0,t=n[70]|0,o<<16>>16){case 91:case 123:{I=51;break t}default:}F(o)|0}if((I|0)==51&&(n[70]=t+-2),!u)break e;n[70]=h+-2;break e}}while(0);if(m=(E(1)|0)<<16>>16==102,t=n[70]|0,m&&!(J(t+2|0,50,6)|0))for(n[70]=t+8,le(M,E(1)|0),t=Ce|0?Ce+16|0:232;;){if(t=n[t>>2]|0,!t)break e;n[t+12>>2]=0,n[t+8>>2]=0,t=t+16|0}n[70]=t+-2}while(0)}function ee(){var t=0,o=0,u=0,h=0,B=0,f=0;B=n[70]|0,t=B+12|0,n[70]=t;e:do switch((E(1)|0)<<16>>16){case 40:{if(o=n[68]|0,f=l[396]|0,u=f&65535,n[o+(u<<3)>>2]=5,t=n[70]|0,l[396]=f+1<<16>>16,n[o+(u<<3)+4>>2]=t,(l[n[67]>>1]|0)!=46){switch(n[70]=t+2,f=E(1)|0,Qe(B,n[70]|0,0,t),o=n[61]|0,u=n[69]|0,B=l[395]|0,l[395]=B+1<<16>>16,n[u+((B&65535)<<2)>>2]=o,f<<16>>16){case 39:{L(39);break}case 34:{L(34);break}default:{n[70]=(n[70]|0)+-2;break e}}switch(t=(n[70]|0)+2|0,n[70]=t,(E(1)|0)<<16>>16){case 44:{n[70]=(n[70]|0)+2,E(1)|0,B=n[61]|0,n[B+4>>2]=t,f=n[70]|0,n[B+16>>2]=f,g[B+24>>0]=1,n[70]=f+-2;break e}case 41:{l[396]=(l[396]|0)+-1<<16>>16,f=n[61]|0,n[f+4>>2]=t,n[f+12>>2]=(n[70]|0)+2,g[f+24>>0]=1,l[395]=(l[395]|0)+-1<<16>>16;break e}default:{n[70]=(n[70]|0)+-2;break e}}}break}case 46:{if(n[70]=(n[70]|0)+2,(E(1)|0)<<16>>16==109&&(o=n[70]|0,(J(o+2|0,44,6)|0)==0)){if(t=n[67]|0,!(ve(t)|0)&&(l[t>>1]|0)==46)break e;Qe(B,B,o+8|0,2)}break}case 42:case 39:case 34:{h=18;break}case 123:{if(t=n[70]|0,l[396]|0){n[70]=t+-2;break e}for(;!(t>>>0>=(n[71]|0)>>>0);){if(t=E(1)|0,P(t)|0)L(t);else if(t<<16>>16==125){h=33;break}t=(n[70]|0)+2|0,n[70]=t}if((h|0)==33&&(n[70]=(n[70]|0)+2),f=(E(1)|0)<<16>>16==102,t=n[70]|0,f&&J(t+2|0,50,6)|0){R();break e}if(n[70]=t+8,t=E(1)|0,P(t)|0){le(B,t);break e}else{R();break e}}default:(n[70]|0)==(t|0)?n[70]=B+10:h=18}while(0);do if((h|0)==18){if(l[396]|0){n[70]=(n[70]|0)+-2;break}for(t=n[71]|0,o=n[70]|0;;){if(o>>>0>=t>>>0){h=25;break}if(u=l[o>>1]|0,P(u)|0){h=23;break}f=o+2|0,n[70]=f,o=f}if((h|0)==23){le(B,u);break}else if((h|0)==25){R();break}}while(0)}function le(t,o){t=t|0,o=o|0;var u=0,h=0;switch(u=(n[70]|0)+2|0,o<<16>>16){case 39:{L(39),h=5;break}case 34:{L(34),h=5;break}default:R()}do if((h|0)==5){if(Qe(t,u,n[70]|0,1),n[70]=(n[70]|0)+2,o=E(0)|0,t=o<<16>>16==97,t?(u=n[70]|0,J(u+2|0,78,10)|0&&(h=11)):(u=n[70]|0,o<<16>>16==119&&(l[u+2>>1]|0)==105&&(l[u+4>>1]|0)==116&&(l[u+6>>1]|0)==104||(h=11)),(h|0)==11){n[70]=u+-2;break}if(n[70]=u+((t?6:4)<<1),(E(1)|0)<<16>>16!=123){n[70]=u;break}t=n[70]|0,o=t;e:for(;;){switch(n[70]=o+2,o=E(1)|0,o<<16>>16){case 39:{L(39),n[70]=(n[70]|0)+2,o=E(1)|0;break}case 34:{L(34),n[70]=(n[70]|0)+2,o=E(1)|0;break}default:o=F(o)|0}if(o<<16>>16!=58){h=20;break}switch(n[70]=(n[70]|0)+2,(E(1)|0)<<16>>16){case 39:{L(39);break}case 34:{L(34);break}default:{h=24;break e}}switch(n[70]=(n[70]|0)+2,(E(1)|0)<<16>>16){case 125:{h=29;break e}case 44:break;default:{h=28;break e}}if(n[70]=(n[70]|0)+2,(E(1)|0)<<16>>16==125){h=29;break}o=n[70]|0}if((h|0)==20){n[70]=u;break}else if((h|0)==24){n[70]=u;break}else if((h|0)==28){n[70]=u;break}else if((h|0)==29){h=n[61]|0,n[h+16>>2]=t,n[h+12>>2]=(n[70]|0)+2;break}}while(0)}function mt(t){t=t|0;e:do switch(l[t>>1]|0){case 100:switch(l[t+-2>>1]|0){case 105:{t=S(t+-4|0,88,2)|0;break e}case 108:{t=S(t+-4|0,92,3)|0;break e}default:{t=0;break e}}case 101:switch(l[t+-2>>1]|0){case 115:switch(l[t+-4>>1]|0){case 108:{t=H(t+-6|0,101)|0;break e}case 97:{t=H(t+-6|0,99)|0;break e}default:{t=0;break e}}case 116:{t=S(t+-4|0,98,4)|0;break e}case 117:{t=S(t+-4|0,106,6)|0;break e}default:{t=0;break e}}case 102:{if((l[t+-2>>1]|0)==111&&(l[t+-4>>1]|0)==101)switch(l[t+-6>>1]|0){case 99:{t=S(t+-8|0,118,6)|0;break e}case 112:{t=S(t+-8|0,130,2)|0;break e}default:{t=0;break e}}else t=0;break}case 107:{t=S(t+-2|0,134,4)|0;break}case 110:{t=t+-2|0,H(t,105)|0?t=1:t=S(t,142,5)|0;break}case 111:{t=H(t+-2|0,100)|0;break}case 114:{t=S(t+-2|0,152,7)|0;break}case 116:{t=S(t+-2|0,166,4)|0;break}case 119:switch(l[t+-2>>1]|0){case 101:{t=H(t+-4|0,110)|0;break e}case 111:{t=S(t+-4|0,174,3)|0;break e}default:{t=0;break e}}default:t=0}while(0);return t|0}function Je(){var t=0,o=0,u=0,h=0;o=n[71]|0,u=n[70]|0;e:for(;;){if(t=u+2|0,u>>>0>=o>>>0){o=10;break}switch(l[t>>1]|0){case 96:{o=7;break e}case 36:{if((l[u+4>>1]|0)==123){o=6;break e}break}case 92:{t=u+4|0;break}default:}u=t}(o|0)==6?(t=u+4|0,n[70]=t,o=n[68]|0,h=l[396]|0,u=h&65535,n[o+(u<<3)>>2]=4,l[396]=h+1<<16>>16,n[o+(u<<3)+4>>2]=t):(o|0)==7?(n[70]=t,u=n[68]|0,h=(l[396]|0)+-1<<16>>16,l[396]=h,(n[u+((h&65535)<<3)>>2]|0)!=3&&R()):(o|0)==10&&(n[70]=t,R())}function E(t){t=t|0;var o=0,u=0,h=0;u=n[70]|0;e:do{o=l[u>>1]|0;A:do if(o<<16>>16!=47)if(t){if(X(o)|0)break;break e}else{if(Ae(o)|0)break;break e}else switch(l[u+2>>1]|0){case 47:{he();break A}case 42:{fe(t);break A}default:{o=47;break e}}while(0);h=n[70]|0,u=h+2|0,n[70]=u}while(h>>>0<(n[71]|0)>>>0);return o|0}function L(t){t=t|0;var o=0,u=0,h=0,B=0;for(B=n[71]|0,o=n[70]|0;;){if(h=o+2|0,o>>>0>=B>>>0){o=9;break}if(u=l[h>>1]|0,u<<16>>16==t<<16>>16){o=10;break}if(u<<16>>16==92)u=o+4|0,(l[u>>1]|0)==13?(o=o+6|0,o=(l[o>>1]|0)==10?o:u):o=u;else if(Fe(u)|0){o=9;break}else o=h}(o|0)==9?(n[70]=h,R()):(o|0)==10&&(n[70]=h)}function Ne(t,o){t=t|0,o=o|0;var u=0,h=0,B=0,f=0;return u=n[70]|0,h=l[u>>1]|0,f=(t|0)==(o|0),B=f?0:t,f=f?0:o,h<<16>>16==97&&(n[70]=u+4,u=E(1)|0,t=n[70]|0,P(u)|0?(L(u),o=(n[70]|0)+2|0,n[70]=o):(F(u)|0,o=n[70]|0),h=E(1)|0,u=n[70]|0),(u|0)!=(t|0)&&Y(t,o,B,f),h|0}function Qe(t,o,u,h){t=t|0,o=o|0,u=u|0,h=h|0;var B=0,f=0;B=n[65]|0,n[65]=B+32,f=n[61]|0,n[(f|0?f+28|0:228)>>2]=B,n[62]=f,n[61]=B,n[B+8>>2]=t,(h|0)==2?t=u:t=(h|0)==1?u+2|0:0,n[B+12>>2]=t,n[B>>2]=o,n[B+4>>2]=u,n[B+16>>2]=0,n[B+20>>2]=h,g[B+24>>0]=(h|0)==1&1,n[B+28>>2]=0}function Dt(){var t=0,o=0,u=0;u=n[71]|0,o=n[70]|0;e:for(;;){if(t=o+2|0,o>>>0>=u>>>0){o=6;break}switch(l[t>>1]|0){case 13:case 10:{o=6;break e}case 93:{o=7;break e}case 92:{t=o+4|0;break}default:}o=t}return(o|0)==6?(n[70]=t,R(),t=0):(o|0)==7&&(n[70]=t,t=93),t|0}function Le(){var t=0,o=0,u=0;e:for(;;){if(t=n[70]|0,o=t+2|0,n[70]=o,t>>>0>=(n[71]|0)>>>0){u=7;break}switch(l[o>>1]|0){case 13:case 10:{u=7;break e}case 47:break e;case 91:{Dt()|0;break}case 92:{n[70]=t+4;break}default:}}(u|0)==7&&R()}function Kt(t){switch(t=t|0,l[t>>1]|0){case 62:{t=(l[t+-2>>1]|0)==61;break}case 41:case 59:{t=1;break}case 104:{t=S(t+-2|0,200,4)|0;break}case 121:{t=S(t+-2|0,208,6)|0;break}case 101:{t=S(t+-2|0,220,3)|0;break}default:t=0}return t|0}function fe(t){t=t|0;var o=0,u=0,h=0,B=0,f=0;for(B=(n[70]|0)+2|0,n[70]=B,u=n[71]|0;o=B+2|0,!(B>>>0>=u>>>0||(h=l[o>>1]|0,!t&&Fe(h)|0));){if(h<<16>>16==42&&(l[B+4>>1]|0)==47){f=8;break}B=o}(f|0)==8&&(n[70]=o,o=B+4|0),n[70]=o}function J(t,o,u){t=t|0,o=o|0,u=u|0;var h=0,B=0;e:do if(!u)t=0;else{for(;h=g[t>>0]|0,B=g[o>>0]|0,h<<24>>24==B<<24>>24;)if(u=u+-1|0,u)t=t+1|0,o=o+1|0;else{t=0;break e}t=(h&255)-(B&255)|0}while(0);return t|0}function te(t){t=t|0;e:do switch(t<<16>>16){case 38:case 37:case 33:{t=1;break}default:if((t&-8)<<16>>16==40|(t+-58&65535)<6)t=1;else{switch(t<<16>>16){case 91:case 93:case 94:{t=1;break e}default:}t=(t+-123&65535)<4}}while(0);return t|0}function yt(t){t=t|0;e:do switch(t<<16>>16){case 38:case 37:case 33:break;default:if(!((t+-58&65535)<6|(t+-40&65535)<7&t<<16>>16!=41)){switch(t<<16>>16){case 91:case 94:break e;default:}return t<<16>>16!=125&(t+-123&65535)<4|0}}while(0);return 1}function Re(t){t=t|0;var o=0;o=l[t>>1]|0;e:do if((o+-9&65535)>=5){switch(o<<16>>16){case 160:case 32:{o=1;break e}default:}if(te(o)|0)return o<<16>>16!=46|(ve(t)|0)|0;o=0}else o=1;while(0);return o|0}function St(t){t=t|0;var o=0,u=0,h=0,B=0;return u=k,k=k+16|0,h=u,n[h>>2]=0,n[64]=t,o=n[3]|0,B=o+(t<<1)|0,t=B+2|0,l[B>>1]=0,n[h>>2]=t,n[65]=t,n[57]=0,n[61]=0,n[59]=0,n[58]=0,n[63]=0,n[60]=0,k=u,o|0}function S(t,o,u){t=t|0,o=o|0,u=u|0;var h=0,B=0;return h=t+(0-u<<1)|0,B=h+2|0,t=n[3]|0,B>>>0>=t>>>0&&!(J(B,o,u<<1)|0)?(B|0)==(t|0)?t=1:t=Re(h)|0:t=0,t|0}function Y(t,o,u,h){t=t|0,o=o|0,u=u|0,h=h|0;var B=0,f=0;B=n[65]|0,n[65]=B+20,f=n[63]|0,n[(f|0?f+16|0:232)>>2]=B,n[63]=B,n[B>>2]=t,n[B+4>>2]=o,n[B+8>>2]=u,n[B+12>>2]=h,n[B+16>>2]=0}function Jt(t){switch(t=t|0,l[t>>1]|0){case 107:{t=S(t+-2|0,134,4)|0;break}case 101:{(l[t+-2>>1]|0)==117?t=S(t+-4|0,106,6)|0:t=0;break}default:t=0}return t|0}function H(t,o){t=t|0,o=o|0;var u=0;return u=n[3]|0,u>>>0<=t>>>0&&(l[t>>1]|0)==o<<16>>16?(u|0)==(t|0)?u=1:u=ge(l[t+-2>>1]|0)|0:u=0,u|0}function ge(t){t=t|0;e:do if((t+-9&65535)<5)t=1;else{switch(t<<16>>16){case 32:case 160:{t=1;break e}default:}t=t<<16>>16!=46&(te(t)|0)}while(0);return t|0}function he(){var t=0,o=0,u=0;t=n[71]|0,u=n[70]|0;e:for(;o=u+2|0,!(u>>>0>=t>>>0);)switch(l[o>>1]|0){case 13:case 10:break e;default:u=o}n[70]=o}function F(t){for(t=t|0;!(X(t)|0||te(t)|0);)if(t=(n[70]|0)+2|0,n[70]=t,t=l[t>>1]|0,!(t<<16>>16)){t=0;break}return t|0}function Nt(){var t=0;switch(t=n[(n[59]|0)+20>>2]|0,t|0){case 1:{t=-1;break}case 2:{t=-2;break}default:t=t-(n[3]|0)>>1}return t|0}function Lt(t){return t=t|0,!(S(t,180,5)|0)&&!(S(t,190,3)|0)?t=S(t,196,2)|0:t=1,t|0}function Ae(t){switch(t=t|0,t<<16>>16){case 160:case 32:case 12:case 11:case 9:{t=1;break}default:t=0}return t|0}function ve(t){return t=t|0,(l[t>>1]|0)==46&&(l[t+-2>>1]|0)==46?t=(l[t+-4>>1]|0)==46:t=0,t|0}function j(t){return t=t|0,(n[3]|0)==(t|0)?t=1:t=Re(t+-2|0)|0,t|0}function Rt(){var t=0;return t=n[(n[60]|0)+12>>2]|0,t?t=t-(n[3]|0)>>1:t=-1,t|0}function vt(){var t=0;return t=n[(n[59]|0)+12>>2]|0,t?t=t-(n[3]|0)>>1:t=-1,t|0}function Ft(){var t=0;return t=n[(n[60]|0)+8>>2]|0,t?t=t-(n[3]|0)>>1:t=-1,t|0}function xt(){var t=0;return t=n[(n[59]|0)+16>>2]|0,t?t=t-(n[3]|0)>>1:t=-1,t|0}function Ut(){var t=0;return t=n[(n[59]|0)+4>>2]|0,t?t=t-(n[3]|0)>>1:t=-1,t|0}function qt(){var t=0;return t=n[59]|0,t=n[(t|0?t+28|0:228)>>2]|0,n[59]=t,(t|0)!=0|0}function Mt(){var t=0;return t=n[60]|0,t=n[(t|0?t+16|0:232)>>2]|0,n[60]=t,(t|0)!=0|0}function R(){g[794]=1,n[66]=(n[70]|0)-(n[3]|0)>>1,n[70]=(n[71]|0)+2}function X(t){return t=t|0,(t|128)<<16>>16==160|(t+-9&65535)<5|0}function P(t){return t=t|0,t<<16>>16==39|t<<16>>16==34|0}function Gt(){return(n[(n[59]|0)+8>>2]|0)-(n[3]|0)>>1|0}function Yt(){return(n[(n[60]|0)+4>>2]|0)-(n[3]|0)>>1|0}function Fe(t){return t=t|0,t<<16>>16==13|t<<16>>16==10|0}function _t(){return(n[n[59]>>2]|0)-(n[3]|0)>>1|0}function Ot(){return(n[n[60]>>2]|0)-(n[3]|0)>>1|0}function Ht(){return p[(n[59]|0)+24>>0]|0|0}function jt(t){t=t|0,n[3]=t}function Xt(){return(g[795]|0)!=0|0}function Pt(){return n[66]|0}function Tt(t){return t=t|0,k=t+992+15&-16,992}return{su:Tt,ai:xt,e:Pt,ee:Yt,ele:Rt,els:Ft,es:Ot,f:Xt,id:Nt,ie:Ut,ip:Ht,is:_t,p:N,re:Mt,ri:qt,sa:St,se:vt,ses:jt,ss:Gt}}(typeof self<"u"?self:global,{},re),pe=D.su(V-(2<<17))}const r=b.length+1;D.ses(pe),D.sa(r-1),Te(b,new Uint16Array(re,pe,r)),D.p()||(d=D.e(),U());const s=[],a=[];for(;D.ri();){const c=D.is(),C=D.ie(),Q=D.ai(),g=D.id(),l=D.ss(),n=D.se();let p;D.ip()&&(p=be(g===-1?c:c+1,b.charCodeAt(g===-1?c-1:c))),s.push({n:p,s:c,e:C,ss:l,se:n,d:g,a:Q})}for(;D.re();){const c=D.es(),C=D.ee(),Q=D.els(),g=D.ele(),l=b.charCodeAt(c),n=Q>=0?b.charCodeAt(Q):-1;a.push({s:c,e:C,ls:Q,le:g,n:l===34||l===39?be(c+1,l):b.slice(c,C),ln:Q<0?void 0:n===34||n===39?be(Q+1,n):b.slice(Q,g)})}return[s,a,!!D.f()]}function be(i,e){d=i;let A="",r=d;for(;;){d>=b.length&&U();const s=b.charCodeAt(d);if(s===e)break;s===92?(A+=b.slice(r,d),A+=mA(),r=d):(s===8232||s===8233||Ze(s)&&U(),++d)}return A+=b.slice(r,d++),A}function mA(){let i=b.charCodeAt(++d);switch(++d,i){case 110:return`
`;case 114:return"\r";case 120:return String.fromCharCode(me(2));case 117:return function(){const e=b.charCodeAt(d);let A;return e===123?(++d,A=me(b.indexOf("}",d)-d),++d,A>1114111&&U()):A=me(4),A<=65535?String.fromCharCode(A):(A-=65536,String.fromCharCode(55296+(A>>10),56320+(1023&A)))}();case 116:return"	";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:b.charCodeAt(d)===10&&++d;case 10:return"";case 56:case 57:U();default:if(i>=48&&i<=55){let e=b.substr(d-1,3).match(/^[0-7]+/)[0],A=parseInt(e,8);return A>255&&(e=e.slice(0,-1),A=parseInt(e,8)),d+=e.length-1,i=b.charCodeAt(d),e==="0"&&i!==56&&i!==57||U(),String.fromCharCode(A)}return Ze(i)?"":String.fromCharCode(i)}}function me(i){const e=d;let A=0,r=0;for(let s=0;s<i;++s,++d){let a,c=b.charCodeAt(d);if(c!==95){if(c>=97)a=c-97+10;else if(c>=65)a=c-65+10;else{if(!(c>=48&&c<=57))break;a=c-48}if(a>=16)break;r=c,A=16*A+a}else r!==95&&s!==0||U(),r=c}return r!==95&&d-e===i||U(),A}function Ze(i){return i===13||i===10}function U(){throw Object.assign(Error(`Parse error ${$e}:${b.slice(0,d).split(`
`).length}:${d-b.lastIndexOf(`
`,d-1)}`),{idx:d})}let We=!1;Pe.then(()=>{We=!0});const DA=i=>We?Xe(i):bA(i),KA=`.then((mod)=>{
	const exports = Object.keys(mod);
	if(
		exports.length===1&&exports[0]==='default'&&mod.default&&mod.default.__esModule
	){
		return mod.default
	}
	return mod
})`.replace(/[\n\t]+/g,"");function De(i,e){if(!e.includes("import"))return;const A=DA(e)[0].filter(s=>s.d>-1);if(A.length===0)return;const r=new Se(e);for(const s of A)r.appendRight(s.se,KA);return{code:r.toString(),map:r.generateMap({source:i,hires:!0})}}function yA(i){try{const e=_.default.readFileSync(i,"utf8");return JSON.parse(e)}catch{}}const Ve=()=>Math.floor(Date.now()/1e8);class SA extends Map{cacheDirectory=G.default.join(nA.default.tmpdir(),"esbuild-kit");cacheFiles;constructor(){super(),_.default.mkdirSync(this.cacheDirectory,{recursive:!0}),this.cacheFiles=_.default.readdirSync(this.cacheDirectory).map(e=>{const[A,r]=e.split("-");return{time:Number(A),key:r,fileName:e}}),setImmediate(()=>this.expireDiskCache())}get(e){const A=super.get(e);if(A)return A;const r=this.cacheFiles.find(c=>c.key===e);if(!r)return;const s=G.default.join(this.cacheDirectory,r.fileName),a=yA(s);if(!a){_.default.promises.unlink(s).then(()=>{const c=this.cacheFiles.indexOf(r);this.cacheFiles.splice(c,1)},()=>{});return}return super.set(e,a),a}set(e,A){if(super.set(e,A),A){const r=Ve();_.default.promises.writeFile(G.default.join(this.cacheDirectory,`${r}-${e}`),JSON.stringify(A)).catch(()=>{})}return this}expireDiskCache(){const e=Ve();for(const A of this.cacheFiles)e-A.time>7&&_.default.promises.unlink(G.default.join(this.cacheDirectory,A.fileName)).catch(()=>{})}}var ie=process.env.ESBK_DISABLE_CACHE?new Map:new SA;const JA=/^[\w+.-]+:\/\//,NA=/^([\w+.-]+:)\/\/([^@/#?]*@)?([^:/#?]*)(:\d+)?(\/[^#?]*)?(\?[^#]*)?(#.*)?/,LA=/^file:(?:\/\/((?![a-z]:)[^/#?]*)?)?(\/?[^#?]*)(\?[^#]*)?(#.*)?/i;var K;(function(i){i[i.Empty=1]="Empty",i[i.Hash=2]="Hash",i[i.Query=3]="Query",i[i.RelativePath=4]="RelativePath",i[i.AbsolutePath=5]="AbsolutePath",i[i.SchemeRelative=6]="SchemeRelative",i[i.Absolute=7]="Absolute"})(K||(K={}));function RA(i){return JA.test(i)}function vA(i){return i.startsWith("//")}function ze(i){return i.startsWith("/")}function FA(i){return i.startsWith("file:")}function et(i){return/^[.?#]/.test(i)}function se(i){const e=NA.exec(i);return tt(e[1],e[2]||"",e[3],e[4]||"",e[5]||"/",e[6]||"",e[7]||"")}function xA(i){const e=LA.exec(i),A=e[2];return tt("file:","",e[1]||"","",ze(A)?A:"/"+A,e[3]||"",e[4]||"")}function tt(i,e,A,r,s,a,c){return{scheme:i,user:e,host:A,port:r,path:s,query:a,hash:c,type:K.Absolute}}function At(i){if(vA(i)){const A=se("http:"+i);return A.scheme="",A.type=K.SchemeRelative,A}if(ze(i)){const A=se("http://foo.com"+i);return A.scheme="",A.host="",A.type=K.AbsolutePath,A}if(FA(i))return xA(i);if(RA(i))return se(i);const e=se("http://foo.com/"+i);return e.scheme="",e.host="",e.type=i?i.startsWith("?")?K.Query:i.startsWith("#")?K.Hash:K.RelativePath:K.Empty,e}function UA(i){if(i.endsWith("/.."))return i;const e=i.lastIndexOf("/");return i.slice(0,e+1)}function qA(i,e){nt(e,e.type),i.path==="/"?i.path=e.path:i.path=UA(e.path)+i.path}function nt(i,e){const A=e<=K.RelativePath,r=i.path.split("/");let s=1,a=0,c=!1;for(let Q=1;Q<r.length;Q++){const g=r[Q];if(!g){c=!0;continue}if(c=!1,g!=="."){if(g===".."){a?(c=!0,a--,s--):A&&(r[s++]=g);continue}r[s++]=g,a++}}let C="";for(let Q=1;Q<s;Q++)C+="/"+r[Q];(!C||c&&!C.endsWith("/.."))&&(C+="/"),i.path=C}function MA(i,e){if(!i&&!e)return"";const A=At(i);let r=A.type;if(e&&r!==K.Absolute){const a=At(e),c=a.type;switch(r){case K.Empty:A.hash=a.hash;case K.Hash:A.query=a.query;case K.Query:case K.RelativePath:qA(A,a);case K.AbsolutePath:A.user=a.user,A.host=a.host,A.port=a.port;case K.SchemeRelative:A.scheme=a.scheme}c>r&&(r=c)}nt(A,r);const s=A.query+A.hash;switch(r){case K.Hash:case K.Query:return s;case K.RelativePath:{const a=A.path.slice(1);return a?et(e||i)&&!et(a)?"./"+a+s:a+s:s||"."}case K.AbsolutePath:return A.path+s;default:return A.scheme+"//"+A.user+A.host+A.port+A.path+s}}function rt(i,e){return e&&!e.endsWith("/")&&(e+="/"),MA(i,e)}function GA(i){if(!i)return"";const e=i.lastIndexOf("/");return i.slice(0,e+1)}const q=0;function YA(i,e){const A=it(i,0);if(A===i.length)return i;e||(i=i.slice());for(let r=A;r<i.length;r=it(i,r+1))i[r]=OA(i[r],e);return i}function it(i,e){for(let A=e;A<i.length;A++)if(!_A(i[A]))return A;return i.length}function _A(i){for(let e=1;e<i.length;e++)if(i[e][q]<i[e-1][q])return!1;return!0}function OA(i,e){return e||(i=i.slice()),i.sort(HA)}function HA(i,e){return i[q]-e[q]}let oe=!1;function jA(i,e,A,r){for(;A<=r;){const s=A+(r-A>>1),a=i[s][q]-e;if(a===0)return oe=!0,s;a<0?A=s+1:r=s-1}return oe=!1,A-1}function XA(i,e,A){for(let r=A+1;r<i.length&&i[r][q]===e;A=r++);return A}function PA(i,e,A){for(let r=A-1;r>=0&&i[r][q]===e;A=r--);return A}function TA(){return{lastKey:-1,lastNeedle:-1,lastIndex:-1}}function $A(i,e,A,r){const{lastKey:s,lastNeedle:a,lastIndex:c}=A;let C=0,Q=i.length-1;if(r===s){if(e===a)return oe=c!==-1&&i[c][q]===e,c;e>=a?C=c===-1?0:c:Q=c}return A.lastKey=r,A.lastNeedle=e,A.lastIndex=jA(i,e,C,Q)}const st=-1,ZA=1;let Ke,ot;class at{constructor(e,A){const r=typeof e=="string";if(!r&&e._decodedMemo)return e;const s=r?JSON.parse(e):e,{version:a,file:c,names:C,sourceRoot:Q,sources:g,sourcesContent:l}=s;this.version=a,this.file=c,this.names=C,this.sourceRoot=Q,this.sources=g,this.sourcesContent=l;const n=rt(Q||"",GA(A));this.resolvedSources=g.map(y=>rt(y||"",n));const{mappings:p}=s;typeof p=="string"?(this._encoded=p,this._decoded=void 0):(this._encoded=void 0,this._decoded=YA(p,r)),this._decodedMemo=TA(),this._bySources=void 0,this._bySourceMemos=void 0}}Ke=i=>i._decoded||(i._decoded=oA(i._encoded)),ot=(i,e,A)=>{const r=Ke(i);if(e>=r.length)return null;const s=r[e],a=WA(s,i._decodedMemo,e,A,ZA);return a===-1?null:s[a]};function WA(i,e,A,r,s){let a=$A(i,r,e,A);return oe?a=(s===st?XA:PA)(i,r,a):s===st&&a++,a===-1||a===i.length?-1:a}let ct,ae;class ut{constructor(){this._indexes={__proto__:null},this.array=[]}}ct=(i,e)=>i._indexes[e],ae=(i,e)=>{const A=ct(i,e);if(A!==void 0)return A;const{array:r,_indexes:s}=i;return s[e]=r.push(e)-1};const VA=0,zA=1,en=2,tn=3,An=4,lt=-1;let Qt,ft,ye,gt,ht;class nn{constructor({file:e,sourceRoot:A}={}){this._names=new ut,this._sources=new ut,this._sourcesContent=[],this._mappings=[],this.file=e,this.sourceRoot=A}}Qt=(i,e,A,r,s,a,c,C)=>ht(!0,i,e,A,r,s,a,c,C),ft=(i,e,A)=>{const{_sources:r,_sourcesContent:s}=i;s[ae(r,e)]=A},ye=i=>{const{file:e,sourceRoot:A,_mappings:r,_sources:s,_sourcesContent:a,_names:c}=i;return on(r),{version:3,file:e||void 0,names:c.array,sourceRoot:A||void 0,sources:s.array,sourcesContent:a,mappings:r}},gt=i=>{const e=ye(i);return Object.assign(Object.assign({},e),{mappings:He(e.mappings)})},ht=(i,e,A,r,s,a,c,C,Q)=>{const{_mappings:g,_sources:l,_sourcesContent:n,_names:p}=e,y=rn(g,A),k=sn(y,r);if(!s)return i&&an(y,k)?void 0:Ct(y,k,[r]);const N=ae(l,s),v=C?ae(p,C):lt;if(N===n.length&&(n[N]=Q!=null?Q:null),!(i&&cn(y,k,N,a,c,v)))return Ct(y,k,C?[r,N,a,c,v]:[r,N,a,c])};function rn(i,e){for(let A=i.length;A<=e;A++)i[A]=[];return i[e]}function sn(i,e){let A=i.length;for(let r=A-1;r>=0;A=r--){const s=i[r];if(e>=s[VA])break}return A}function Ct(i,e,A){for(let r=i.length;r>e;r--)i[r]=i[r-1];i[e]=A}function on(i){const{length:e}=i;let A=e;for(let r=A-1;r>=0&&!(i[r].length>0);A=r,r--);A<e&&(i.length=A)}function an(i,e){return e===0?!0:i[e-1].length===1}function cn(i,e,A,r,s,a){if(e===0)return!1;const c=i[e-1];return c.length===1?!1:A===c[zA]&&r===c[en]&&s===c[tn]&&a===(c.length===5?c[An]:lt)}const Bt=Et("",-1,-1,"",null),un=[];function Et(i,e,A,r,s){return{source:i,line:e,column:A,name:r,content:s}}function dt(i,e,A,r){return{map:i,sources:e,source:A,content:r}}function wt(i,e){return dt(i,e,"",null)}function ln(i,e){return dt(null,un,i,e)}function Qn(i){const e=new nn({file:i.map.file}),{sources:A,map:r}=i,s=r.names,a=Ke(r);for(let c=0;c<a.length;c++){const C=a[c];for(let Q=0;Q<C.length;Q++){const g=C[Q],l=g[0];let n=Bt;if(g.length!==1){const ee=A[g[1]];if(n=kt(ee,g[2],g[3],g.length===5?s[g[4]]:""),n==null)continue}const{column:p,line:y,name:k,content:N,source:v}=n;Qt(e,c,l,v,y,p,k),v&&N!=null&&ft(e,v,N)}}return e}function kt(i,e,A,r){if(!i.map)return Et(i.source,e,A,r,i.content);const s=ot(i.map,e,A);return s==null?null:s.length===1?Bt:kt(i.sources[s[1]],s[2],s[3],s.length===5?i.map.names[s[4]]:r)}function fn(i){return Array.isArray(i)?i:[i]}function gn(i,e){const A=fn(i).map(a=>new at(a,"")),r=A.pop();for(let a=0;a<A.length;a++)if(A[a].sources.length>1)throw new Error(`Transformation map ${a} must have exactly one source file.
Did you specify these with the most recent transformation maps first?`);let s=It(r,e,"",0);for(let a=A.length-1;a>=0;a--)s=wt(A[a],[s]);return s}function It(i,e,A,r){const{resolvedSources:s,sourcesContent:a}=i,c=r+1,C=s.map((Q,g)=>{const l={importer:A,depth:c,source:Q||"",content:void 0},n=e(l.source,l),{source:p,content:y}=l;if(n)return It(new at(n,p),e,p,c);const k=y!==void 0?y:a?a[g]:null;return ln(p,k)});return wt(i,C)}class hn{constructor(e,A){const r=A.decodedMappings?ye(e):gt(e);this.version=r.version,this.file=r.file,this.mappings=r.mappings,this.names=r.names,this.sourceRoot=r.sourceRoot,this.sources=r.sources,A.excludeContent||(this.sourcesContent=r.sourcesContent)}toString(){return JSON.stringify(this)}}function pt(i,e,A){const r=typeof A=="object"?A:{excludeContent:!!A,decodedMappings:!1},s=gn(i,e);return new hn(Qn(s),r)}function Cn(i,e,A){const r=[],s=[],a={code:e};for(const c of A){const C=c(i,a.code);C&&(Object.assign(a,C),r.unshift(C.map),C.warnings&&s.push(...C.warnings))}return{...a,map:pt(r,()=>null),warnings:s}}async function Bn(i,e,A){const r=[],s=[],a={code:e};for(const c of A){const C=await c(i,a.code);C&&(Object.assign(a,C),r.unshift(C.map),C.warnings&&s.push(...C.warnings))}return{...a,map:pt(r,()=>null),warnings:s}}const En=process.versions.node,bt=i=>{const e={target:`node${En}`,loader:"default",sourcemap:!0,minifyWhitespace:!0,keepNames:!0,...i};if(e.sourcefile){const{sourcefile:A}=e,r=G.default.extname(A);r?(r===".cts"||r===".mts")&&(e.sourcefile=`${A.slice(0,-3)}ts`):e.sourcefile+=".js"}return e};function dn(i,e,A){const r={};e.endsWith(".cjs")||e.endsWith(".cts")||(r["import.meta.url"]=`'${Zt.pathToFileURL(e)}'`);const s=bt({format:"cjs",sourcefile:e,define:r,banner:"(()=>{",footer:"})()",...A}),a=qe(i+JSON.stringify(s)+ne.version);let c=ie.get(a);if(c||(c=Cn(e,i,[(C,Q)=>{const g=ne.transformSync(Q,s);return s.sourcefile!==C&&(g.map=g.map.replace(JSON.stringify(s.sourcefile),JSON.stringify(C))),g},De]),ie.set(a,c)),c.warnings&&c.warnings.length>0){const{warnings:C}=c;for(const Q of C)console.log(Q)}return c}async function wn(i,e,A){const r=bt({format:"esm",sourcefile:e,...A}),s=qe(i+JSON.stringify(r)+ne.version);let a=ie.get(s);if(a||(a=await Bn(e,i,[async(c,C)=>{const Q=await ne.transform(C,r);return r.sourcefile!==c&&(Q.map=Q.map.replace(JSON.stringify(r.sourcefile),JSON.stringify(c))),Q},De]),ie.set(s,a)),a.warnings&&a.warnings.length>0){const{warnings:c}=a;for(const C of c)console.log(C)}return a}const ce=Object.create(null);ce[".js"]=".ts",ce[".cjs"]=".cts",ce[".mjs"]=".mts";const kn=i=>{const e=G.default.extname(i),[A,r]=G.default.extname(i).split("?"),s=ce[A];if(s)return i.slice(0,-e.length)+s+(r?`?${r}`:"")};exports.compareNodeVersion=xe,exports.installSourceMapSupport=iA,exports.resolveTsPath=kn,exports.transform=wn,exports.transformDynamicImport=De,exports.transformSync=dn;
