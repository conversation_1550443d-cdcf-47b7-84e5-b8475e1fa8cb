{"name": "esniff", "version": "2.0.1", "description": "Low footprint ECMAScript source code parser", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.medikoo.com/)", "keywords": ["sniff", "analyze", "ast", "parse", "syntax", "sniffer", "detective", "detect", "find", "search", "source", "code"], "repository": "medikoo/esniff", "dependencies": {"d": "^1.0.1", "es5-ext": "^0.10.62", "event-emitter": "^0.3.5", "type": "^2.7.2"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-medikoo": "^4.2.0", "esprima": "^4.0.1", "github-release-from-cc-changelog": "^2.3.0", "nyc": "^15.1.0", "prettier-elastic": "^2.7.1", "tad": "^3.1.1"}, "eslintConfig": {"extends": "medikoo/es5", "root": true, "overrides": [{"files": ["index.js"], "rules": {"max-depth": "off"}}, {"files": ["index.js", "utils/is-variable-name.js"], "rules": {"max-lines": "off"}}, {"files": ["utils/is-variable-name.js"], "rules": {"no-misleading-character-class": "off"}}, {"files": ["test/**"], "env": {"node": true}}]}, "eslintIgnore": ["/coverage", "/test/__playground"], "prettier": {"printWidth": 100, "tabWidth": 4, "overrides": [{"files": ["*.md", "*.yml"], "options": {"tabWidth": 2}}]}, "nyc": {"all": true, "exclude": [".github", "coverage/**", "test/**", "*.config.js"], "reporter": ["lcov", "html", "text-summary"]}, "scripts": {"coverage": "nyc npm test", "lint": "eslint .", "lint:updated": "pipe-git-updated --base=main --ext=js -- eslint --ignore-pattern '!*'", "prettier-check": "prettier -c \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettier-check:updated": "pipe-git-updated --base=main --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettify:updated": "pipe-git-updated ---base=main -ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier --write", "test": "node ./node_modules/tad/bin/tad"}, "engines": {"node": ">=0.10"}, "license": "ISC"}